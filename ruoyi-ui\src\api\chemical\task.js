import request from '@/utils/request'

// 启动数据读取任务
export function startTask() {
  return request({
    url: '/chemical/task/start',
    method: 'post'
  })
}

// 暂停数据读取任务
export function pauseTask() {
  return request({
    url: '/chemical/task/pause',
    method: 'post'
  })
}

// 停止数据读取任务
export function stopTask() {
  return request({
    url: '/chemical/task/stop',
    method: 'post'
  })
}

// 重启数据读取任务
export function restartTask() {
  return request({
    url: '/chemical/task/restart',
    method: 'post'
  })
}

// 获取任务状态
export function getTaskStatus() {
  return request({
    url: '/chemical/task/status',
    method: 'get'
  })
}

// 获取任务监控信息
export function getTaskMonitor() {
  return request({
    url: '/chemical/task/monitor',
    method: 'get'
  })
}

// 启动数据处理任务
export function startDataProcessing() {
  return request({
    url: '/chemical/task/startProcessing',
    method: 'post'
  })
}

// 启动日志处理任务
export function startLogProcessing() {
  return request({
    url: '/chemical/task/startLogProcessing',
    method: 'post'
  })
}

// 获取实时日志信息
export function getRealtimeLogs(lastLogId) {
  return request({
    url: '/chemical/task/logs',
    method: 'get',
    params: { lastLogId }
  })
}

// 清空日志信息
export function clearLogs() {
  return request({
    url: '/chemical/task/clearLogs',
    method: 'post'
  })
}

// 获取系统运行状态
export function getSystemStatus() {
  return request({
    url: '/chemical/task/systemStatus',
    method: 'get'
  })
}

// 初始化Pulsar客户端
export function initPulsarClient() {
  return request({
    url: '/chemical/task/initPulsar',
    method: 'post'
  })
}

// 关闭Pulsar客户端
export function closePulsarClient() {
  return request({
    url: '/chemical/task/closePulsar',
    method: 'post'
  })
}

// 检查Pulsar连接状态
export function checkPulsarStatus() {
  return request({
    url: '/chemical/task/pulsarStatus',
    method: 'get'
  })
}
