<template>
  <div class="app-container">
    <!-- 系统概览卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <i class="el-icon-data-line"></i>
            <span>总数据量</span>
          </div>
          <div class="card-content">
            <div class="number">{{ overview.totalCount || 0 }}</div>
            <div class="desc">条记录</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <i class="el-icon-loading"></i>
            <span>未处理数据</span>
          </div>
          <div class="card-content">
            <div class="number text-warning">{{ overview.unprocessedCount || 0 }}</div>
            <div class="desc">条记录</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <i class="el-icon-check"></i>
            <span>已导出数据</span>
          </div>
          <div class="card-content">
            <div class="number text-success">{{ overview.exportedCount || 0 }}</div>
            <div class="desc">条记录</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-header">
            <i class="el-icon-plus"></i>
            <span>今日新增</span>
          </div>
          <div class="card-content">
            <div class="number text-info">{{ overview.todayNewCount || 0 }}</div>
            <div class="desc">条记录</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务状态和系统状态 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>任务状态</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshTaskStatus">刷新</el-button>
          </div>
          <div class="task-status">
            <div class="status-item">
              <span class="label">数据读取任务:</span>
              <el-tag :type="getTaskStatusType(taskStatus.dataReading)">
                {{ getTaskStatusText(taskStatus.dataReading) }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="label">数据处理任务:</span>
              <el-tag :type="getTaskStatusType(taskStatus.dataProcessing)">
                {{ getTaskStatusText(taskStatus.dataProcessing) }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="label">日志处理任务:</span>
              <el-tag :type="getTaskStatusType(taskStatus.logProcessing)">
                {{ getTaskStatusText(taskStatus.logProcessing) }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="label">Pulsar连接:</span>
              <el-tag :type="pulsarStatus.connected ? 'success' : 'danger'">
                {{ pulsarStatus.connected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header" class="clearfix">
            <span>系统性能</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshPerformance">刷新</el-button>
          </div>
          <div class="performance-metrics">
            <div class="metric-item">
              <span class="label">内存使用率:</span>
              <el-progress :percentage="performance.memoryUsage" :color="getProgressColor(performance.memoryUsage)"></el-progress>
            </div>
            <div class="metric-item">
              <span class="label">CPU使用率:</span>
              <el-progress :percentage="performance.cpuUsage" :color="getProgressColor(performance.cpuUsage)"></el-progress>
            </div>
            <div class="metric-item">
              <span class="label">处理成功率:</span>
              <el-progress :percentage="overview.processRate" :color="getProgressColor(overview.processRate, true)"></el-progress>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时日志 -->
    <el-row>
      <el-col :span="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>实时日志</span>
            <div style="float: right;">
              <el-button type="text" @click="clearLogs">清空日志</el-button>
              <el-button type="text" @click="toggleAutoScroll">
                {{ autoScroll ? '停止滚动' : '自动滚动' }}
              </el-button>
            </div>
          </div>
          <div class="log-container" ref="logContainer">
            <div v-for="(log, index) in logs" :key="index" class="log-item" :class="getLogClass(log.level)">
              <span class="log-time">{{ log.timestamp }}</span>
              <span class="log-level">{{ log.level }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            <div v-if="logs.length === 0" class="no-logs">暂无日志信息</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getSystemOverview, getRealtimeData, getPerformanceMetrics, getRealtimeLogs, clearLogs } from "@/api/chemical/monitor";

export default {
  name: "ChemicalMonitor",
  data() {
    return {
      // 概览数据
      overview: {
        totalCount: 0,
        unprocessedCount: 0,
        exportedCount: 0,
        todayNewCount: 0,
        processRate: 0,
        exportRate: 0
      },
      // 任务状态
      taskStatus: {
        dataReading: 'STOPPED',
        dataProcessing: 'STOPPED',
        logProcessing: 'STOPPED'
      },
      // Pulsar状态
      pulsarStatus: {
        connected: false
      },
      // 性能指标
      performance: {
        memoryUsage: 0,
        cpuUsage: 0
      },
      // 日志数据
      logs: [],
      lastLogId: 0,
      autoScroll: true,
      // 定时器
      refreshTimer: null,
      logTimer: null
    };
  },
  created() {
    this.loadData();
    this.startAutoRefresh();
  },
  beforeDestroy() {
    this.stopAutoRefresh();
  },
  methods: {
    // 加载数据
    async loadData() {
      try {
        const response = await getSystemOverview();
        if (response.code === 200) {
          const data = response.data;
          this.overview = data.dataStatistics || {};
          this.taskStatus = data.taskStatus || {};
          this.pulsarStatus = data.pulsarStatus || {};
        }
      } catch (error) {
        console.error('加载数据失败:', error);
      }
    },
    
    // 刷新任务状态
    async refreshTaskStatus() {
      // 实现任务状态刷新逻辑
    },
    
    // 刷新性能指标
    async refreshPerformance() {
      try {
        const response = await getPerformanceMetrics();
        if (response.code === 200) {
          this.performance = response.data;
        }
      } catch (error) {
        console.error('刷新性能指标失败:', error);
      }
    },
    
    // 加载实时日志
    async loadRealtimeLogs() {
      try {
        const response = await getRealtimeLogs(this.lastLogId);
        if (response.code === 200) {
          const newLogs = response.data.logs || [];
          this.logs.push(...newLogs);
          
          // 限制日志数量
          if (this.logs.length > 1000) {
            this.logs = this.logs.slice(-500);
          }
          
          if (newLogs.length > 0) {
            this.lastLogId = newLogs[newLogs.length - 1].id;
            this.$nextTick(() => {
              if (this.autoScroll) {
                this.scrollToBottom();
              }
            });
          }
        }
      } catch (error) {
        console.error('加载实时日志失败:', error);
      }
    },
    
    // 清空日志
    async clearLogs() {
      try {
        await clearLogs();
        this.logs = [];
        this.lastLogId = 0;
        this.$message.success('日志已清空');
      } catch (error) {
        this.$message.error('清空日志失败');
      }
    },
    
    // 切换自动滚动
    toggleAutoScroll() {
      this.autoScroll = !this.autoScroll;
      if (this.autoScroll) {
        this.scrollToBottom();
      }
    },
    
    // 滚动到底部
    scrollToBottom() {
      const container = this.$refs.logContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    
    // 开始自动刷新
    startAutoRefresh() {
      // 每30秒刷新一次概览数据
      this.refreshTimer = setInterval(() => {
        this.loadData();
      }, 30000);
      
      // 每5秒刷新一次日志
      this.logTimer = setInterval(() => {
        this.loadRealtimeLogs();
      }, 5000);
    },
    
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
      }
      if (this.logTimer) {
        clearInterval(this.logTimer);
      }
    },
    
    // 获取任务状态类型
    getTaskStatusType(status) {
      const statusMap = {
        'RUNNING': 'success',
        'PAUSED': 'warning',
        'STOPPED': 'info',
        'ERROR': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取任务状态文本
    getTaskStatusText(status) {
      const statusMap = {
        'RUNNING': '运行中',
        'PAUSED': '已暂停',
        'STOPPED': '已停止',
        'ERROR': '错误'
      };
      return statusMap[status] || '未知';
    },
    
    // 获取进度条颜色
    getProgressColor(percentage, reverse = false) {
      if (reverse) {
        // 对于成功率等指标，绿色表示好
        if (percentage >= 80) return '#67c23a';
        if (percentage >= 60) return '#e6a23c';
        return '#f56c6c';
      } else {
        // 对于使用率等指标，红色表示高
        if (percentage >= 80) return '#f56c6c';
        if (percentage >= 60) return '#e6a23c';
        return '#67c23a';
      }
    },
    
    // 获取日志样式类
    getLogClass(level) {
      const levelMap = {
        'ERROR': 'log-error',
        'WARN': 'log-warning',
        'INFO': 'log-info',
        'DEBUG': 'log-debug'
      };
      return levelMap[level] || 'log-info';
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.overview-card {
  text-align: center;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.card-header i {
  margin-right: 5px;
  font-size: 18px;
}

.card-content .number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-content .desc {
  color: #999;
  font-size: 14px;
}

.text-warning {
  color: #e6a23c;
}

.text-success {
  color: #67c23a;
}

.text-info {
  color: #409eff;
}

.task-status .status-item,
.performance-metrics .metric-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.task-status .label,
.performance-metrics .label {
  width: 120px;
  font-weight: bold;
}

.performance-metrics .el-progress {
  flex: 1;
  margin-left: 10px;
}

.log-container {
  height: 400px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  padding: 5px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-level {
  font-weight: bold;
  margin-right: 10px;
  width: 50px;
  display: inline-block;
}

.log-message {
  word-break: break-all;
}

.log-error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.log-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.log-info {
  background-color: #f0f9ff;
  color: #409eff;
}

.log-debug {
  background-color: #f0f0f0;
  color: #909399;
}

.no-logs {
  text-align: center;
  color: #999;
  padding: 50px 0;
}
</style>
