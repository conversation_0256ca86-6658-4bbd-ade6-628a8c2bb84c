-- 化学审计系统菜单配置SQL
-- 包含完整的菜单结构和权限配置

-- 1. 主菜单：化学审计系统
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('化学审计系统', 0, 6, 'chemical', NULL, 1, 0, 'M', '0', '0', '', 'tool', 'admin', GETDATE(), '', NULL, '化学数据审计管理系统');

-- 获取刚插入的主菜单ID（假设为2000）
DECLARE @MAIN_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '化学审计系统' AND parent_id = 0);

-- 2. 数据管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('数据管理', @MAIN_MENU_ID, 1, 'data', NULL, 1, 0, 'M', '0', '0', '', 'table', 'admin', GETDATE(), '', NULL, '化学数据管理');

DECLARE @DATA_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '数据管理' AND parent_id = @MAIN_MENU_ID);

-- 2.1 化学数据管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('化学数据', @DATA_MENU_ID, 1, 'chemical', 'audit/chemical/index', 1, 0, 'C', '0', '0', 'audit:chemical:list', 'documentation', 'admin', GETDATE(), '', NULL, '化学数据管理');

DECLARE @CHEMICAL_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '化学数据' AND parent_id = @DATA_MENU_ID);

-- 化学数据管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('化学数据查询', @CHEMICAL_MENU_ID, 1, '', '', 1, 0, 'F', '0', '0', 'audit:chemical:query', '#', 'admin', GETDATE(), '', NULL, ''),
('化学数据新增', @CHEMICAL_MENU_ID, 2, '', '', 1, 0, 'F', '0', '0', 'audit:chemical:add', '#', 'admin', GETDATE(), '', NULL, ''),
('化学数据修改', @CHEMICAL_MENU_ID, 3, '', '', 1, 0, 'F', '0', '0', 'audit:chemical:edit', '#', 'admin', GETDATE(), '', NULL, ''),
('化学数据删除', @CHEMICAL_MENU_ID, 4, '', '', 1, 0, 'F', '0', '0', 'audit:chemical:remove', '#', 'admin', GETDATE(), '', NULL, ''),
('化学数据导出', @CHEMICAL_MENU_ID, 5, '', '', 1, 0, 'F', '0', '0', 'audit:chemical:export', '#', 'admin', GETDATE(), '', NULL, ''),
('化学数据重新处理', @CHEMICAL_MENU_ID, 6, '', '', 1, 0, 'F', '0', '0', 'audit:chemical:reprocess', '#', 'admin', GETDATE(), '', NULL, '');

-- 2.2 应审数据管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('应审数据', @DATA_MENU_ID, 2, 'chemicalYs', 'audit/chemicalYs/index', 1, 0, 'C', '0', '0', 'audit:chemicalYs:list', 'edit', 'admin', GETDATE(), '', NULL, '化学应审数据管理');

DECLARE @CHEMICAL_YS_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '应审数据' AND parent_id = @DATA_MENU_ID);

-- 应审数据管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('应审数据查询', @CHEMICAL_YS_MENU_ID, 1, '', '', 1, 0, 'F', '0', '0', 'audit:chemicalYs:query', '#', 'admin', GETDATE(), '', NULL, ''),
('应审数据新增', @CHEMICAL_YS_MENU_ID, 2, '', '', 1, 0, 'F', '0', '0', 'audit:chemicalYs:add', '#', 'admin', GETDATE(), '', NULL, ''),
('应审数据修改', @CHEMICAL_YS_MENU_ID, 3, '', '', 1, 0, 'F', '0', '0', 'audit:chemicalYs:edit', '#', 'admin', GETDATE(), '', NULL, ''),
('应审数据删除', @CHEMICAL_YS_MENU_ID, 4, '', '', 1, 0, 'F', '0', '0', 'audit:chemicalYs:remove', '#', 'admin', GETDATE(), '', NULL, ''),
('应审数据导出', @CHEMICAL_YS_MENU_ID, 5, '', '', 1, 0, 'F', '0', '0', 'audit:chemicalYs:export', '#', 'admin', GETDATE(), '', NULL, '');

-- 3. 任务管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('任务管理', @MAIN_MENU_ID, 2, 'task', NULL, 1, 0, 'M', '0', '0', '', 'system', 'admin', GETDATE(), '', NULL, '任务管理');

DECLARE @TASK_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '任务管理' AND parent_id = @MAIN_MENU_ID);

-- 3.1 任务控制
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('任务控制', @TASK_MENU_ID, 1, 'control', 'audit/task/control', 1, 0, 'C', '0', '0', 'audit:task:control', 'job', 'admin', GETDATE(), '', NULL, '任务启动停止控制');

DECLARE @TASK_CONTROL_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '任务控制' AND parent_id = @TASK_MENU_ID);

-- 任务控制权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('任务启动', @TASK_CONTROL_MENU_ID, 1, '', '', 1, 0, 'F', '0', '0', 'audit:task:start', '#', 'admin', GETDATE(), '', NULL, ''),
('任务停止', @TASK_CONTROL_MENU_ID, 2, '', '', 1, 0, 'F', '0', '0', 'audit:task:stop', '#', 'admin', GETDATE(), '', NULL, ''),
('任务暂停', @TASK_CONTROL_MENU_ID, 3, '', '', 1, 0, 'F', '0', '0', 'audit:task:pause', '#', 'admin', GETDATE(), '', NULL, ''),
('任务重启', @TASK_CONTROL_MENU_ID, 4, '', '', 1, 0, 'F', '0', '0', 'audit:task:restart', '#', 'admin', GETDATE(), '', NULL, ''),
('统一数据处理', @TASK_CONTROL_MENU_ID, 5, '', '', 1, 0, 'F', '0', '0', 'audit:task:unified', '#', 'admin', GETDATE(), '', NULL, '');

-- 3.2 任务监控
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('任务监控', @TASK_MENU_ID, 2, 'monitor', 'audit/task/monitor', 1, 0, 'C', '0', '0', 'audit:task:monitor', 'monitor', 'admin', GETDATE(), '', NULL, '任务执行监控');

DECLARE @TASK_MONITOR_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '任务监控' AND parent_id = @TASK_MENU_ID);

-- 任务监控权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('监控查询', @TASK_MONITOR_MENU_ID, 1, '', '', 1, 0, 'F', '0', '0', 'audit:monitor:query', '#', 'admin', GETDATE(), '', NULL, ''),
('监控详情', @TASK_MONITOR_MENU_ID, 2, '', '', 1, 0, 'F', '0', '0', 'audit:monitor:detail', '#', 'admin', GETDATE(), '', NULL, ''),
('监控删除', @TASK_MONITOR_MENU_ID, 3, '', '', 1, 0, 'F', '0', '0', 'audit:monitor:remove', '#', 'admin', GETDATE(), '', NULL, '');

-- 4. 规则配置子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('规则配置', @MAIN_MENU_ID, 3, 'rule', NULL, 1, 0, 'M', '0', '0', '', 'tool', 'admin', GETDATE(), '', NULL, '数据刷新规则配置');

DECLARE @RULE_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '规则配置' AND parent_id = @MAIN_MENU_ID);

-- 4.1 规则管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('规则管理', @RULE_MENU_ID, 1, 'config', 'audit/ruleConfig/ruleConfig', 1, 0, 'C', '0', '0', 'audit:ruleConfig:list', 'build', 'admin', GETDATE(), '', NULL, '数据刷新规则管理');

DECLARE @RULE_CONFIG_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '规则管理' AND parent_id = @RULE_MENU_ID);

-- 规则管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('规则查询', @RULE_CONFIG_MENU_ID, 1, '', '', 1, 0, 'F', '0', '0', 'audit:ruleConfig:query', '#', 'admin', GETDATE(), '', NULL, ''),
('规则新增', @RULE_CONFIG_MENU_ID, 2, '', '', 1, 0, 'F', '0', '0', 'audit:ruleConfig:add', '#', 'admin', GETDATE(), '', NULL, ''),
('规则修改', @RULE_CONFIG_MENU_ID, 3, '', '', 1, 0, 'F', '0', '0', 'audit:ruleConfig:edit', '#', 'admin', GETDATE(), '', NULL, ''),
('规则删除', @RULE_CONFIG_MENU_ID, 4, '', '', 1, 0, 'F', '0', '0', 'audit:ruleConfig:remove', '#', 'admin', GETDATE(), '', NULL, ''),
('规则导出', @RULE_CONFIG_MENU_ID, 5, '', '', 1, 0, 'F', '0', '0', 'audit:ruleConfig:export', '#', 'admin', GETDATE(), '', NULL, '');

-- 5. 数据导出子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('数据导出', @MAIN_MENU_ID, 4, 'export', NULL, 1, 0, 'M', '0', '0', '', 'download', 'admin', GETDATE(), '', NULL, '数据导出管理');

DECLARE @EXPORT_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '数据导出' AND parent_id = @MAIN_MENU_ID);

-- 5.1 导出管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('导出管理', @EXPORT_MENU_ID, 1, 'manage', 'audit/export/index', 1, 0, 'C', '0', '0', 'audit:export:list', 'upload', 'admin', GETDATE(), '', NULL, '数据导出管理');

DECLARE @EXPORT_MANAGE_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '导出管理' AND parent_id = @EXPORT_MENU_ID);

-- 导出管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('导出查询', @EXPORT_MANAGE_MENU_ID, 1, '', '', 1, 0, 'F', '0', '0', 'audit:export:query', '#', 'admin', GETDATE(), '', NULL, ''),
('CSV导出', @EXPORT_MANAGE_MENU_ID, 2, '', '', 1, 0, 'F', '0', '0', 'audit:export:csv', '#', 'admin', GETDATE(), '', NULL, ''),
('Excel导出', @EXPORT_MANAGE_MENU_ID, 3, '', '', 1, 0, 'F', '0', '0', 'audit:export:excel', '#', 'admin', GETDATE(), '', NULL, ''),
('导出删除', @EXPORT_MANAGE_MENU_ID, 4, '', '', 1, 0, 'F', '0', '0', 'audit:export:remove', '#', 'admin', GETDATE(), '', NULL, ''),
('文件下载', @EXPORT_MANAGE_MENU_ID, 5, '', '', 1, 0, 'F', '0', '0', 'audit:export:download', '#', 'admin', GETDATE(), '', NULL, '');

-- 6. 系统配置子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('系统配置', @MAIN_MENU_ID, 5, 'config', NULL, 1, 0, 'M', '0', '0', '', 'system', 'admin', GETDATE(), '', NULL, '系统配置管理');

DECLARE @CONFIG_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '系统配置' AND parent_id = @MAIN_MENU_ID);

-- 6.1 参数配置
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('参数配置', @CONFIG_MENU_ID, 1, 'params', 'audit/config/params', 1, 0, 'C', '0', '0', 'audit:config:list', 'edit', 'admin', GETDATE(), '', NULL, '系统参数配置');

DECLARE @CONFIG_PARAMS_MENU_ID BIGINT = (SELECT menu_id FROM sys_menu WHERE menu_name = '参数配置' AND parent_id = @CONFIG_MENU_ID);

-- 参数配置权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('参数查询', @CONFIG_PARAMS_MENU_ID, 1, '', '', 1, 0, 'F', '0', '0', 'audit:config:query', '#', 'admin', GETDATE(), '', NULL, ''),
('参数新增', @CONFIG_PARAMS_MENU_ID, 2, '', '', 1, 0, 'F', '0', '0', 'audit:config:add', '#', 'admin', GETDATE(), '', NULL, ''),
('参数修改', @CONFIG_PARAMS_MENU_ID, 3, '', '', 1, 0, 'F', '0', '0', 'audit:config:edit', '#', 'admin', GETDATE(), '', NULL, ''),
('参数删除', @CONFIG_PARAMS_MENU_ID, 4, '', '', 1, 0, 'F', '0', '0', 'audit:config:remove', '#', 'admin', GETDATE(), '', NULL, '');

-- 为admin角色分配所有化学审计系统权限
DECLARE @ADMIN_ROLE_ID BIGINT = (SELECT role_id FROM sys_role WHERE role_key = 'admin');

-- 获取所有化学审计系统相关的菜单ID
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @ADMIN_ROLE_ID, menu_id FROM sys_menu 
WHERE menu_id = @MAIN_MENU_ID 
   OR parent_id = @MAIN_MENU_ID 
   OR parent_id IN (SELECT menu_id FROM sys_menu WHERE parent_id = @MAIN_MENU_ID)
   OR parent_id IN (SELECT menu_id FROM sys_menu WHERE parent_id IN (SELECT menu_id FROM sys_menu WHERE parent_id = @MAIN_MENU_ID));

-- 创建字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES
('化学任务类型', 'chemical_task_type', '0', 'admin', GETDATE(), '化学任务类型列表'),
('化学任务状态', 'chemical_task_status', '0', 'admin', GETDATE(), '化学任务状态列表'),
('化学数据状态', 'chemical_data_status', '0', 'admin', GETDATE(), '化学数据状态列表'),
('导出文件类型', 'export_file_type', '0', 'admin', GETDATE(), '导出文件类型列表');

-- 创建字典数据
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '数据读取', 'DATA_READ', 'chemical_task_type', '', 'primary', 'Y', '0', 'admin', GETDATE(), '数据读取任务'),
(2, '数据处理', 'DATA_PROCESS', 'chemical_task_type', '', 'info', 'N', '0', 'admin', GETDATE(), '数据处理任务'),
(3, '数据刷新', 'DATA_REFRESH', 'chemical_task_type', '', 'warning', 'N', '0', 'admin', GETDATE(), '数据刷新任务'),
(4, '数据导出', 'DATA_EXPORT', 'chemical_task_type', '', 'success', 'N', '0', 'admin', GETDATE(), '数据导出任务'),
(5, '统一处理', 'UNIFIED_PROCESS', 'chemical_task_type', '', 'danger', 'N', '0', 'admin', GETDATE(), '统一数据处理任务'),

(1, '等待中', 'PENDING', 'chemical_task_status', '', 'info', 'Y', '0', 'admin', GETDATE(), '任务等待中'),
(2, '运行中', 'RUNNING', 'chemical_task_status', '', 'primary', 'N', '0', 'admin', GETDATE(), '任务运行中'),
(3, '已完成', 'SUCCESS', 'chemical_task_status', '', 'success', 'N', '0', 'admin', GETDATE(), '任务执行成功'),
(4, '已失败', 'FAILED', 'chemical_task_status', '', 'danger', 'N', '0', 'admin', GETDATE(), '任务执行失败'),
(5, '已暂停', 'PAUSED', 'chemical_task_status', '', 'warning', 'N', '0', 'admin', GETDATE(), '任务已暂停'),
(6, '已取消', 'CANCELLED', 'chemical_task_status', '', 'info', 'N', '0', 'admin', GETDATE(), '任务已取消'),

(1, '正常', '0', 'chemical_data_status', '', 'primary', 'Y', '0', 'admin', GETDATE(), '正常状态'),
(2, '停用', '1', 'chemical_data_status', '', 'danger', 'N', '0', 'admin', GETDATE(), '停用状态'),

(1, 'CSV文件', 'CSV', 'export_file_type', '', 'primary', 'Y', '0', 'admin', GETDATE(), 'CSV格式文件'),
(2, 'Excel文件', 'EXCEL', 'export_file_type', '', 'success', 'N', '0', 'admin', GETDATE(), 'Excel格式文件'),
(3, 'PDF文件', 'PDF', 'export_file_type', '', 'info', 'N', '0', 'admin', GETDATE(), 'PDF格式文件');
