<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TestResultMapper">
    
    <resultMap type="TestResult" id="TestResult">
        <result property="testResultId"    column="test_result_id"    />
        <result property="planGroupId"    column="plan_group_id"    />
        <result property="testParamId"    column="test_param_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="supplierDatasheetVal"    column="supplier_datasheet_val"    />
        <result property="testValue"    column="test_value"    />
        <result property="attachments"    column="attachments"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="planCode"    column="plan_code"    />
        <result property="performanceName"    column="performance_name"    />
        <result property="paramNumber"    column="param_number"    />
        <result property="materialName"    column="material_name"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="processType"    column="process_type"    />
        <result property="paramName"    column="param_name"    />
        <result property="paramUnit"    column="param_unit"    />
        <result property="materialModel"    column="material_model"    />
        <result property="performanceType"    column="performance_type"    />
        <result property="testEquipment"    column="test_equipment"    />
        <result property="testParameter"    column="test_parameter"    />
    </resultMap>

    <sql id="selectTestResultVo">
        select tr.test_result_id, tr.plan_group_id, tr.test_param_id, tr.group_id, tr.supplier_datasheet_val, tr.test_value, tr.attachments, tr.remark, tr.create_by, tr.create_time, tr.update_by, tr.update_time,
               tpg.plan_code, tpg.performance_name, tpg.performance_type, tpg.test_equipment,
               tpi.param_name, tpi.param_value as test_parameter, tpi.unit as param_unit,
               pg.param_number, pg.process_type,
               m.material_name, m.supplier_name, m.material_model
        from test_results tr
        left join test_plan_group tpg on tr.plan_group_id = tpg.plan_group_id
        left join test_param_item tpi on tr.test_param_id = tpi.test_param_id
        left join process_param_group pg on tr.group_id = pg.group_id
        left join materials m on pg.material_id = m.material_id
    </sql>

    <select id="selectTestResultList" parameterType="TestResult" resultMap="TestResult">
        <include refid="selectTestResultVo"/>
        <where>
            <if test="planGroupId != null "> and tr.plan_group_id = #{planGroupId}</if>
            <if test="testParamId != null "> and tr.test_param_id = #{testParamId}</if>
            <if test="groupId != null "> and tr.group_id = #{groupId}</if>
            <if test="supplierDatasheetVal != null  and supplierDatasheetVal != ''"> and tr.supplier_datasheet_val like concat('%', #{supplierDatasheetVal}, '%')</if>
            <if test="testValue != null "> and tr.test_value = #{testValue}</if>
            <if test="planCode != null  and planCode != ''"> and tpg.plan_code like concat('%', #{planCode}, '%')</if>
            <if test="testPlanCode != null  and testPlanCode != ''"> and tpg.plan_code like concat('%', #{testPlanCode}, '%')</if>
            <if test="paramNumber != null  and paramNumber != ''"> and pg.param_number like concat('%', #{paramNumber}, '%')</if>
            <if test="materialName != null  and materialName != ''"> and m.material_name like concat('%', #{materialName}, '%')</if>
            <if test="supplierName != null  and supplierName != ''"> and m.supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="materialModel != null  and materialModel != ''"> and m.material_model like concat('%', #{materialModel}, '%')</if>
            <if test="processType != null  and processType != ''"> and pg.process_type like concat('%', #{processType}, '%')</if>
            <if test="performanceType != null  and performanceType != ''"> and tpg.performance_type like concat('%', #{performanceType}, '%')</if>
            <if test="testEquipment != null  and testEquipment != ''"> and tpg.test_equipment like concat('%', #{testEquipment}, '%')</if>
            <if test="paramName != null  and paramName != ''"> and tpi.param_name like concat('%', #{paramName}, '%')</if>
        </where>
        order by tr.create_time desc
    </select>
    
    <select id="selectTestResultByTestResultId" parameterType="Long" resultMap="TestResult">
        <include refid="selectTestResultVo"/>
        where tr.test_result_id = #{testResultId}
    </select>
        
    <insert id="insertTestResult" parameterType="TestResult" useGeneratedKeys="true" keyProperty="testResultId">
        insert into test_results
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planGroupId != null">plan_group_id,</if>
            <if test="testParamId != null">test_param_id,</if>
            <if test="groupId != null">group_id,</if>
            <if test="supplierDatasheetVal != null">supplier_datasheet_val,</if>
            <if test="testValue != null">test_value,</if>
            <if test="attachments != null">attachments,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planGroupId != null">#{planGroupId},</if>
            <if test="testParamId != null">#{testParamId},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="supplierDatasheetVal != null">#{supplierDatasheetVal},</if>
            <if test="testValue != null">#{testValue},</if>

            <if test="attachments != null">#{attachments},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateTestResult" parameterType="TestResult">
        update test_results
        <trim prefix="SET" suffixOverrides=",">
            <if test="planGroupId != null">plan_group_id = #{planGroupId},</if>
            <if test="testParamId != null">test_param_id = #{testParamId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="supplierDatasheetVal != null">supplier_datasheet_val = #{supplierDatasheetVal},</if>
            <if test="testValue != null">test_value = #{testValue},</if>
            attachments = #{attachments},
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where test_result_id = #{testResultId}
    </update>

    <delete id="deleteTestResultByTestResultId" parameterType="Long">
        delete from test_results where test_result_id = #{testResultId}
    </delete>

    <delete id="deleteTestResultByTestResultIds" parameterType="String">
        delete from test_results where test_result_id in 
        <foreach item="testResultId" collection="array" open="(" separator="," close=")">
            #{testResultId}
        </foreach>
    </delete>

    <select id="selectTestResultTrendList" parameterType="TestResult" resultMap="TestResult">
        <include refid="selectTestResultVo"/>
        <where>
            <if test="planCode != null and planCode != ''"> and tpg.plan_code like concat('%', #{planCode}, '%')</if>
            <if test="paramNumber != null and paramNumber != ''"> and pg.param_number like concat('%', #{paramNumber}, '%')</if>
            <if test="materialName != null and materialName != ''"> and m.material_name like concat('%', #{materialName}, '%')</if>
        </where>
        order by tr.create_time desc
    </select>

    <select id="selectPlanCodeOptions" resultType="String">
        select distinct plan_code from test_plan_group where plan_code is not null and plan_code != '' order by plan_code
    </select>

    <select id="selectParamNumberOptions" resultType="String">
        select distinct param_number from process_param_group where param_number is not null and param_number != '' order by param_number
    </select>

    <!-- 获取材料名称选项 -->
    <select id="selectMaterialNameOptions" resultType="String">
        select distinct material_name from materials where material_name is not null and material_name != '' order by material_name
    </select>

    <!-- 获取供应商名称选项 -->
    <select id="selectSupplierNameOptions" resultType="String">
        select distinct supplier_name from materials where supplier_name is not null and supplier_name != '' order by supplier_name
    </select>

    <!-- 获取工艺类型选项 -->
    <select id="selectProcessTypeOptions" resultType="String">
        select distinct process_type from process_param_group where process_type is not null and process_type != '' order by process_type
    </select>

    <!-- 获取测试方案组选项 -->
    <select id="selectTestPlanGroupOptions" resultType="String">
        select distinct plan_code from test_plan_group where plan_code is not null and plan_code != '' order by plan_code
    </select>

    <!-- 获取测试参数明细选项 -->
    <select id="selectTestParamItemOptions" resultType="String">
        select distinct param_name from test_param_item where param_name is not null and param_name != '' order by param_name
    </select>

    <!-- 根据测试方案组ID获取测试参数明细选项 -->
    <select id="selectTestParamItemOptionsByPlanGroupId" parameterType="Long" resultType="String">
        select distinct param_name from test_param_item
        where plan_group_id = #{planGroupId} and param_name is not null and param_name != ''
        order by param_name
    </select>

    <!-- 获取材料型号选项 -->
    <select id="selectMaterialModelOptions" resultType="String">
        select distinct material_model from materials where material_model is not null and material_model != '' order by material_model
    </select>

    <!-- 获取性能类型选项 -->
    <select id="selectPerformanceTypeOptions" resultType="String">
        select distinct performance_type from test_plan_group where performance_type is not null and performance_type != '' order by performance_type
    </select>

    <!-- 获取测试设备选项 -->
    <select id="selectTestEquipmentOptions" resultType="String">
        select distinct test_equipment from test_plan_group where test_equipment is not null and test_equipment != '' order by test_equipment
    </select>

</mapper>
