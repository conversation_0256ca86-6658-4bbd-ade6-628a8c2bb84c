package com.ruoyi.system.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 材料信息对象 materials
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class Material extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 材料ID */
    private Long materialId;

    /** 材料名称 */
    @Excel(name = "材料名称")
    private String materialName;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String supplierName;

    /** 材料型号 */
    @Excel(name = "材料型号")
    private String materialModel;

    /** 材料描述 */
    @Excel(name = "材料描述")
    private String materialDescription;

    /** 附件URL */
    private String attachments;

    /** 工艺参数组信息 */
    private List<ProcessParamGroup> processParamGroupList;

    public void setMaterialId(Long materialId)
    {
        this.materialId = materialId;
    }

    public Long getMaterialId()
    {
        return materialId;
    }
    public void setMaterialName(String materialName)
    {
        this.materialName = materialName;
    }

    public String getMaterialName()
    {
        return materialName;
    }
    public void setSupplierName(String supplierName)
    {
        this.supplierName = supplierName;
    }

    public String getSupplierName()
    {
        return supplierName;
    }
    public void setMaterialModel(String materialModel)
    {
        this.materialModel = materialModel;
    }

    public String getMaterialModel()
    {
        return materialModel;
    }
    public void setMaterialDescription(String materialDescription)
    {
        this.materialDescription = materialDescription;
    }

    public String getMaterialDescription()
    {
        return materialDescription;
    }
    public void setAttachments(String attachments)
    {
        this.attachments = attachments;
    }

    public String getAttachments()
    {
        return attachments;
    }

    public List<ProcessParamGroup> getProcessParamGroupList()
    {
        return processParamGroupList;
    }

    public void setProcessParamGroupList(List<ProcessParamGroup> processParamGroupList)
    {
        this.processParamGroupList = processParamGroupList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("materialId", getMaterialId())
                .append("materialName", getMaterialName())
                .append("supplierName", getSupplierName())
                .append("materialModel", getMaterialModel())
                .append("materialDescription", getMaterialDescription())
                .append("attachments", getAttachments())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("processParamGroupList", getProcessParamGroupList())
                .toString();
    }
}