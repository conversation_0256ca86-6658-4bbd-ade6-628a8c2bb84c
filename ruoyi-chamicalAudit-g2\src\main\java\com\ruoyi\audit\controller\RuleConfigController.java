package com.ruoyi.audit.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.audit.domain.RuleConfig;
import com.ruoyi.audit.service.IRuleConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据刷新规则配置Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/audit/ruleConfig")
public class RuleConfigController extends BaseController
{
    @Autowired
    private IRuleConfigService ruleConfigService;

    /**
     * 查询数据刷新规则配置列表
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(RuleConfig ruleConfig)
    {
        startPage();
        List<RuleConfig> list = ruleConfigService.selectRuleConfigList(ruleConfig);
        return getDataTable(list);
    }

    /**
     * 导出数据刷新规则配置列表
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:export')")
    @Log(title = "数据刷新规则配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RuleConfig ruleConfig)
    {
        List<RuleConfig> list = ruleConfigService.selectRuleConfigList(ruleConfig);
        ExcelUtil<RuleConfig> util = new ExcelUtil<RuleConfig>(RuleConfig.class);
        util.exportExcel(response, list, "数据刷新规则配置数据");
    }

    /**
     * 获取数据刷新规则配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ruleConfigService.selectRuleConfigById(id));
    }

    /**
     * 根据产品、过程、测试名称查询规则配置
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:query')")
    @GetMapping("/getByNames")
    public AjaxResult getByNames(String productName, String processName, String testName)
    {
        RuleConfig ruleConfig = ruleConfigService.selectRuleConfigByNames(productName, processName, testName);
        if (ruleConfig == null) {
            ruleConfig = ruleConfigService.getDefaultRuleConfig();
        }
        return success(ruleConfig);
    }

    /**
     * 新增数据刷新规则配置
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:add')")
    @Log(title = "数据刷新规则配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RuleConfig ruleConfig)
    {
        // 检查是否已存在相同配置
        RuleConfig existingConfig = ruleConfigService.selectRuleConfigByNames(
            ruleConfig.getProductName(), 
            ruleConfig.getProcessName(), 
            ruleConfig.getTestName()
        );
        
        if (existingConfig != null) {
            return error("该产品-过程-测试的规则配置已存在，请使用修改功能");
        }
        
        ruleConfig.setCreatedBy(getUsername());
        ruleConfig.setUpdatedBy(getUsername());
        return toAjax(ruleConfigService.insertRuleConfig(ruleConfig));
    }

    /**
     * 修改数据刷新规则配置
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:edit')")
    @Log(title = "数据刷新规则配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RuleConfig ruleConfig)
    {
        ruleConfig.setUpdatedBy(getUsername());
        return toAjax(ruleConfigService.updateRuleConfig(ruleConfig));
    }

    /**
     * 删除数据刷新规则配置
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:remove')")
    @Log(title = "数据刷新规则配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ruleConfigService.deleteRuleConfigByIds(ids));
    }

    /**
     * 获取默认规则配置
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:query')")
    @GetMapping("/default")
    public AjaxResult getDefaultConfig()
    {
        return success(ruleConfigService.getDefaultRuleConfig());
    }

    /**
     * 批量设置规则配置
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:edit')")
    @Log(title = "批量设置规则配置", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSet")
    public AjaxResult batchSet(@RequestBody List<RuleConfig> ruleConfigs)
    {
        int successCount = 0;
        int failureCount = 0;
        
        for (RuleConfig ruleConfig : ruleConfigs) {
            try {
                // 检查是否已存在
                RuleConfig existingConfig = ruleConfigService.selectRuleConfigByNames(
                    ruleConfig.getProductName(), 
                    ruleConfig.getProcessName(), 
                    ruleConfig.getTestName()
                );
                
                ruleConfig.setUpdatedBy(getUsername());
                
                if (existingConfig != null) {
                    ruleConfig.setId(existingConfig.getId());
                    ruleConfigService.updateRuleConfig(ruleConfig);
                } else {
                    ruleConfig.setCreatedBy(getUsername());
                    ruleConfigService.insertRuleConfig(ruleConfig);
                }
                successCount++;
            } catch (Exception e) {
                failureCount++;
                logger.error("批量设置规则配置失败: " + ruleConfig.getProductName() + "-" + 
                           ruleConfig.getProcessName() + "-" + ruleConfig.getTestName(), e);
            }
        }
        
        return success("批量设置完成，成功: " + successCount + " 条，失败: " + failureCount + " 条");
    }

    /**
     * 复制规则配置
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:add')")
    @Log(title = "复制规则配置", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{id}")
    public AjaxResult copy(@PathVariable Long id, @RequestBody RuleConfig newRuleConfig)
    {
        RuleConfig sourceConfig = ruleConfigService.selectRuleConfigById(id);
        if (sourceConfig == null) {
            return error("源规则配置不存在");
        }
        
        // 检查目标配置是否已存在
        RuleConfig existingConfig = ruleConfigService.selectRuleConfigByNames(
            newRuleConfig.getProductName(), 
            newRuleConfig.getProcessName(), 
            newRuleConfig.getTestName()
        );
        
        if (existingConfig != null) {
            return error("目标规则配置已存在");
        }
        
        // 复制配置
        RuleConfig copyConfig = new RuleConfig();
        copyConfig.setProductName(newRuleConfig.getProductName());
        copyConfig.setProcessName(newRuleConfig.getProcessName());
        copyConfig.setTestName(newRuleConfig.getTestName());
        copyConfig.setEnableControlLimitAdjustment(sourceConfig.getEnableControlLimitAdjustment());
        copyConfig.setEnableMovingRangeAdjustment(sourceConfig.getEnableMovingRangeAdjustment());
        copyConfig.setEnableNinePointSameSideCheck(sourceConfig.getEnableNinePointSameSideCheck());
        copyConfig.setEnableSixPointTrendCheck(sourceConfig.getEnableSixPointTrendCheck());
        copyConfig.setEnableCpkAdjustment(sourceConfig.getEnableCpkAdjustment());
        copyConfig.setCpkTarget(sourceConfig.getCpkTarget());
        copyConfig.setIsRefreshMode(sourceConfig.getIsRefreshMode());
        copyConfig.setRuleDescription("复制自: " + sourceConfig.getProductName() + "-" + 
                                     sourceConfig.getProcessName() + "-" + sourceConfig.getTestName());
        copyConfig.setStatus(sourceConfig.getStatus());
        copyConfig.setCreatedBy(getUsername());
        copyConfig.setUpdatedBy(getUsername());
        
        return toAjax(ruleConfigService.insertRuleConfig(copyConfig));
    }

    /**
     * 启用/禁用规则配置
     */
    @PreAuthorize("@ss.hasPermi('audit:ruleConfig:edit')")
    @Log(title = "启用/禁用规则配置", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RuleConfig ruleConfig)
    {
        ruleConfig.setUpdatedBy(getUsername());
        return toAjax(ruleConfigService.updateRuleConfig(ruleConfig));
    }
}
