// 材料配置页面的方法补充
export default {
  /** 参数名称搜索建议 */
  queryParamNameSuggestions(queryString, cb) {
    let suggestions = this.paramNameSuggestions;
    if (queryString) {
      suggestions = this.paramNameSuggestions.filter(item => {
        return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
      });
    }
    cb(suggestions);
  },

  /** 参数编号搜索建议 */
  queryParamNumberSuggestions(queryString, cb) {
    let suggestions = this.paramNumberSuggestions;
    if (queryString) {
      suggestions = this.paramNumberSuggestions.filter(item => {
        return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
      });
    }
    cb(suggestions);
  },

  /** 参数单位搜索建议 */
  queryUnitSuggestions(queryString, cb) {
    let suggestions = this.unitSuggestions;
    if (queryString) {
      suggestions = this.unitSuggestions.filter(item => {
        return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
      });
    }
    cb(suggestions);
  },

  /** 材料名称选择事件 */
  handleMaterialNameSelect(item) {
    this.materialQueryParams.materialName = item.value;
    this.handleMaterialQuery();
  },

  /** 查询材料列表 */
  getMaterialList() {
    this.materialLoading = true;
    listMaterial(this.materialQueryParams).then(response => {
      this.materialList = response.rows;
      this.materialTotal = response.total;
      this.materialLoading = false;
    });
  },

  /** 材料查询 */
  handleMaterialQuery() {
    this.materialQueryParams.pageNum = 1;
    this.getMaterialList();
  },

  /** 重置材料查询 */
  resetMaterialQuery() {
    this.resetForm("materialQueryForm");
    this.handleMaterialQuery();
  },

  /** 材料行点击事件 */
  handleMaterialClick(row) {
    this.currentMaterial = row;
    this.paramGroupQueryParams.materialId = row.materialId;
    this.getParamGroupList();
    this.paramItemList = [];
    this.paramItemTotal = 0;
    this.currentParamGroup = null;
  },

  /** 材料行样式 */
  getMaterialRowClassName({row, rowIndex}) {
    if (this.currentMaterial && row.materialId === this.currentMaterial.materialId) {
      return 'current-row';
    }
    return '';
  },

  /** 查询工艺参数组列表 */
  getParamGroupList() {
    if (!this.currentMaterial) return;
    this.paramGroupLoading = true;
    listProcessParamGroup(this.paramGroupQueryParams).then(response => {
      this.paramGroupList = response.rows;
      this.paramGroupTotal = response.total;
      this.paramGroupLoading = false;
    });
  },

  /** 参数组查询 */
  handleParamGroupQuery() {
    this.paramGroupQueryParams.pageNum = 1;
    this.getParamGroupList();
  },

  /** 重置参数组查询 */
  resetParamGroupQuery() {
    this.resetForm("paramGroupQueryForm");
    this.handleParamGroupQuery();
  },

  /** 工艺参数组行点击事件 */
  handleParamGroupClick(row) {
    this.currentParamGroup = row;
    this.paramItemQueryParams.groupId = row.groupId;
    this.getParamItemList();
  },

  /** 参数组行样式 */
  getParamGroupRowClassName({row, rowIndex}) {
    if (this.currentParamGroup && row.groupId === this.currentParamGroup.groupId) {
      return 'current-row';
    }
    return '';
  },

  /** 查询参数明细列表 */
  getParamItemList() {
    if (!this.currentParamGroup) return;
    this.paramItemLoading = true;
    listProcessParamItem(this.paramItemQueryParams).then(response => {
      this.paramItemList = response.rows;
      this.paramItemTotal = response.total;
      this.paramItemLoading = false;
    });
  },

  /** 参数明细查询 */
  handleParamItemQuery() {
    this.paramItemQueryParams.pageNum = 1;
    this.getParamItemList();
  },

  /** 重置参数明细查询 */
  resetParamItemQuery() {
    this.resetForm("paramItemQueryForm");
    this.handleParamItemQuery();
  },

  /** 新增材料 */
  handleAddMaterial() {
    this.resetMaterialForm();
    this.materialOpen = true;
    this.materialTitle = "添加材料信息";
  },

  /** 修改材料 */
  handleEditMaterial(row) {
    this.resetMaterialForm();
    const materialId = row.materialId;
    getMaterial(materialId).then(response => {
      this.materialForm = response.data;
      this.materialFileList = response.data.attachments || [];
      this.materialOpen = true;
      this.materialTitle = "修改材料信息";
    });
  },

  /** 提交材料表单 */
  submitMaterialForm() {
    this.$refs["materialForm"].validate(valid => {
      if (valid) {
        this.materialForm.attachmentList = this.materialFileList;
        if (this.materialForm.materialId != null) {
          updateMaterial(this.materialForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.materialOpen = false;
            this.getMaterialList();
          });
        } else {
          addMaterial(this.materialForm).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.materialOpen = false;
            this.getMaterialList();
          });
        }
      }
    });
  },

  /** 取消材料操作 */
  cancelMaterial() {
    this.materialOpen = false;
    this.resetMaterialForm();
  },

  /** 重置材料表单 */
  resetMaterialForm() {
    this.materialForm = {
      materialId: null,
      materialName: null,
      supplierName: null,
      materialModel: null,
      materialDescription: null,
      attachments: null,
      remark: null
    };
    this.materialFileList = [];
    this.resetForm("materialForm");
  },

  /** 删除材料 */
  handleDeleteMaterial(row) {
    const materialIds = row.materialId;
    this.$modal.confirm('是否确认删除材料编号为"' + materialIds + '"的数据项？').then(function() {
      return delMaterial(materialIds);
    }).then(() => {
      this.getMaterialList();
      this.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  },

  /** 导出材料 */
  handleExportMaterial() {
    this.download('material/material/export', {
      ...this.materialQueryParams
    }, `material_${new Date().getTime()}.xlsx`);
  },

  /** 导入材料 */
  handleImportMaterial() {
    this.$refs.importInput.click();
  },

  /** 处理文件导入 */
  handleFileImport(event) {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      
      this.$http.post('/material/material/importData', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': 'Bearer ' + getToken()
        }
      }).then(response => {
        if (response.data.code === 200) {
          this.$modal.msgSuccess("导入成功");
          this.getMaterialList();
        } else {
          this.$modal.msgError(response.data.msg || "导入失败");
        }
      }).catch(error => {
        this.$modal.msgError("导入失败: " + error.message);
      });
      
      // 清空文件输入框
      event.target.value = '';
    }
  },

  /** 整体导出 */
  handleExportComplete() {
    this.$modal.loading("正在导出数据，请稍候...");
    exportCompleteData(this.materialQueryParams).then(response => {
      this.$modal.closeLoading();
      this.download('material/material/exportComplete', {
        ...this.materialQueryParams
      }, `complete_data_${new Date().getTime()}.xlsx`);
    }).catch(error => {
      this.$modal.closeLoading();
      this.$modal.msgError("导出失败: " + (error.message || "未知错误"));
    });
  },

  /** 新增工艺参数组 */
  handleAddParamGroup() {
    this.resetParamGroupForm();
    this.paramGroupForm.materialId = this.currentMaterial.materialId;
    this.paramGroupOpen = true;
    this.paramGroupTitle = "添加工艺参数组";
  },

  /** 修改工艺参数组 */
  handleEditParamGroup(row) {
    this.resetParamGroupForm();
    const groupId = row.groupId;
    getProcessParamGroup(groupId).then(response => {
      this.paramGroupForm = response.data;
      this.paramGroupFileList = response.data.attachments || [];
      this.paramGroupOpen = true;
      this.paramGroupTitle = "修改工艺参数组";
    });
  },

  /** 提交工艺参数组表单 */
  submitParamGroupForm() {
    this.$refs["paramGroupForm"].validate(valid => {
      if (valid) {
        this.paramGroupForm.attachmentList = this.paramGroupFileList;
        if (this.paramGroupForm.groupId != null) {
          updateProcessParamGroup(this.paramGroupForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.paramGroupOpen = false;
            this.getParamGroupList();
          });
        } else {
          addProcessParamGroup(this.paramGroupForm).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.paramGroupOpen = false;
            this.getParamGroupList();
          });
        }
      }
    });
  },

  /** 取消工艺参数组操作 */
  cancelParamGroup() {
    this.paramGroupOpen = false;
    this.resetParamGroupForm();
  },

  /** 重置工艺参数组表单 */
  resetParamGroupForm() {
    this.paramGroupForm = {
      groupId: null,
      materialId: null,
      processType: null,
      paramNumber: null,
      attachments: null,
      remark: null
    };
    this.paramGroupFileList = [];
    this.resetForm("paramGroupForm");
  },

  /** 删除工艺参数组 */
  handleDeleteParamGroup(row) {
    const groupIds = row.groupId;
    this.$modal.confirm('是否确认删除参数组编号为"' + groupIds + '"的数据项？').then(function() {
      return delProcessParamGroup(groupIds);
    }).then(() => {
      this.getParamGroupList();
      this.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  },

  /** 导出工艺参数组 */
  handleExportParamGroup() {
    this.download('material/processParamGroup/export', {
      ...this.paramGroupQueryParams
    }, `param_group_${new Date().getTime()}.xlsx`);
  },

  /** 新增参数明细 */
  handleAddParamItem() {
    this.resetParamItemForm();
    this.paramItemForm.groupId = this.currentParamGroup.groupId;
    this.paramItemOpen = true;
    this.paramItemTitle = "添加参数明细";
  },

  /** 修改参数明细 */
  handleEditParamItem(row) {
    this.resetParamItemForm();
    const itemId = row.itemId;
    getProcessParamItem(itemId).then(response => {
      this.paramItemForm = response.data;
      this.paramItemFileList = response.data.attachments || [];
      this.paramItemOpen = true;
      this.paramItemTitle = "修改参数明细";
    });
  },

  /** 提交参数明细表单 */
  submitParamItemForm() {
    this.$refs["paramItemForm"].validate(valid => {
      if (valid) {
        this.paramItemForm.attachmentList = this.paramItemFileList;
        if (this.paramItemForm.itemId != null) {
          updateProcessParamItem(this.paramItemForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.paramItemOpen = false;
            this.getParamItemList();
          });
        } else {
          addProcessParamItem(this.paramItemForm).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.paramItemOpen = false;
            this.getParamItemList();
          });
        }
      }
    });
  },

  /** 取消参数明细操作 */
  cancelParamItem() {
    this.paramItemOpen = false;
    this.resetParamItemForm();
  },

  /** 重置参数明细表单 */
  resetParamItemForm() {
    this.paramItemForm = {
      itemId: null,
      groupId: null,
      paramName: null,
      paramValue: null,
      unit: null,
      attachments: null,
      remark: null
    };
    this.paramItemFileList = [];
    this.resetForm("paramItemForm");
  },

  /** 删除参数明细 */
  handleDeleteParamItem(row) {
    const itemIds = row.itemId;
    this.$modal.confirm('是否确认删除参数明细编号为"' + itemIds + '"的数据项？').then(function() {
      return delProcessParamItem(itemIds);
    }).then(() => {
      this.getParamItemList();
      this.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  },

  /** 导出参数明细 */
  handleExportParamItem() {
    this.download('material/processParamItem/export', {
      ...this.paramItemQueryParams
    }, `param_item_${new Date().getTime()}.xlsx`);
  },

  /** 材料附件上传成功 */
  handleMaterialUploadSuccess(response, file, fileList) {
    if (response.code === 200) {
      this.materialFileList = fileList.map(item => ({
        name: item.name,
        url: item.response ? item.response.url : item.url,
        size: this.formatFileSize(item.size)
      }));
      this.$modal.msgSuccess("上传成功");
    } else {
      this.$modal.msgError(response.msg);
    }
  },

  /** 材料附件移除 */
  handleMaterialFileRemove(file, fileList) {
    this.materialFileList = fileList.map(item => ({
      name: item.name,
      url: item.response ? item.response.url : item.url,
      size: this.formatFileSize(item.size)
    }));
  },

  /** 材料附件上传前检查 */
  beforeMaterialUpload(file) {
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      this.$modal.msgError('上传文件大小不能超过 10MB!');
    }
    return isLt10M;
  },

  /** 参数组附件上传成功 */
  handleParamGroupUploadSuccess(response, file, fileList) {
    if (response.code === 200) {
      this.paramGroupFileList = fileList.map(item => ({
        name: item.name,
        url: item.response ? item.response.url : item.url,
        size: this.formatFileSize(item.size)
      }));
      this.$modal.msgSuccess("上传成功");
    } else {
      this.$modal.msgError(response.msg);
    }
  },

  /** 参数组附件移除 */
  handleParamGroupFileRemove(file, fileList) {
    this.paramGroupFileList = fileList.map(item => ({
      name: item.name,
      url: item.response ? item.response.url : item.url,
      size: this.formatFileSize(item.size)
    }));
  },

  /** 参数组附件上传前检查 */
  beforeParamGroupUpload(file) {
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      this.$modal.msgError('上传文件大小不能超过 10MB!');
    }
    return isLt10M;
  },

  /** 参数明细附件上传成功 */
  handleParamItemUploadSuccess(response, file, fileList) {
    if (response.code === 200) {
      this.paramItemFileList = fileList.map(item => ({
        name: item.name,
        url: item.response ? item.response.url : item.url,
        size: this.formatFileSize(item.size)
      }));
      this.$modal.msgSuccess("上传成功");
    } else {
      this.$modal.msgError(response.msg);
    }
  },

  /** 参数明细附件移除 */
  handleParamItemFileRemove(file, fileList) {
    this.paramItemFileList = fileList.map(item => ({
      name: item.name,
      url: item.response ? item.response.url : item.url,
      size: this.formatFileSize(item.size)
    }));
  },

  /** 参数明细附件上传前检查 */
  beforeParamItemUpload(file) {
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      this.$modal.msgError('上传文件大小不能超过 10MB!');
    }
    return isLt10M;
  },

  /** 查看附件 */
  handleViewAttachments(attachments) {
    try {
      this.attachmentList = Array.isArray(attachments) ? attachments : JSON.parse(attachments || '[]');
    } catch (e) {
      this.attachmentList = [];
    }
    this.attachmentDialogVisible = true;
  },

  /** 下载附件 */
  downloadAttachment(url, name) {
    const link = document.createElement('a');
    link.href = url;
    link.download = name;
    link.click();
  },

  /** 格式化文件大小 */
  formatFileSize(size) {
    if (!size) return '0 B';
    if (size < 1024) {
      return size + ' B';
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' KB';
    } else {
      return (size / 1024 / 1024).toFixed(2) + ' MB';
    }
  }
};