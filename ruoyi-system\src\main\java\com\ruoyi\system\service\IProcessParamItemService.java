package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.ProcessParamItem;

/**
 * 工艺参数明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IProcessParamItemService 
{
    /**
     * 查询工艺参数明细
     * 
     * @param itemId 工艺参数明细主键
     * @return 工艺参数明细
     */
    public ProcessParamItem selectProcessParamItemByItemId(Long itemId);

    /**
     * 查询工艺参数明细列表
     * 
     * @param processParamItem 工艺参数明细
     * @return 工艺参数明细集合
     */
    public List<ProcessParamItem> selectProcessParamItemList(ProcessParamItem processParamItem);

    /**
     * 新增工艺参数明细
     * 
     * @param processParamItem 工艺参数明细
     * @return 结果
     */
    public int insertProcessParamItem(ProcessParamItem processParamItem);

    /**
     * 修改工艺参数明细
     * 
     * @param processParamItem 工艺参数明细
     * @return 结果
     */
    public int updateProcessParamItem(ProcessParamItem processParamItem);

    /**
    /**
     * 批量删除工艺参数明细
     *
     * @param itemIds 需要删除的工艺参数明细主键集合
     * @return 结果
     */
    public int deleteProcessParamItemByItemIds(Long[] itemIds);

    /**
     * 获取工艺参数明细选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    public List<String> selectProcessParamItemOptions(String type);

    /**
     * 删除工艺参数明细信息
     * 
     * @param itemId 工艺参数明细主键
     * @return 结果
     */
    public int deleteProcessParamItemByItemId(Long itemId);
}