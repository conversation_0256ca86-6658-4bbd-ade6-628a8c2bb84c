package com.ruoyi.audit.domain;

/**
 * 计算值参数
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class CalculatedValues 
{
    /** 均值 */
    private double mean;
    
    /** 上控制限 */
    private double upperControlLimit;
    
    /** 下控制限 */
    private double lowerControlLimit;
    
    /** 标准差 */
    private double standardDeviation;
    
    /** 3倍标准差 */
    private double threeSigma;
    
    /** 上调整范围 */
    private double upperAdjustmentRange;
    
    /** 下调整范围 */
    private double lowerAdjustmentRange;
    
    /** 测试名称 */
    private String testName;

    public CalculatedValues() {
    }

    public CalculatedValues(double mean, double upperControlLimit, double lowerControlLimit, 
                           double standardDeviation, String testName) {
        this.mean = mean;
        this.upperControlLimit = upperControlLimit;
        this.lowerControlLimit = lowerControlLimit;
        this.standardDeviation = standardDeviation;
        this.testName = testName;
        calculateDerivedValues();
    }

    /**
     * 计算派生值
     */
    private void calculateDerivedValues() {
        this.threeSigma = 3 * standardDeviation;
        this.upperAdjustmentRange = (upperControlLimit - mean) * 0.5;
        this.lowerAdjustmentRange = (mean - lowerControlLimit) * 0.5;
    }

    public double getMean() {
        return mean;
    }

    public void setMean(double mean) {
        this.mean = mean;
        calculateDerivedValues();
    }

    public double getUpperControlLimit() {
        return upperControlLimit;
    }

    public void setUpperControlLimit(double upperControlLimit) {
        this.upperControlLimit = upperControlLimit;
        calculateDerivedValues();
    }

    public double getLowerControlLimit() {
        return lowerControlLimit;
    }

    public void setLowerControlLimit(double lowerControlLimit) {
        this.lowerControlLimit = lowerControlLimit;
        calculateDerivedValues();
    }

    public double getStandardDeviation() {
        return standardDeviation;
    }

    public void setStandardDeviation(double standardDeviation) {
        this.standardDeviation = standardDeviation;
        calculateDerivedValues();
    }

    public double getThreeSigma() {
        return threeSigma;
    }

    public double getUpperAdjustmentRange() {
        return upperAdjustmentRange;
    }

    public double getLowerAdjustmentRange() {
        return lowerAdjustmentRange;
    }

    public String getTestName() {
        return testName;
    }

    public void setTestName(String testName) {
        this.testName = testName;
    }

    @Override
    public String toString() {
        return "CalculatedValues{" +
                "mean=" + mean +
                ", upperControlLimit=" + upperControlLimit +
                ", lowerControlLimit=" + lowerControlLimit +
                ", standardDeviation=" + standardDeviation +
                ", threeSigma=" + threeSigma +
                ", upperAdjustmentRange=" + upperAdjustmentRange +
                ", lowerAdjustmentRange=" + lowerAdjustmentRange +
                ", testName='" + testName + '\'' +
                '}';
    }
}
