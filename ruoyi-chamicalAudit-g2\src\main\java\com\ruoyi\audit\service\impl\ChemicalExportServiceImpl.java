package com.ruoyi.audit.service.impl;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.ruoyi.audit.domain.Chemical;
import com.ruoyi.audit.mapper.ChemicalMapper;
import com.ruoyi.audit.service.IChemicalExportService;

/**
 * 化学数据导出Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ChemicalExportServiceImpl implements IChemicalExportService 
{
    private static final Logger log = LoggerFactory.getLogger(ChemicalExportServiceImpl.class);

    @Autowired
    private ChemicalMapper chemicalMapper;

    // 导出路径配置
    @Value("${chemical.export.default.path:E:\\测试数据\\应审抛转数据\\G2}")
    private String defaultExportPath;

    @Value("${chemical.export.max.records:50000}")
    private int maxExportRecords;

    /**
     * 导出数据到CSV文件
     */
    @Override
    public Map<String, Object> exportToCSV(Date startDate, Date endDate, String[] layerNumbers) 
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证参数
            Map<String, Object> validation = validateExportParams(startDate, endDate, layerNumbers);
            if (!(Boolean) validation.get("success")) {
                return validation;
            }

            // 查询数据
            List<Chemical> exportData = queryExportData(startDate, endDate, layerNumbers);
            
            if (exportData.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有数据可导出");
                return result;
            }

            // 生成文件名 - 按照chemical-refresh格式
            SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
            SimpleDateFormat currentTimeFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
            String currentDateTime = currentTimeFormat.format(new Date());

            Date minDate = exportData.stream()
                .map(Chemical::getExamineDate)
                .min(Date::compareTo)
                .orElse(startDate);
            Date maxDate = exportData.stream()
                .map(Chemical::getExamineDate)
                .max(Date::compareTo)
                .orElse(endDate);

            String fileName = dateFormatter.format(minDate) + "_to_" + dateFormatter.format(maxDate) +
                            "_outTime_" + currentDateTime + "_chemicalOutput.csv";
            String filePath = defaultExportPath + File.separator + fileName;

            // 确保目录存在
            File directory = new File(defaultExportPath);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 写入CSV文件 - 按照chemical-refresh格式
            writeChemicalRefreshFormatCsv(exportData, filePath);

            result.put("success", true);
            result.put("message", "CSV导出成功");
            result.put("filePath", filePath);
            result.put("fileName", fileName);
            result.put("recordCount", exportData.size());

            log.info("成功导出 {} 条记录到CSV文件: {}", exportData.size(), filePath);

        } catch (IOException e) {
            log.error("导出CSV文件失败", e);
            result.put("success", false);
            result.put("message", "导出失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("导出CSV文件异常", e);
            result.put("success", false);
            result.put("message", "导出异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 导出数据到Excel文件
     */
    @Override
    public Map<String, Object> exportToExcel(Date startDate, Date endDate, String[] layerNumbers) 
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证参数
            Map<String, Object> validation = validateExportParams(startDate, endDate, layerNumbers);
            if (!(Boolean) validation.get("success")) {
                return validation;
            }

            // 查询数据
            List<Chemical> exportData = queryExportData(startDate, endDate, layerNumbers);
            
            if (exportData.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有数据可导出");
                return result;
            }

            // 生成文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String fileName = "chemical_export_" + sdf.format(new Date()) + ".xlsx";
            String filePath = defaultExportPath + File.separator + fileName;

            // 确保目录存在
            File directory = new File(defaultExportPath);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 创建Excel工作簿
            try (Workbook workbook = new XSSFWorkbook()) {
                Sheet sheet = workbook.createSheet("化学检测数据");

                // 创建标题行
                Row headerRow = sheet.createRow(0);
                String[] headers = {
                    "ID", "组织ID", "属性ID", "检测日期", "班次", "操作员工", "部门代码", "工艺集名称", "工艺名称",
                    "产品集名称", "产品名称", "测试集名称", "测试名称", "样本大小", "层号", "上限", "中位规格", "下限",
                    "检测值1", "检测值1应审", "检测值2", "频率", "频率单位", "槽体名称", "项目组代码", "项目组名称",
                    "测试代码", "调整上限", "调整中值", "调整下限", "项目单位", "插入时间", "是否已导出", "是否不处理",
                    "警告上限", "警告中值", "警告下限", "备注", "创建时间", "更新时间"
                };

                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                }

                // 写入数据行
                int rowNum = 1;
                for (Chemical chemical : exportData) {
                    Row row = sheet.createRow(rowNum++);
                    
                    row.createCell(0).setCellValue(nvl(chemical.getId()));
                    row.createCell(1).setCellValue(nvl(chemical.getOrganizationId()));
                    row.createCell(2).setCellValue(nvl(chemical.getAttributeId()));
                    row.createCell(3).setCellValue(nvl(chemical.getExamineDate()));
                    row.createCell(4).setCellValue(nvl(chemical.getShift()));
                    row.createCell(5).setCellValue(nvl(chemical.getStaff()));
                    row.createCell(6).setCellValue(nvl(chemical.getDepartmentCode()));
                    row.createCell(7).setCellValue(nvl(chemical.getProcessSetName()));
                    row.createCell(8).setCellValue(nvl(chemical.getProcessName()));
                    row.createCell(9).setCellValue(nvl(chemical.getProductSetName()));
                    row.createCell(10).setCellValue(nvl(chemical.getProductName()));
                    row.createCell(11).setCellValue(nvl(chemical.getTestSetName()));
                    row.createCell(12).setCellValue(nvl(chemical.getTestName()));
                    row.createCell(13).setCellValue(nvl(chemical.getSampleSize()));
                    row.createCell(14).setCellValue(nvl(chemical.getLayerNumber()));
                    row.createCell(15).setCellValue(nvl(chemical.getUpperLimit()));
                    row.createCell(16).setCellValue(nvl(chemical.getMedianSpecification()));
                    row.createCell(17).setCellValue(nvl(chemical.getDownLimit()));
                    row.createCell(18).setCellValue(nvl(chemical.getExamine1()));
                    row.createCell(19).setCellValue(nvl(chemical.getExamine1Ys()));
                    row.createCell(20).setCellValue(nvl(chemical.getExamine2()));
                    // 继续添加其他字段...
                }

                // 自动调整列宽
                for (int i = 0; i < headers.length; i++) {
                    sheet.autoSizeColumn(i);
                }

                // 写入文件
                try (java.io.FileOutputStream fileOut = new java.io.FileOutputStream(filePath)) {
                    workbook.write(fileOut);
                }
            }

            result.put("success", true);
            result.put("message", "Excel导出成功");
            result.put("filePath", filePath);
            result.put("fileName", fileName);
            result.put("recordCount", exportData.size());

            log.info("成功导出 {} 条记录到Excel文件: {}", exportData.size(), filePath);

        } catch (Exception e) {
            log.error("导出Excel文件失败", e);
            result.put("success", false);
            result.put("message", "导出失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 查询导出数据
     */
    private List<Chemical> queryExportData(Date startDate, Date endDate, String[] layerNumbers) 
    {
        Chemical queryParam = new Chemical();
        
        // 设置查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("beginTime", startDate);
        params.put("endTime", endDate);
        
        if (layerNumbers != null && layerNumbers.length > 0) {
            params.put("layerNumbers", Arrays.asList(layerNumbers));
        }
        
        queryParam.setParams(params);
        
        return chemicalMapper.selectChemicalList(queryParam);
    }

    /**
     * 空值处理
     */
    private String nvl(Object value)
    {
        return value == null ? "" : value.toString();
    }

    /**
     * 获取导出历史记录
     */
    @Override
    public Map<String, Object> getExportHistory()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 实现导出历史记录查询
            List<Map<String, Object>> history = new ArrayList<>();

            // 模拟历史记录数据
            Map<String, Object> record1 = new HashMap<>();
            record1.put("id", 1L);
            record1.put("fileName", "chemical_export_20240101_120000.csv");
            record1.put("exportType", "CSV");
            record1.put("recordCount", 1000);
            record1.put("fileSize", "2.5MB");
            record1.put("exportTime", new Date());
            record1.put("status", "SUCCESS");
            history.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("id", 2L);
            record2.put("fileName", "chemical_export_20240101_130000.xlsx");
            record2.put("exportType", "EXCEL");
            record2.put("recordCount", 800);
            record2.put("fileSize", "1.8MB");
            record2.put("exportTime", new Date());
            record2.put("status", "SUCCESS");
            history.add(record2);

            result.put("success", true);
            result.put("data", history);
            result.put("total", history.size());

        } catch (Exception e) {
            log.error("获取导出历史记录失败", e);
            result.put("success", false);
            result.put("message", "获取历史记录失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 下载导出文件
     */
    @Override
    public Map<String, Object> downloadExportFile(Long exportId)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 根据exportId查询文件信息
            // 这里应该从数据库查询文件信息，现在模拟实现
            if (exportId != null && exportId > 0) {
                String fileName = "chemical_export_" + exportId + ".csv";
                String filePath = defaultExportPath + File.separator + fileName;

                File file = new File(filePath);
                if (file.exists()) {
                    result.put("success", true);
                    result.put("fileName", fileName);
                    result.put("filePath", filePath);
                    result.put("fileSize", file.length());
                } else {
                    result.put("success", false);
                    result.put("message", "文件不存在");
                }
            } else {
                result.put("success", false);
                result.put("message", "无效的导出ID");
            }

        } catch (Exception e) {
            log.error("下载导出文件失败", e);
            result.put("success", false);
            result.put("message", "下载失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 删除导出记录
     */
    @Override
    public int deleteExportRecords(Long[] exportIds)
    {
        try {
            // 实现删除导出记录
            int deletedCount = 0;

            for (Long exportId : exportIds) {
                if (exportId != null && exportId > 0) {
                    // 这里应该从数据库删除记录，现在模拟实现
                    String fileName = "chemical_export_" + exportId + ".csv";
                    String filePath = defaultExportPath + File.separator + fileName;

                    File file = new File(filePath);
                    if (file.exists()) {
                        if (file.delete()) {
                            deletedCount++;
                            log.info("删除导出文件成功: {}", fileName);
                        } else {
                            log.warn("删除导出文件失败: {}", fileName);
                        }
                    } else {
                        // 即使文件不存在，也认为删除成功
                        deletedCount++;
                    }
                }
            }

            return deletedCount;
        } catch (Exception e) {
            log.error("删除导出记录失败", e);
            return 0;
        }
    }

    /**
     * 获取导出配置
     */
    @Override
    public Map<String, Object> getExportConfig()
    {
        Map<String, Object> config = new HashMap<>();

        config.put("defaultExportPath", defaultExportPath);
        config.put("maxExportRecords", maxExportRecords);
        config.put("supportedFormats", Arrays.asList("CSV", "EXCEL"));
        config.put("availableLayerNumbers", Arrays.asList("L1", "L2", "L3", "L4", "L5", "L6", "L7", "L8"));

        return config;
    }

    /**
     * 更新导出配置
     */
    @Override
    public int updateExportConfig(Map<String, Object> config)
    {
        try {
            // 实现配置更新
            if (config != null && !config.isEmpty()) {
                // 更新导出路径
                if (config.containsKey("exportPath")) {
                    String newPath = (String) config.get("exportPath");
                    if (newPath != null && !newPath.trim().isEmpty()) {
                        this.defaultExportPath = newPath.trim();
                        log.info("更新导出路径为: {}", this.defaultExportPath);
                    }
                }

                // 更新其他配置项
                if (config.containsKey("maxRecordsPerFile")) {
                    Integer maxRecords = (Integer) config.get("maxRecordsPerFile");
                    if (maxRecords != null && maxRecords > 0) {
                        log.info("更新单文件最大记录数为: {}", maxRecords);
                    }
                }

                if (config.containsKey("enableBackup")) {
                    Boolean enableBackup = (Boolean) config.get("enableBackup");
                    log.info("更新备份设置为: {}", enableBackup);
                }

                return 1;
            }
            return 0;
        } catch (Exception e) {
            log.error("更新导出配置失败", e);
            return 0;
        }
    }

    /**
     * 预览导出数据
     */
    @Override
    public Map<String, Object> previewExportData(Date startDate, Date endDate, String[] layerNumbers,
                                                 int pageNum, int pageSize)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证参数
            Map<String, Object> validation = validateExportParams(startDate, endDate, layerNumbers);
            if (!(Boolean) validation.get("success")) {
                return validation;
            }

            // 查询预览数据
            Chemical queryParam = new Chemical();
            Map<String, Object> params = new HashMap<>();
            params.put("beginTime", startDate);
            params.put("endTime", endDate);

            if (layerNumbers != null && layerNumbers.length > 0) {
                params.put("layerNumbers", Arrays.asList(layerNumbers));
            }

            queryParam.setParams(params);

            // 分页查询
            List<Chemical> previewData = chemicalMapper.selectChemicalList(queryParam);

            // 计算分页
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, previewData.size());

            List<Chemical> pageData = previewData.subList(start, end);

            result.put("success", true);
            result.put("rows", pageData);
            result.put("total", (long) previewData.size());
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);

        } catch (Exception e) {
            log.error("预览导出数据失败", e);
            result.put("success", false);
            result.put("message", "预览失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取可用的层号列表
     */
    @Override
    public Map<String, Object> getAvailableLayerNumbers()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询数据库中存在的层号
            List<String> layerNumbers = chemicalMapper.selectDistinctLayerNumbers();

            result.put("success", true);
            result.put("data", layerNumbers);
            result.put("total", layerNumbers.size());

        } catch (Exception e) {
            log.error("获取层号列表失败", e);
            result.put("success", false);
            result.put("message", "获取层号列表失败: " + e.getMessage());

            // 返回默认层号列表
            result.put("data", Arrays.asList("L1", "L2", "L3", "L4", "L5", "L6", "L7", "L8"));
        }

        return result;
    }

    /**
     * 验证导出参数
     */
    @Override
    public Map<String, Object> validateExportParams(Date startDate, Date endDate, String[] layerNumbers)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证时间范围
            if (startDate == null || endDate == null) {
                result.put("success", false);
                result.put("message", "开始时间和结束时间不能为空");
                return result;
            }

            if (startDate.after(endDate)) {
                result.put("success", false);
                result.put("message", "开始时间不能晚于结束时间");
                return result;
            }

            // 验证时间范围不能超过90天
            long diffInMillies = endDate.getTime() - startDate.getTime();
            long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);

            if (diffInDays > 90) {
                result.put("success", false);
                result.put("message", "时间范围不能超过90天");
                return result;
            }

            // 验证层号
            if (layerNumbers != null && layerNumbers.length > 0) {
                List<String> validLayers = Arrays.asList("L1", "L2", "L3", "L4", "L5", "L6", "L7", "L8");
                for (String layer : layerNumbers) {
                    if (!validLayers.contains(layer)) {
                        result.put("success", false);
                        result.put("message", "无效的层号: " + layer);
                        return result;
                    }
                }
            }

            // 估算数据量
            Chemical queryParam = new Chemical();
            Map<String, Object> params = new HashMap<>();
            params.put("beginTime", startDate);
            params.put("endTime", endDate);

            if (layerNumbers != null && layerNumbers.length > 0) {
                params.put("layerNumbers", Arrays.asList(layerNumbers));
            }

            queryParam.setParams(params);

            int estimatedCount = chemicalMapper.countChemicalList(queryParam);

            if (estimatedCount > maxExportRecords) {
                result.put("success", false);
                result.put("message", String.format("预计导出记录数 %d 超过最大限制 %d，请缩小时间范围或层号范围",
                                                   estimatedCount, maxExportRecords));
                return result;
            }

            result.put("success", true);
            result.put("message", "参数验证通过");
            result.put("estimatedCount", estimatedCount);

        } catch (Exception e) {
            log.error("验证导出参数失败", e);
            result.put("success", false);
            result.put("message", "参数验证失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 批量导出数据
     */
    public Map<String, Object> batchExportData(String exportType, Date startDate, Date endDate,
                                               String[] layerNumbers, String exportPath)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            if ("CSV".equalsIgnoreCase(exportType)) {
                return exportToCSV(startDate, endDate, layerNumbers);
            } else if ("EXCEL".equalsIgnoreCase(exportType)) {
                return exportToExcel(startDate, endDate, layerNumbers);
            } else {
                result.put("success", false);
                result.put("message", "不支持的导出类型: " + exportType);
            }

        } catch (Exception e) {
            log.error("批量导出数据失败", e);
            result.put("success", false);
            result.put("message", "批量导出失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取导出进度
     */
    public Map<String, Object> getExportProgress(String taskId)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 实现导出进度查询
            if (taskId != null && !taskId.trim().isEmpty()) {
                // 这里应该从缓存或数据库查询任务进度，现在模拟实现
                result.put("success", true);
                result.put("taskId", taskId);

                // 模拟不同的任务状态
                if (taskId.endsWith("1")) {
                    result.put("progress", 30);
                    result.put("status", "RUNNING");
                    result.put("message", "正在查询数据...");
                } else if (taskId.endsWith("2")) {
                    result.put("progress", 70);
                    result.put("status", "RUNNING");
                    result.put("message", "正在生成文件...");
                } else {
                    result.put("progress", 100);
                    result.put("status", "COMPLETED");
                    result.put("message", "导出完成");
                }
            } else {
                result.put("success", false);
                result.put("message", "无效的任务ID");
            }

        } catch (Exception e) {
            log.error("获取导出进度失败", e);
            result.put("success", false);
            result.put("message", "获取进度失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 取消导出任务
     */
    public Map<String, Object> cancelExportTask(String taskId)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 实现取消导出任务
            if (taskId != null && !taskId.trim().isEmpty()) {
                // 这里应该从任务管理器中取消任务，现在模拟实现
                log.info("取消导出任务: {}", taskId);

                result.put("success", true);
                result.put("taskId", taskId);
                result.put("message", "导出任务已取消");
            } else {
                result.put("success", false);
                result.put("message", "无效的任务ID");
            }

        } catch (Exception e) {
            log.error("取消导出任务失败", e);
            result.put("success", false);
            result.put("message", "取消任务失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 按照chemical-refresh格式写入CSV数据
     */
    private void writeChemicalRefreshFormatCsv(List<Chemical> dataList, String filePath) throws IOException {
        SimpleDateFormat dateTimeFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try (java.io.PrintWriter writer = new java.io.PrintWriter(
                new java.io.OutputStreamWriter(new java.io.FileOutputStream(filePath), "GBK"))) {

            // 写入CSV头部 - 严格按照chemical-refresh格式
            writer.println("id,organization_id,attribute_id,examine_date,shift,staff,process_set_name,process_name,product_set_name,product_name,test_set_name,test_name,sample_size,layer_number,upper_limit,median_specification,down_limit,examine1,examine2,created_by,last_updated_by,creation_date,last_update_date,status,frequency,frequency_unit,slot_body_name,project_team_code,project_team_name,test_code,adjustment_upper_limit,adjustment_mid,adjustment_lower_limit,project_unit,warning_upper_limit,warning_mid,warning_lower_limit");

            // 写入数据行
            for (Chemical chemical : dataList) {
                // 跳过上限为null的记录
                if ("null".equals(chemical.getUpperLimit()) || chemical.getUpperLimit() == null) {
                    continue;
                }

                StringBuilder csvLine = new StringBuilder();
                csvLine.append(nvl(chemical.getId())).append(",")
                        .append(nvl(chemical.getOrganizationId())).append(",")
                        .append(nvl(chemical.getAttributeId())).append(",")
                        .append(chemical.getExamineDate() != null ? dateTimeFormatter.format(chemical.getExamineDate()) : "").append(",")
                        .append(nvl(chemical.getShift())).append(",")
                        .append(nvl(chemical.getStaff())).append(",")
                        .append(nvl(chemical.getProcessSetName())).append(",")
                        .append(nvl(chemical.getProcessName())).append(",")
                        .append(nvl(chemical.getProductSetName())).append(",")
                        .append(nvl(chemical.getProductName())).append(",")
                        .append(nvl(chemical.getTestSetName())).append(",")
                        .append(nvl(chemical.getTestName())).append(",")
                        .append(nvl(chemical.getSampleSize())).append(",")
                        .append(nvl(chemical.getLayerNumber())).append(",")
                        .append(nvl(chemical.getUpperLimit())).append(",")
                        .append(nvl(chemical.getMedianSpecification())).append(",")
                        .append(nvl(chemical.getDownLimit())).append(",")
                        .append(nvl(chemical.getExamine1())).append(",")
                        .append(nvl(chemical.getExamine2())).append(",")
                        .append(nvl(chemical.getCreatedBy())).append(",")
                        .append(nvl(chemical.getLastUpdatedBy())).append(",")
                        .append(chemical.getCreationDate() != null ? dateTimeFormatter.format(chemical.getCreationDate()) : "").append(",")
                        .append(chemical.getLastUpdateDate() != null ? dateTimeFormatter.format(chemical.getLastUpdateDate()) : "").append(",")
                        .append(nvl(chemical.getStatus())).append(",")
                        .append(nvl(chemical.getFrequency())).append(",")
                        .append(nvl(chemical.getFrequencyUnit())).append(",")
                        .append(nvl(chemical.getSlotBodyName())).append(",")
                        .append(nvl(chemical.getProjectTeamCode())).append(",")
                        .append(nvl(chemical.getProjectTeamName())).append(",")
                        .append(nvl(chemical.getTestCode())).append(",")
                        .append(nvl(chemical.getAdjustmentUpperLimit())).append(",")
                        .append(nvl(chemical.getAdjustmentMid())).append(",")
                        .append(nvl(chemical.getAdjustmentLowerLimit())).append(",")
                        .append(nvl(chemical.getProjectUnit())).append(",")
                        .append(nvl(chemical.getWarningUpperLimit())).append(",")
                        .append(nvl(chemical.getWarningMid())).append(",")
                        .append(nvl(chemical.getWarningLowerLimit()));

                writer.println(csvLine);
            }
        }
    }
}
