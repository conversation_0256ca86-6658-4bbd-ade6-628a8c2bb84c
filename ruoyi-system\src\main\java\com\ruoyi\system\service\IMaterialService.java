package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.domain.Material;

import javax.servlet.http.HttpServletResponse;

/**
 * 材料信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IMaterialService 
{
    /**
     * 查询材料信息
     * 
     * @param materialId 材料信息主键
     * @return 材料信息
     */
    public Material selectMaterialByMaterialId(Long materialId);

    /**
     * 查询材料信息列表
     * 
     * @param material 材料信息
     * @return 材料信息集合
     */
    public List<Material> selectMaterialList(Material material);

    /**
     * 新增材料信息
     * 
     * @param material 材料信息
     * @return 结果
     */
    public int insertMaterial(Material material);

    /**
     * 修改材料信息
     * 
     * @param material 材料信息
     * @return 结果
     */
    public int updateMaterial(Material material);

    /**
    /**
     * 批量删除材料信息
     *
     * @param materialIds 需要删除的材料信息主键集合
     * @return 结果
     */
    public int deleteMaterialByMaterialIds(Long[] materialIds);





    /**
     * 删除材料信息信息
     *
     * @param materialId 材料信息主键
     * @return 结果
     */
    public int deleteMaterialByMaterialId(Long materialId);
    
    /**
     * 获取材料名称选项
     *
     * @return 材料名称列表
     */
    public List<String> selectMaterialNameOptions();
    
    /**
     * 获取供应商名称选项
     *
     * @return 供应商名称列表
     */
    public List<String> selectSupplierNameOptions();


    /**
     * 获取材料选项数据
     * 
     * @param material 材料
     * @return 选项列表
     */
    public List<String> selectMaterialOptions(Material material);

    /**
     * 获取完整导出数据（材料+参数组+参数明细）
     * 
     * @param material 材料查询条件
     * @return 完整数据列表
     */
    public List<Map<String, Object>> selectCompleteExportData(Material material);

    /**
     * 获取材料选项数据
     * 
     * @param type 选项类型
     * @return 选项列表
     */
    public List<String> selectMaterialOptions(String type);

    /**
     * 整体导出数据
     * 
     * @param response HTTP响应
     * @param material 材料查询条件
     */
    public void exportCompleteData(javax.servlet.http.HttpServletResponse response, Material material);
}
