-- 测试结果数据生成脚本
-- 根据新的数据录入结构生成测试结果数据

-- 插入测试结果数据
-- 使用新的结构：plan_group_id, test_param_id, group_id

-- 1. 碳纤维复合材料 T700-12K 的测试结果
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 拉伸性能测试结果
(1, 1, 1, 1500.0, 1485.2, '拉伸强度测试，符合标准', 'admin', NOW()),
(1, 2, 1, 150.0, 148.5, '拉伸模量测试，性能良好', 'admin', NOW()),
(1, 3, 1, 1.2, 1.15, '断裂伸长率测试', 'admin', NOW()),
(1, 4, 1, 0.3, 0.31, '泊松比测试', 'admin', NOW()),

-- 弯曲性能测试结果
(2, 5, 1, 1200.0, 1195.8, '弯曲强度测试', 'admin', NOW()),
(2, 6, 1, 120.0, 118.9, '弯曲模量测试', 'admin', NOW()),
(2, 7, 1, 1.5, 1.48, '最大弯曲应变测试', 'admin', NOW()),

-- 冲击性能测试结果
(3, 8, 1, 85.0, 82.5, '冲击强度测试', 'admin', NOW()),
(3, 9, 1, 45.0, 43.8, '冲击韧性测试', 'admin', NOW()),

-- 热变形温度测试结果
(5, 11, 1, 180.0, 178.5, '热变形温度测试', 'admin', NOW()),
(5, 12, 1, 1.8, 1.8, '载荷测试', 'admin', NOW()),

-- 2. 玻璃纤维复合材料 E-Glass-2400 的测试结果
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 拉伸性能测试结果
(1, 1, 4, 800.0, 785.6, '拉伸强度测试', 'admin', NOW()),
(1, 2, 4, 45.0, 44.2, '拉伸模量测试', 'admin', NOW()),
(1, 3, 4, 2.5, 2.48, '断裂伸长率测试', 'admin', NOW()),

-- 弯曲性能测试结果
(2, 5, 4, 650.0, 642.3, '弯曲强度测试', 'admin', NOW()),
(2, 6, 4, 40.0, 39.5, '弯曲模量测试', 'admin', NOW()),

-- 冲击性能测试结果
(3, 8, 4, 120.0, 118.5, '冲击强度测试', 'admin', NOW()),
(3, 9, 4, 65.0, 63.2, '冲击韧性测试', 'admin', NOW()),

-- 3. 芳纶纤维复合材料 Kevlar-49 的测试结果
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 拉伸性能测试结果
(1, 1, 6, 2800.0, 2765.4, '拉伸强度测试，高性能材料', 'admin', NOW()),
(1, 2, 6, 130.0, 128.7, '拉伸模量测试', 'admin', NOW()),
(1, 3, 6, 2.8, 2.75, '断裂伸长率测试', 'admin', NOW()),

-- 冲击性能测试结果
(3, 8, 6, 180.0, 175.8, '冲击强度测试，优异性能', 'admin', NOW()),
(3, 9, 6, 95.0, 92.3, '冲击韧性测试', 'admin', NOW()),

-- 4. 中复神鹰 SYT49-12K 的测试结果
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 拉伸性能测试结果
(1, 1, 8, 1450.0, 1438.9, '拉伸强度测试', 'admin', NOW()),
(1, 2, 8, 145.0, 143.2, '拉伸模量测试', 'admin', NOW()),
(1, 3, 8, 1.1, 1.08, '断裂伸长率测试', 'admin', NOW()),

-- 弯曲性能测试结果
(2, 5, 8, 1180.0, 1172.5, '弯曲强度测试', 'admin', NOW()),
(2, 6, 8, 115.0, 113.8, '弯曲模量测试', 'admin', NOW()),

-- 5. 威海光威 GW-T300 的测试结果
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 拉伸性能测试结果
(1, 1, 11, 1350.0, 1342.8, '拉伸强度测试', 'admin', NOW()),
(1, 2, 11, 135.0, 133.5, '拉伸模量测试', 'admin', NOW()),
(1, 3, 11, 1.0, 0.98, '断裂伸长率测试', 'admin', NOW()),

-- 热变形温度测试结果
(5, 11, 11, 175.0, 173.2, '热变形温度测试', 'admin', NOW()),

-- 6. 环氧树脂的测试结果
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 拉伸性能测试结果
(1, 1, 13, 75.0, 73.8, '拉伸强度测试', 'admin', NOW()),
(1, 2, 13, 3.2, 3.15, '拉伸模量测试', 'admin', NOW()),

-- 介电性能测试结果
(8, 19, 13, 3.8, 3.75, '介电常数测试', 'admin', NOW()),
(8, 20, 13, 0.02, 0.021, '介电损耗测试', 'admin', NOW()),

-- 密度测试结果
(10, 22, 13, 1.15, 1.148, '密度测试', 'admin', NOW()),

-- 7. 聚酰亚胺树脂的测试结果
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 热变形温度测试结果
(5, 11, 14, 280.0, 275.8, '热变形温度测试，高温性能优异', 'admin', NOW()),

-- 玻璃化转变温度测试结果
(6, 13, 14, 320.0, 318.5, '玻璃化转变温度测试', 'admin', NOW()),

-- 介电性能测试结果
(8, 19, 14, 3.5, 3.48, '介电常数测试', 'admin', NOW()),
(8, 20, 14, 0.008, 0.0082, '介电损耗测试，低损耗', 'admin', NOW()),

-- 8. 添加更多变化的测试数据（模拟批次差异）
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 碳纤维T700第二批次测试
(1, 1, 1, 1500.0, 1492.8, '第二批次拉伸强度测试', 'admin', DATE_SUB(NOW(), INTERVAL 7 DAY)),
(1, 2, 1, 150.0, 149.2, '第二批次拉伸模量测试', 'admin', DATE_SUB(NOW(), INTERVAL 7 DAY)),
(2, 5, 1, 1200.0, 1188.5, '第二批次弯曲强度测试', 'admin', DATE_SUB(NOW(), INTERVAL 7 DAY)),

-- 玻璃纤维第二批次测试
(1, 1, 4, 800.0, 792.3, '第二批次拉伸强度测试', 'admin', DATE_SUB(NOW(), INTERVAL 5 DAY)),
(1, 2, 4, 45.0, 45.8, '第二批次拉伸模量测试', 'admin', DATE_SUB(NOW(), INTERVAL 5 DAY)),
(3, 8, 4, 120.0, 115.8, '第二批次冲击强度测试', 'admin', DATE_SUB(NOW(), INTERVAL 5 DAY)),

-- 芳纶纤维第二批次测试
(1, 1, 6, 2800.0, 2785.6, '第二批次拉伸强度测试', 'admin', DATE_SUB(NOW(), INTERVAL 3 DAY)),
(1, 3, 6, 2.8, 2.82, '第二批次断裂伸长率测试', 'admin', DATE_SUB(NOW(), INTERVAL 3 DAY)),

-- 9. 添加第三批次测试数据（模拟时间序列）
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time) VALUES
-- 碳纤维T700第三批次测试
(1, 1, 1, 1500.0, 1478.5, '第三批次拉伸强度测试', 'admin', DATE_SUB(NOW(), INTERVAL 14 DAY)),
(1, 2, 1, 150.0, 147.8, '第三批次拉伸模量测试', 'admin', DATE_SUB(NOW(), INTERVAL 14 DAY)),
(5, 11, 1, 180.0, 179.2, '第三批次热变形温度测试', 'admin', DATE_SUB(NOW(), INTERVAL 14 DAY)),

-- 中复神鹰第二批次测试
(1, 1, 8, 1450.0, 1445.2, '第二批次拉伸强度测试', 'admin', DATE_SUB(NOW(), INTERVAL 10 DAY)),
(2, 5, 8, 1180.0, 1175.8, '第二批次弯曲强度测试', 'admin', DATE_SUB(NOW(), INTERVAL 10 DAY)),

-- 威海光威第二批次测试
(1, 1, 11, 1350.0, 1355.6, '第二批次拉伸强度测试，性能提升', 'admin', DATE_SUB(NOW(), INTERVAL 8 DAY)),
(1, 2, 11, 135.0, 136.2, '第二批次拉伸模量测试', 'admin', DATE_SUB(NOW(), INTERVAL 8 DAY));

-- 10. 验证数据插入结果
SELECT 
    COUNT(*) as '测试结果总数',
    COUNT(DISTINCT plan_group_id) as '涉及测试方案组数',
    COUNT(DISTINCT test_param_id) as '涉及测试参数数',
    COUNT(DISTINCT group_id) as '涉及工艺参数组数'
FROM test_results;

-- 11. 显示各对比维度的数据分布
SELECT '=== 材料对比数据分布 ===' as '统计信息';
SELECT 
    m.material_name as '材料名称',
    m.supplier_name as '供应商',
    COUNT(tr.test_result_id) as '测试记录数',
    AVG(tr.supplier_datasheet_val) as '供应商数据平均值',
    AVG(tr.test_value) as '测试数据平均值'
FROM test_results tr
JOIN process_param_group ppg ON tr.group_id = ppg.group_id
JOIN materials m ON ppg.material_id = m.material_id
GROUP BY m.material_id, m.material_name, m.supplier_name
ORDER BY COUNT(tr.test_result_id) DESC;

SELECT '=== 供应商对比数据分布 ===' as '统计信息';
SELECT 
    m.supplier_name as '供应商',
    COUNT(tr.test_result_id) as '测试记录数',
    COUNT(DISTINCT m.material_id) as '材料种类数',
    AVG(tr.supplier_datasheet_val) as '供应商数据平均值',
    AVG(tr.test_value) as '测试数据平均值'
FROM test_results tr
JOIN process_param_group ppg ON tr.group_id = ppg.group_id
JOIN materials m ON ppg.material_id = m.material_id
GROUP BY m.supplier_name
ORDER BY COUNT(tr.test_result_id) DESC;

SELECT '=== 测试方案组数据分布 ===' as '统计信息';
SELECT 
    tpg.plan_code as '方案编号',
    tpg.performance_name as '性能名称',
    COUNT(tr.test_result_id) as '测试记录数',
    AVG(tr.supplier_datasheet_val) as '供应商数据平均值',
    AVG(tr.test_value) as '测试数据平均值'
FROM test_results tr
JOIN test_plan_group tpg ON tr.plan_group_id = tpg.plan_group_id
GROUP BY tpg.plan_group_id, tpg.plan_code, tpg.performance_name
ORDER BY COUNT(tr.test_result_id) DESC;

SELECT '测试数据生成完成！' as '状态信息';
SELECT '数据包含多个批次、多种材料、多个供应商的完整测试记录' as '数据说明';
