<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.ChemicalYsMapper">
    
    <resultMap type="ChemicalYs" id="ChemicalYsResult">
        <result property="id"    column="id"    />
        <result property="organizationId"    column="organization_id"    />
        <result property="attributeId"    column="attribute_id"    />
        <result property="examineDate"    column="examine_date"    />
        <result property="shift"    column="shift"    />
        <result property="staff"    column="staff"    />
        <result property="departmentCode"    column="department_code"    />
        <result property="processSetName"    column="process_set_name"    />
        <result property="processName"    column="process_name"    />
        <result property="productSetName"    column="product_set_name"    />
        <result property="productName"    column="product_name"    />
        <result property="testSetName"    column="test_set_name"    />
        <result property="testName"    column="test_name"    />
        <result property="sampleSize"    column="sample_size"    />
        <result property="layerNumber"    column="layer_number"    />
        <result property="upperLimit"    column="upper_limit"    />
        <result property="medianSpecification"    column="median_specification"    />
        <result property="downLimit"    column="down_limit"    />
        <result property="examine1"    column="examine1"    />
        <result property="examine1Ys"    column="examine1_ys"    />
        <result property="examine1Zs"    column="examine1_zs"    />
        <result property="examine2"    column="examine2"    />
        <result property="isModified"    column="is_modified"    />
        <result property="originalExamine1"    column="original_examine1"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectChemicalYsVo">
        select id, organization_id, attribute_id, examine_date, shift, staff, department_code,
               process_set_name, process_name, product_set_name, product_name, test_set_name, test_name,
               sample_size, layer_number, upper_limit, median_specification, down_limit,
               examine1, examine1_ys, examine1_zs, examine2, is_modified, original_examine1,
               create_time, update_time
        from chemical_ys
    </sql>

    <select id="selectChemicalYsList" parameterType="ChemicalYs" resultMap="ChemicalYsResult">
        <include refid="selectChemicalYsVo"/>
        <where>  
            <if test="id != null and id != ''"> and id = #{id}</if>
            <if test="examineDate != null"> and examine_date = #{examineDate}</if>
            <if test="processName != null and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="productName != null and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="testName != null and testName != ''"> and test_name like concat('%', #{testName}, '%')</if>
            <if test="layerNumber != null and layerNumber != ''"> and layer_number = #{layerNumber}</if>
        </where>
        order by examine_date desc
    </select>
    
    <select id="selectChemicalYsById" parameterType="String" resultMap="ChemicalYsResult">
        <include refid="selectChemicalYsVo"/>
        where id = #{id}
    </select>

    <select id="selectChemicalYsByDateRange" resultMap="ChemicalYsResult">
        <include refid="selectChemicalYsVo"/>
        <where>
            <if test="startDate != null and endDate != null">
                and examine_date between #{startDate} and #{endDate}
            </if>
            <if test="layerNumbers != null and layerNumbers.length > 0">
                and layer_number in
                <foreach item="layerNumber" collection="layerNumbers" open="(" separator="," close=")">
                    #{layerNumber}
                </foreach>
            </if>
        </where>
        order by examine_date desc
    </select>
        
    <insert id="insertChemicalYs" parameterType="ChemicalYs">
        insert into chemical_ys
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="organizationId != null">organization_id,</if>
            <if test="attributeId != null">attribute_id,</if>
            <if test="examineDate != null">examine_date,</if>
            <if test="shift != null and shift != ''">shift,</if>
            <if test="staff != null and staff != ''">staff,</if>
            <if test="departmentCode != null and departmentCode != ''">department_code,</if>
            <if test="processSetName != null and processSetName != ''">process_set_name,</if>
            <if test="processName != null and processName != ''">process_name,</if>
            <if test="productSetName != null and productSetName != ''">product_set_name,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="testSetName != null and testSetName != ''">test_set_name,</if>
            <if test="testName != null and testName != ''">test_name,</if>
            <if test="sampleSize != null and sampleSize != ''">sample_size,</if>
            <if test="layerNumber != null and layerNumber != ''">layer_number,</if>
            <if test="upperLimit != null and upperLimit != ''">upper_limit,</if>
            <if test="medianSpecification != null and medianSpecification != ''">median_specification,</if>
            <if test="downLimit != null and downLimit != ''">down_limit,</if>
            <if test="examine1 != null and examine1 != ''">examine1,</if>
            <if test="examine1Ys != null and examine1Ys != ''">examine1_ys,</if>
            <if test="examine1Zs != null and examine1Zs != ''">examine1_zs,</if>
            <if test="examine2 != null and examine2 != ''">examine2,</if>
            <if test="isModified != null">is_modified,</if>
            <if test="originalExamine1 != null">original_examine1,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="organizationId != null">#{organizationId},</if>
            <if test="attributeId != null">#{attributeId},</if>
            <if test="examineDate != null">#{examineDate},</if>
            <if test="shift != null and shift != ''">#{shift},</if>
            <if test="staff != null and staff != ''">#{staff},</if>
            <if test="departmentCode != null and departmentCode != ''">#{departmentCode},</if>
            <if test="processSetName != null and processSetName != ''">#{processSetName},</if>
            <if test="processName != null and processName != ''">#{processName},</if>
            <if test="productSetName != null and productSetName != ''">#{productSetName},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="testSetName != null and testSetName != ''">#{testSetName},</if>
            <if test="testName != null and testName != ''">#{testName},</if>
            <if test="sampleSize != null and sampleSize != ''">#{sampleSize},</if>
            <if test="layerNumber != null and layerNumber != ''">#{layerNumber},</if>
            <if test="upperLimit != null and upperLimit != ''">#{upperLimit},</if>
            <if test="medianSpecification != null and medianSpecification != ''">#{medianSpecification},</if>
            <if test="downLimit != null and downLimit != ''">#{downLimit},</if>
            <if test="examine1 != null and examine1 != ''">#{examine1},</if>
            <if test="examine1Ys != null and examine1Ys != ''">#{examine1Ys},</if>
            <if test="examine1Zs != null and examine1Zs != ''">#{examine1Zs},</if>
            <if test="examine2 != null and examine2 != ''">#{examine2},</if>
            <if test="isModified != null">#{isModified},</if>
            <if test="originalExamine1 != null">#{originalExamine1},</if>
            getdate()
        </trim>
    </insert>

    <update id="updateChemicalYs" parameterType="ChemicalYs">
        update chemical_ys
        <trim prefix="SET" suffixOverrides=",">
            <if test="organizationId != null">organization_id = #{organizationId},</if>
            <if test="attributeId != null">attribute_id = #{attributeId},</if>
            <if test="examineDate != null">examine_date = #{examineDate},</if>
            <if test="shift != null and shift != ''">shift = #{shift},</if>
            <if test="staff != null and staff != ''">staff = #{staff},</if>
            <if test="departmentCode != null and departmentCode != ''">department_code = #{departmentCode},</if>
            <if test="processSetName != null and processSetName != ''">process_set_name = #{processSetName},</if>
            <if test="processName != null and processName != ''">process_name = #{processName},</if>
            <if test="productSetName != null and productSetName != ''">product_set_name = #{productSetName},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="testSetName != null and testSetName != ''">test_set_name = #{testSetName},</if>
            <if test="testName != null and testName != ''">test_name = #{testName},</if>
            <if test="sampleSize != null and sampleSize != ''">sample_size = #{sampleSize},</if>
            <if test="layerNumber != null and layerNumber != ''">layer_number = #{layerNumber},</if>
            <if test="upperLimit != null and upperLimit != ''">upper_limit = #{upperLimit},</if>
            <if test="medianSpecification != null and medianSpecification != ''">median_specification = #{medianSpecification},</if>
            <if test="downLimit != null and downLimit != ''">down_limit = #{downLimit},</if>
            <if test="examine1 != null and examine1 != ''">examine1 = #{examine1},</if>
            <if test="examine1Ys != null and examine1Ys != ''">examine1_ys = #{examine1Ys},</if>
            <if test="examine1Zs != null and examine1Zs != ''">examine1_zs = #{examine1Zs},</if>
            <if test="examine2 != null and examine2 != ''">examine2 = #{examine2},</if>
            <if test="isModified != null">is_modified = #{isModified},</if>
            <if test="originalExamine1 != null">original_examine1 = #{originalExamine1},</if>
            update_time = getdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteChemicalYsById" parameterType="String">
        delete from chemical_ys where id = #{id}
    </delete>

    <delete id="deleteChemicalYsByIds" parameterType="String">
        delete from chemical_ys where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
