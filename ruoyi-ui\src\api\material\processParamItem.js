import request from '@/utils/request'

// 查询工艺参数明细列表
export function listProcessParamItem(query) {
  return request({
    url: '/material/processParamItem/list',
    method: 'get',
    params: query
  })
}

// 根据参数组ID查询工艺参数明细列表
export function listByGroupId(groupId) {
  return request({
    url: '/material/processParamItem/listByGroupId/' + groupId,
    method: 'get'
  })
}

// 查询工艺参数明细详细
export function getProcessParamItem(itemId) {
  return request({
    url: '/material/processParamItem/' + itemId,
    method: 'get'
  })
}

// 新增工艺参数明细
export function addProcessParamItem(data) {
  return request({
    url: '/material/processParamItem',
    method: 'post',
    data: data
  })
}

// 修改工艺参数明细
export function updateProcessParamItem(data) {
  return request({
    url: '/material/processParamItem',
    method: 'put',
    data: data
  })
}

// 删除工艺参数明细
export function delProcessParamItem(itemId) {
  return request({
    url: '/material/processParamItem/' + itemId,
    method: 'delete'
  })
}

// 导出工艺参数明细
// 导出工艺参数明细
export function exportProcessParamItem(query) {
  return request({
    url: '/material/processParamItem/export',
    method: 'post',
    data: query
  })
}

// 获取参数明细选项数据
export function getProcessParamItemOptions(query) {
  return request({
    url: '/material/processParamItem/options',
    method: 'get',
    params: query
  })
}

// 上传附件
export function uploadItemAttachment(data) {
  return request({
    url: '/material/processParamItem/upload',
    method: 'post',
    data: data
  })
}

// 下载附件
export function downloadItemAttachment(fileName) {
  return request({
    url: '/material/processParamItem/download/' + fileName,
    method: 'get',
    responseType: 'blob'
  })
}
