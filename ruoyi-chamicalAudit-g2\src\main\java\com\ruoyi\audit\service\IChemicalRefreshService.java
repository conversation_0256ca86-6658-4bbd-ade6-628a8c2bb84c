package com.ruoyi.audit.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import com.ruoyi.audit.domain.ChemicalRefreshTask;

/**
 * 化学数据刷新Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IChemicalRefreshService 
{
    /**
     * 执行数据刷新任务
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param layerNumbers 层号列表
     * @param exportPath 导出路径
     * @param backupPath 备份路径
     * @return 刷新任务结果
     */
    public Map<String, Object> executeRefreshTask(Date startDate, Date endDate, 
                                                  String[] layerNumbers, String exportPath, String backupPath);

    /**
     * 查询刷新任务列表
     * 
     * @param refreshTask 查询条件
     * @return 刷新任务列表
     */
    public List<ChemicalRefreshTask> selectRefreshTaskList(ChemicalRefreshTask refreshTask);

    /**
     * 查询刷新任务详情
     * 
     * @param refreshId 任务ID
     * @return 刷新任务详情
     */
    public ChemicalRefreshTask selectRefreshTaskById(Long refreshId);

    /**
     * 新增刷新任务记录
     * 
     * @param refreshTask 刷新任务
     * @return 结果
     */
    public int insertRefreshTask(ChemicalRefreshTask refreshTask);

    /**
     * 更新刷新任务记录
     * 
     * @param refreshTask 刷新任务
     * @return 结果
     */
    public int updateRefreshTask(ChemicalRefreshTask refreshTask);

    /**
     * 删除刷新任务记录
     * 
     * @param refreshIds 任务ID数组
     * @return 结果
     */
    public int deleteRefreshTaskByIds(Long[] refreshIds);

    /**
     * 获取最近的刷新任务
     * 
     * @param limit 查询数量限制
     * @return 最近的刷新任务列表
     */
    public List<ChemicalRefreshTask> getRecentRefreshTasks(int limit);

    /**
     * 检查是否有正在运行的刷新任务
     * 
     * @return 是否有正在运行的任务
     */
    public boolean hasRunningRefreshTask();

    /**
     * 停止正在运行的刷新任务
     * 
     * @return 结果
     */
    public Map<String, Object> stopRunningRefreshTask();

    /**
     * 获取刷新任务统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getRefreshTaskStatistics();

    /**
     * 导出刷新数据到CSV文件
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param layerNumbers 层号列表
     * @param exportPath 导出路径
     * @return 导出结果
     */
    public Map<String, Object> exportRefreshDataToCsv(Date startDate, Date endDate, 
                                                      String[] layerNumbers, String exportPath);

    /**
     * 处理化学数据刷新逻辑
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param layerNumbers 层号列表
     * @return 处理结果
     */
    public Map<String, Object> processChemicalDataRefresh(Date startDate, Date endDate, String[] layerNumbers);

    /**
     * 清理历史刷新任务记录
     * 
     * @param days 保留天数
     * @return 清理的记录数
     */
    public int cleanHistoryRefreshTasks(int days);
}
