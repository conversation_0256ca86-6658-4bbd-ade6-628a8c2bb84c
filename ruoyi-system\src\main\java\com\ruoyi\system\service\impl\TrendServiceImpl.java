package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TrendMapper;
import com.ruoyi.system.service.ITrendService;

/**
 * 趋势对比Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class TrendServiceImpl implements ITrendService 
{
    @Autowired
    private TrendMapper trendMapper;

    /**
     * 获取趋势对比数据
     * 
     * @param paramNumbers 参数编号列表（逗号分隔）
     * @param performanceNames 性能名称列表（逗号分隔）
     * @return 趋势数据
     */
    @Override
    public Map<String, Object> getTrendData(String paramNumbers, String performanceNames)
    {
        Map<String, Object> result = new HashMap<>();
        
        // 解析参数编号和性能名称
        String[] paramArray = paramNumbers != null ? paramNumbers.split(",") : new String[0];
        String[] performanceArray = performanceNames != null ? performanceNames.split(",") : new String[0];
        
        // 获取趋势数据
        List<Map<String, Object>> trendData = trendMapper.selectTrendData(paramArray, performanceArray);
        
        // 组织数据结构
        Map<String, List<Map<String, Object>>> groupedData = new HashMap<>();
        for (Map<String, Object> data : trendData) {
            String paramNumber = (String) data.get("paramNumber");
            if (!groupedData.containsKey(paramNumber)) {
                groupedData.put(paramNumber, new ArrayList<>());
            }
            groupedData.get(paramNumber).add(data);
        }
        
        result.put("data", groupedData);
        result.put("paramNumbers", paramArray);
        result.put("performanceNames", performanceArray);
        
        return result;
    }

    /**
     * 获取所有参数编号
     * 
     * @return 参数编号列表
     */
    @Override
    public List<String> getParamNumbers()
    {
        return trendMapper.selectParamNumbers();
    }

    /**
     * 获取所有性能名称
     * 
     * @return 性能名称列表
     */
    @Override
    public List<String> getPerformanceNames()
    {
        return trendMapper.selectPerformanceNames();
    }

    /**
     * 根据参数编号获取参数详情
     * 
     * @param paramNumber 参数编号
     * @return 参数详情
     */
    @Override
    public Map<String, Object> getParamDetails(String paramNumber)
    {
        return trendMapper.selectParamDetails(paramNumber);
    }
}