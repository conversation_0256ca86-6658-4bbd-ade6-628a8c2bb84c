package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ProcessParamGroup;
import com.ruoyi.system.domain.ProcessParamItem;
import java.util.Map;

/**
 * 工艺参数组Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProcessParamGroupMapper 
{
    /**
     * 查询工艺参数组
     * 
     * @param groupId 工艺参数组主键
     * @return 工艺参数组
     */
    public ProcessParamGroup selectProcessParamGroupByGroupId(Long groupId);

    /**
     * 查询工艺参数组列表
     * 
     * @param processParamGroup 工艺参数组
     * @return 工艺参数组集合
     */
    public List<ProcessParamGroup> selectProcessParamGroupList(ProcessParamGroup processParamGroup);

    /**
     * 根据材料ID查询工艺参数组列表
     * 
     * @param materialId 材料ID
     * @return 工艺参数组集合
     */
    public List<ProcessParamGroup> selectProcessParamGroupByMaterialId(Long materialId);

    /**
     * 新增工艺参数组
     * 
     * @param processParamGroup 工艺参数组
     * @return 结果
     */
    public int insertProcessParamGroup(ProcessParamGroup processParamGroup);

    /**
     * 修改工艺参数组
     * 
     * @param processParamGroup 工艺参数组
     * @return 结果
     */
    public int updateProcessParamGroup(ProcessParamGroup processParamGroup);

    /**
     * 删除工艺参数组
     * 
     * @param groupId 工艺参数组主键
     * @return 结果
     */
    public int deleteProcessParamGroupByGroupId(Long groupId);

    /**
     * 批量删除工艺参数组
     * 
     * @param groupIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcessParamGroupByGroupIds(Long[] groupIds);

    /**
     * 批量删除工艺参数明细
     * 
     * @param groupIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcessParamItemByGroupIds(Long[] groupIds);
    
    /**
    /**
     * 批量新增工艺参数明细
     *
     * @param processParamItemList 工艺参数明细列表
     * @return 结果
     */
    public int batchProcessParamItem(List<ProcessParamItem> processParamItemList);

    /**
     * 获取工艺类型选项
     *
     * @return 工艺类型列表
     */
    public List<String> selectProcessTypeOptions();

    /**
     * 获取参数编号选项
     *
     * @return 参数编号列表
     */
    public List<String> selectParamNumberOptions();

    
    /**
     * 根据材料ID获取完整数据用于导出
     *
     * @param materialId 材料ID
     * @return 完整数据列表
     */
    public List<Map<String, Object>> selectCompleteDataByMaterialId(Long materialId);

    /**
     * 通过参数组主键删除工艺参数明细信息
     * 
     * @param groupId 参数组ID
     * @return 结果
     */
    public int deleteProcessParamItemByGroupId(Long groupId);
}
