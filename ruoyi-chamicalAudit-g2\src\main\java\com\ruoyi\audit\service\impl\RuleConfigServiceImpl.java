package com.ruoyi.audit.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.audit.mapper.RuleConfigMapper;
import com.ruoyi.audit.domain.RuleConfig;
import com.ruoyi.audit.service.IRuleConfigService;

/**
 * 数据刷新规则配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class RuleConfigServiceImpl implements IRuleConfigService 
{
    @Autowired
    private RuleConfigMapper ruleConfigMapper;

    /**
     * 查询数据刷新规则配置
     * 
     * @param id 数据刷新规则配置主键
     * @return 数据刷新规则配置
     */
    @Override
    public RuleConfig selectRuleConfigById(Long id)
    {
        return ruleConfigMapper.selectRuleConfigById(id);
    }

    /**
     * 查询数据刷新规则配置列表
     * 
     * @param ruleConfig 数据刷新规则配置
     * @return 数据刷新规则配置
     */
    @Override
    public List<RuleConfig> selectRuleConfigList(RuleConfig ruleConfig)
    {
        return ruleConfigMapper.selectRuleConfigList(ruleConfig);
    }

    /**
     * 根据产品、过程、测试名称查询规则配置
     * 
     * @param productName 产品名称
     * @param processName 过程名称
     * @param testName 测试名称
     * @return 数据刷新规则配置
     */
    @Override
    public RuleConfig selectRuleConfigByNames(String productName, String processName, String testName)
    {
        return ruleConfigMapper.selectRuleConfigByNames(productName, processName, testName);
    }

    /**
     * 新增数据刷新规则配置
     * 
     * @param ruleConfig 数据刷新规则配置
     * @return 结果
     */
    @Override
    public int insertRuleConfig(RuleConfig ruleConfig)
    {
        ruleConfig.setCreatedTime(new Date());
        ruleConfig.setUpdatedTime(new Date());
        if (ruleConfig.getStatus() == null) {
            ruleConfig.setStatus("1"); // 默认启用
        }
        return ruleConfigMapper.insertRuleConfig(ruleConfig);
    }

    /**
     * 修改数据刷新规则配置
     * 
     * @param ruleConfig 数据刷新规则配置
     * @return 结果
     */
    @Override
    public int updateRuleConfig(RuleConfig ruleConfig)
    {
        ruleConfig.setUpdatedTime(new Date());
        return ruleConfigMapper.updateRuleConfig(ruleConfig);
    }

    /**
     * 批量删除数据刷新规则配置
     * 
     * @param ids 需要删除的数据刷新规则配置主键
     * @return 结果
     */
    @Override
    public int deleteRuleConfigByIds(Long[] ids)
    {
        return ruleConfigMapper.deleteRuleConfigByIds(ids);
    }

    /**
     * 删除数据刷新规则配置信息
     * 
     * @param id 数据刷新规则配置主键
     * @return 结果
     */
    @Override
    public int deleteRuleConfigById(Long id)
    {
        return ruleConfigMapper.deleteRuleConfigById(id);
    }

    /**
     * 获取默认规则配置
     * 
     * @return 默认规则配置
     */
    @Override
    public RuleConfig getDefaultRuleConfig()
    {
        RuleConfig defaultConfig = new RuleConfig();
        defaultConfig.setEnableControlLimitAdjustment(true);
        defaultConfig.setEnableMovingRangeAdjustment(true);
        defaultConfig.setEnableNinePointSameSideCheck(true);
        defaultConfig.setEnableSixPointTrendCheck(true);
        defaultConfig.setEnableCpkAdjustment(true);
        defaultConfig.setCpkTarget(1.33);
        defaultConfig.setIsRefreshMode(false);
        defaultConfig.setStatus("1");
        return defaultConfig;
    }

    /**
     * 批量导入规则配置
     * 
     * @param ruleConfigList 规则配置列表
     * @return 导入结果
     */
    @Override
    public String importRuleConfig(List<RuleConfig> ruleConfigList)
    {
        if (ruleConfigList == null || ruleConfigList.isEmpty()) {
            return "导入数据为空";
        }

        int successCount = 0;
        int failureCount = 0;
        StringBuilder failureMsg = new StringBuilder();

        for (RuleConfig ruleConfig : ruleConfigList) {
            try {
                // 检查是否已存在相同配置
                RuleConfig existingConfig = selectRuleConfigByNames(
                    ruleConfig.getProductName(), 
                    ruleConfig.getProcessName(), 
                    ruleConfig.getTestName()
                );

                if (existingConfig != null) {
                    // 更新现有配置
                    ruleConfig.setId(existingConfig.getId());
                    updateRuleConfig(ruleConfig);
                } else {
                    // 新增配置
                    insertRuleConfig(ruleConfig);
                }
                successCount++;
            } catch (Exception e) {
                failureCount++;
                String msg = "<br/>" + failureCount + "、规则配置 " + ruleConfig.getProductName() + 
                           "-" + ruleConfig.getProcessName() + "-" + ruleConfig.getTestName() + 
                           " 导入失败：" + e.getMessage();
                failureMsg.append(msg);
            }
        }

        if (failureCount > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureCount + " 条数据格式不正确，错误如下：");
            return failureMsg.toString();
        } else {
            return "恭喜您，数据已全部导入成功！共 " + successCount + " 条，数据如下：";
        }
    }

    /**
     * 导出规则配置
     * 
     * @param ruleConfig 查询条件
     * @return 规则配置列表
     */
    @Override
    public List<RuleConfig> exportRuleConfig(RuleConfig ruleConfig)
    {
        return ruleConfigMapper.selectRuleConfigList(ruleConfig);
    }
}
