import request from '@/utils/request'

// 获取系统概览信息
export function getSystemOverview() {
  return request({
    url: '/chemical/monitor/overview',
    method: 'get'
  })
}

// 获取实时数据
export function getRealtimeData() {
  return request({
    url: '/chemical/monitor/realtime',
    method: 'get'
  })
}

// 获取任务监控列表
export function getTaskMonitorList(query) {
  return request({
    url: '/chemical/monitor/tasks',
    method: 'get',
    params: query
  })
}

// 获取运行中的任务
export function getRunningTasks() {
  return request({
    url: '/chemical/monitor/runningTasks',
    method: 'get'
  })
}

// 获取任务详细信息
export function getTaskDetail(taskId) {
  return request({
    url: '/chemical/monitor/task/' + taskId,
    method: 'get'
  })
}

// 获取最新任务记录
export function getLatestTask(taskType) {
  return request({
    url: '/chemical/monitor/latestTask',
    method: 'get',
    params: { taskType }
  })
}

// 获取实时日志
export function getRealtimeLogs(lastLogId) {
  return request({
    url: '/chemical/monitor/logs',
    method: 'get',
    params: { lastLogId }
  })
}

// 获取系统性能指标
export function getPerformanceMetrics() {
  return request({
    url: '/chemical/monitor/performance',
    method: 'get'
  })
}

// 获取数据处理趋势
export function getDataTrends(days) {
  return request({
    url: '/chemical/monitor/trends',
    method: 'get',
    params: { days }
  })
}

// 获取错误统计
export function getErrorStatistics(hours) {
  return request({
    url: '/chemical/monitor/errors',
    method: 'get',
    params: { hours }
  })
}

// 获取告警信息
export function getAlerts() {
  return request({
    url: '/chemical/monitor/alerts',
    method: 'get'
  })
}

// 清理历史任务记录
export function cleanHistoryTasks(days) {
  return request({
    url: '/chemical/monitor/cleanHistory',
    method: 'post',
    params: { days }
  })
}

// 导出监控报告
export function exportMonitorReport(days) {
  return request({
    url: '/chemical/monitor/exportReport',
    method: 'post',
    params: { days }
  })
}

// 重置任务状态
export function resetTaskStatus(taskId) {
  return request({
    url: '/chemical/monitor/resetTask/' + taskId,
    method: 'post'
  })
}

// 删除任务记录
export function removeTaskRecords(taskIds) {
  return request({
    url: '/chemical/monitor/' + taskIds,
    method: 'delete'
  })
}

// 清空日志
export function clearLogs() {
  return request({
    url: '/chemical/task/clearLogs',
    method: 'post'
  })
}
