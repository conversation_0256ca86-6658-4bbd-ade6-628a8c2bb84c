package com.ruoyi.audit.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.ruoyi.audit.domain.Chemical;
import com.ruoyi.audit.domain.RuleConfig;

/**
 * 数据刷新规则引擎
 * 实现5大数据刷新规则：
 * 1. 控制线内调整
 * 2. 移动极差调整
 * 3. 9点同侧检查
 * 4. 6点递变检查
 * 5. CPK值调整
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Component
public class DataRefreshRuleEngine 
{
    private static final Logger log = LoggerFactory.getLogger(DataRefreshRuleEngine.class);
    private static final Random random = new Random();

    /**
     * 控制限制参数
     */
    public static class ControlLimits {
        public double fMean;
        public double fSp;
        public double upperControlLimit;
        public double lowerControlLimit;
        
        public ControlLimits(double fMean, double fSp) {
            this.fMean = fMean;
            this.fSp = fSp;
            calculateControlLimits();
        }
        
        private void calculateControlLimits() {
            double threeSigma = 3 * fSp;
            this.upperControlLimit = fMean + threeSigma / 2;
            this.lowerControlLimit = fMean - threeSigma / 2;
        }
    }

    /**
     * 应用所有数据刷新规则
     * 
     * @param chemicals 化学数据列表
     * @param controlLimits 控制限制
     * @param ruleConfig 规则配置
     * @return 修改的记录数
     */
    public int applyAllRules(List<Chemical> chemicals, ControlLimits controlLimits, RuleConfig ruleConfig) {
        if (chemicals == null || chemicals.isEmpty()) {
            return 0;
        }

        int modifiedCount = 0;
        
        // 保存原始值
        for (Chemical chemical : chemicals) {
            if (chemical.getExamine1ZS() == null || chemical.getExamine1ZS().isEmpty()) {
                chemical.setExamine1ZS(chemical.getExamine1()); // 保存原始值
            }
        }

        // 1. 控制线内调整
        if (ruleConfig == null || ruleConfig.getEnableControlLimitAdjustment() == null || ruleConfig.getEnableControlLimitAdjustment()) {
            modifiedCount += applyControlLimitAdjustment(chemicals, controlLimits);
        }

        // 2. 移动极差调整
        if (ruleConfig == null || ruleConfig.getEnableMovingRangeAdjustment() == null || ruleConfig.getEnableMovingRangeAdjustment()) {
            modifiedCount += applyMovingRangeAdjustment(chemicals, controlLimits);
        }

        // 3. 9点同侧检查
        if (ruleConfig == null || ruleConfig.getEnableNinePointSameSideCheck() == null || ruleConfig.getEnableNinePointSameSideCheck()) {
            modifiedCount += applyNinePointSameSideCheck(chemicals, controlLimits);
        }

        // 4. 6点递变检查
        if (ruleConfig == null || ruleConfig.getEnableSixPointTrendCheck() == null || ruleConfig.getEnableSixPointTrendCheck()) {
            modifiedCount += applySixPointTrendCheck(chemicals);
        }

        // 5. CPK值调整
        if (ruleConfig == null || ruleConfig.getEnableCpkAdjustment() == null || ruleConfig.getEnableCpkAdjustment()) {
            double cpkTarget = (ruleConfig != null && ruleConfig.getCpkTarget() != null) ? ruleConfig.getCpkTarget() : 1.33;
            modifiedCount += applyCpkAdjustment(chemicals, cpkTarget, ruleConfig);
        }

        return modifiedCount;
    }

    /**
     * 规则1: 控制线内调整
     * 将超出控制限的数据调整到控制限内
     */
    private int applyControlLimitAdjustment(List<Chemical> chemicals, ControlLimits controlLimits) {
        int modifiedCount = 0;
        double upperAdjustmentRange = (controlLimits.upperControlLimit - controlLimits.fMean) * 0.5;
        double lowerAdjustmentRange = (controlLimits.fMean - controlLimits.lowerControlLimit) * 0.5;

        for (Chemical chemical : chemicals) {
            try {
                double examine1 = Double.parseDouble(chemical.getExamine1());
                double examine2 = parseDoubleWithDefault(chemical.getExamine2(), 0);
                boolean modified = false;

                if (examine1 > controlLimits.upperControlLimit) {
                    // 调整到上限至中值范围内的随机值
                    double adjustedValue = controlLimits.upperControlLimit - (random.nextDouble() * upperAdjustmentRange);
                    double adjustedExamine2 = examine2 != 0 ? Math.round(adjustedValue * examine2 / examine1 * 1000.0) / 1000.0 : 0;
                    
                    chemical.setExamine1(String.format("%.3f", adjustedValue));
                    chemical.setExamine2(String.format("%.3f", adjustedExamine2));
                    modified = true;
                    
                } else if (examine1 < controlLimits.lowerControlLimit) {
                    // 调整到下限至中值范围内的随机值
                    double adjustedValue = controlLimits.lowerControlLimit + (random.nextDouble() * lowerAdjustmentRange);
                    double adjustedExamine2 = examine2 != 0 ? Math.round(adjustedValue * examine2 / examine1 * 1000.0) / 1000.0 : 0;
                    
                    chemical.setExamine1(String.format("%.3f", adjustedValue));
                    chemical.setExamine2(String.format("%.3f", adjustedExamine2));
                    modified = true;
                }

                if (modified) {
                    modifiedCount++;
                    log.debug("控制线调整: {} -> {}", examine1, chemical.getExamine1());
                }
                
            } catch (NumberFormatException e) {
                log.warn("无效的数值格式: {}", chemical.getExamine1());
            }
        }

        return modifiedCount;
    }

    /**
     * 规则2: 移动极差调整
     * 调整相邻数据点之间的极差，使其不超过3倍标准差
     */
    private int applyMovingRangeAdjustment(List<Chemical> chemicals, ControlLimits controlLimits) {
        int modifiedCount = 0;
        double threeSigma = 3 * controlLimits.fSp;

        for (int i = 0; i < chemicals.size() - 1; i++) {
            try {
                double currentValue = Double.parseDouble(chemicals.get(i).getExamine1());
                double nextValue = Double.parseDouble(chemicals.get(i + 1).getExamine1());
                double difference = Math.abs(nextValue - currentValue);

                if (difference > threeSigma) {
                    // 确定哪个值更远离中值
                    Chemical toAdjust = Math.abs(currentValue - controlLimits.fMean) > Math.abs(nextValue - controlLimits.fMean) 
                                      ? chemicals.get(i) : chemicals.get(i + 1);
                    double originalExamine1 = Math.abs(currentValue - controlLimits.fMean) > Math.abs(nextValue - controlLimits.fMean) 
                                            ? currentValue : nextValue;
                    
                    double adjustValue = calculateAdjustValue(controlLimits.fMean, controlLimits.upperControlLimit, controlLimits.lowerControlLimit);
                    double examine2 = parseDoubleWithDefault(toAdjust.getExamine2(), 0);
                    double adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / originalExamine1 * 1000.0) / 1000.0 : 0;
                    
                    toAdjust.setExamine1(String.format("%.3f", adjustValue));
                    toAdjust.setExamine2(String.format("%.3f", adjustedExamine2));
                    modifiedCount++;
                    
                    log.debug("移动极差调整: {} -> {}", originalExamine1, adjustValue);
                }
                
            } catch (NumberFormatException e) {
                log.warn("移动极差调整时数值格式错误: {}", e.getMessage());
            }
        }

        return modifiedCount;
    }

    /**
     * 规则3: 9点同侧检查
     * 检查连续9个点是否都在均值同一侧，如果是则调整部分点
     */
    private int applyNinePointSameSideCheck(List<Chemical> chemicals, ControlLimits controlLimits) {
        int modifiedCount = 0;
        double rangeLower = (controlLimits.fMean - controlLimits.lowerControlLimit) * 0.1;
        double rangeUpper = (controlLimits.upperControlLimit - controlLimits.fMean) * 0.1;

        for (int start = 0; start <= chemicals.size() - 9; start++) {
            boolean allAbove = true;
            boolean allBelow = true;

            // 检查连续9个点
            for (int i = start; i < start + 9; i++) {
                try {
                    double examine1 = Double.parseDouble(chemicals.get(i).getExamine1());
                    if (examine1 <= controlLimits.fMean) {
                        allAbove = false;
                    }
                    if (examine1 >= controlLimits.fMean) {
                        allBelow = false;
                    }
                    if (!allAbove && !allBelow) {
                        break;
                    }
                } catch (NumberFormatException e) {
                    allAbove = false;
                    allBelow = false;
                    break;
                }
            }

            if (allAbove || allBelow) {
                // 调整第2、3、7、8、9个点
                int[] adjustIndexes = {start + 1, start + 2, start + 6, start + 7, start + 8};
                for (int index : adjustIndexes) {
                    Chemical chemicalToAdjust = chemicals.get(index);
                    try {
                        double originalExamine1 = Double.parseDouble(chemicalToAdjust.getExamine1());
                        double examine2 = parseDoubleWithDefault(chemicalToAdjust.getExamine2(), 0);
                        
                        double adjustValue = controlLimits.fMean + (allBelow ? rangeUpper : -rangeLower) + 
                                           random.nextDouble() * (allBelow ? rangeUpper : rangeLower);
                        adjustValue = Math.min(Math.max(adjustValue, controlLimits.lowerControlLimit + rangeLower), 
                                             controlLimits.upperControlLimit - rangeUpper);
                        
                        double adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / originalExamine1 * 1000.0) / 1000.0 : 0;
                        
                        chemicalToAdjust.setExamine1(String.format("%.3f", adjustValue));
                        chemicalToAdjust.setExamine2(String.format("%.3f", adjustedExamine2));
                        modifiedCount++;
                        
                        log.debug("9点同侧调整: {} -> {}", originalExamine1, adjustValue);
                        
                    } catch (NumberFormatException e) {
                        log.warn("9点同侧检查时数值格式错误: {}", e.getMessage());
                    }
                }
            }
        }

        return modifiedCount;
    }

    /**
     * 规则4: 6点递变检查
     * 检查连续6个点是否呈递增或递减趋势，如果是则调整第3个点
     */
    private int applySixPointTrendCheck(List<Chemical> chemicals) {
        int modifiedCount = 0;

        for (int start = 0; start <= chemicals.size() - 6; start++) {
            boolean increasing = true;
            boolean decreasing = true;
            double[] examine1Values = new double[6];

            // 预先填充examine1数组
            for (int i = 0; i < 6; i++) {
                try {
                    examine1Values[i] = Double.parseDouble(chemicals.get(start + i).getExamine1());
                } catch (NumberFormatException e) {
                    examine1Values[i] = 0;
                }
            }

            // 检查趋势
            for (int i = 0; i < 5; i++) {
                if (examine1Values[i] >= examine1Values[i + 1]) {
                    increasing = false;
                }
                if (examine1Values[i] <= examine1Values[i + 1]) {
                    decreasing = false;
                }
                if (!increasing && !decreasing) {
                    break;
                }
            }

            if (increasing || decreasing) {
                // 调整第3个点
                int adjustIndex = start + 2;
                Chemical chemicalToAdjust = chemicals.get(adjustIndex);
                double previousExamine1 = examine1Values[1];
                double currentExamine1 = examine1Values[2];
                double adjustValue;

                if (increasing) {
                    adjustValue = currentExamine1 - (currentExamine1 - previousExamine1) * 1.2;
                } else {
                    adjustValue = currentExamine1 + (previousExamine1 - currentExamine1) * 1.2;
                }

                try {
                    double examine2 = parseDoubleWithDefault(chemicalToAdjust.getExamine2(), 0);
                    double adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / currentExamine1 * 1000.0) / 1000.0 : 0;
                    
                    chemicalToAdjust.setExamine1(String.format("%.3f", adjustValue));
                    chemicalToAdjust.setExamine2(String.format("%.3f", adjustedExamine2));
                    modifiedCount++;
                    
                    log.debug("6点递变调整: {} -> {}", currentExamine1, adjustValue);
                    
                } catch (NumberFormatException e) {
                    log.warn("6点递变检查时数值格式错误: {}", e.getMessage());
                }
            }
        }

        return modifiedCount;
    }

    /**
     * 规则5: CPK值调整
     * 调整数据使CPK值达到目标值
     */
    private int applyCpkAdjustment(List<Chemical> chemicals, double cpkTarget, RuleConfig ruleConfig) {
        int modifiedCount = 0;
        double cpk = calculateCPK(chemicals, ruleConfig);
        
        if (cpk >= cpkTarget) {
            return 0; // 已满足CPK要求
        }

        double average = chemicals.stream()
                .mapToDouble(c -> parseDoubleWithDefault(c.getExamine1(), 0))
                .average()
                .orElse(0.0);

        int adjustmentCount = 0;
        while (cpk < cpkTarget && adjustmentCount < 15) {
            // 找到与均值差距最大的Chemical
            Chemical furthest = chemicals.stream()
                    .max((c1, c2) -> Double.compare(
                            Math.abs(parseDoubleWithDefault(c1.getExamine1(), 0) - average),
                            Math.abs(parseDoubleWithDefault(c2.getExamine1(), 0) - average)))
                    .orElse(null);

            if (furthest != null) {
                try {
                    double originalExamine1 = Double.parseDouble(furthest.getExamine1());
                    double adjustValue = average + (random.nextDouble() - 0.5) * 0.1 * average;
                    double examine2 = parseDoubleWithDefault(furthest.getExamine2(), 0);
                    double adjustedExamine2 = examine2 != 0 ? Math.round(adjustValue * examine2 / originalExamine1 * 1000.0) / 1000.0 : 0;
                    
                    furthest.setExamine1(String.format("%.3f", adjustValue));
                    furthest.setExamine2(String.format("%.3f", adjustedExamine2));
                    modifiedCount++;
                    
                    // 重新计算CPK
                    cpk = calculateCPK(chemicals, ruleConfig);
                    log.debug("CPK调整: {} -> {}, 新CPK: {}", originalExamine1, adjustValue, cpk);
                    
                } catch (NumberFormatException e) {
                    log.warn("CPK调整时数值格式错误: {}", e.getMessage());
                    break;
                }
            }
            adjustmentCount++;
        }

        return modifiedCount;
    }

    /**
     * 计算CPK值
     */
    private double calculateCPK(List<Chemical> chemicals, RuleConfig ruleConfig) {
        if (chemicals == null || chemicals.isEmpty()) {
            return Double.NaN;
        }

        double[] values = chemicals.stream()
                .mapToDouble(c -> parseDoubleWithDefault(c.getExamine1(), 0))
                .toArray();

        double mean = Arrays.stream(values).average().orElse(Double.NaN);
        double stdDev = Math.sqrt(Arrays.stream(values)
                .map(v -> Math.pow(v - mean, 2))
                .sum() / (values.length - 1));

        try {
            double usl = Double.parseDouble(chemicals.get(0).getUpperLimit());
            
            if (ruleConfig != null && ruleConfig.getIsRefreshMode() != null && ruleConfig.getIsRefreshMode()) {
                // 刷新模式，仅考虑规格上限
                return (usl - mean) / (3 * stdDev);
            } else {
                // 正常模式，同时考虑规格上下限
                double lsl = Double.parseDouble(chemicals.get(0).getDownLimit());
                return Math.min((usl - mean) / (3 * stdDev), (mean - lsl) / (3 * stdDev));
            }
        } catch (NumberFormatException e) {
            log.warn("计算CPK时规格限制格式错误: {}", e.getMessage());
            return Double.NaN;
        }
    }

    /**
     * 计算调整值
     */
    private double calculateAdjustValue(double mean, double upperLimit, double lowerLimit) {
        double rangeUpper = (upperLimit - mean) * 0.1;
        double rangeLower = (mean - lowerLimit) * 0.1;
        
        double adjustValue = mean + (random.nextBoolean() ? 1 : -1) * random.nextDouble() * 
                           (random.nextBoolean() ? rangeUpper : rangeLower);
        
        return Math.min(Math.max(adjustValue, lowerLimit), upperLimit);
    }

    /**
     * 安全解析double值
     */
    private double parseDoubleWithDefault(String s, double defaultValue) {
        try {
            return s != null && !s.trim().isEmpty() ? Double.parseDouble(s) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 检查所有条件是否满足
     */
    public boolean checkAllConditions(List<Chemical> chemicals, ControlLimits controlLimits, RuleConfig ruleConfig) {
        if (chemicals.isEmpty()) {
            return false;
        }

        // 检查CPK条件
        double cpkTarget = (ruleConfig != null && ruleConfig.getCpkTarget() != null) ? ruleConfig.getCpkTarget() : 1.33;
        double cpk = calculateCPK(chemicals, ruleConfig);
        boolean cpkConditionMet = cpk >= cpkTarget;

        if (!cpkConditionMet) {
            return false;
        }

        // 检查其他SPC规则
        return checkSPCRules(chemicals, controlLimits);
    }

    /**
     * 检查SPC规则
     */
    private boolean checkSPCRules(List<Chemical> chemicals, ControlLimits controlLimits) {
        double mean = controlLimits.fMean;
        double threeSigma = 3 * controlLimits.fSp;
        int length = chemicals.size();

        double[] values = chemicals.stream()
                .mapToDouble(c -> parseDoubleWithDefault(c.getExamine1(), 0))
                .toArray();

        // 检查移动极差
        for (int i = 0; i < length - 1; i++) {
            if (Math.abs(values[i + 1] - values[i]) > threeSigma) {
                return false;
            }
        }

        // 检查9点同侧
        for (int i = 0; i <= length - 9; i++) {
            boolean allAbove = true;
            boolean allBelow = true;
            for (int j = i; j < i + 9; j++) {
                if (values[j] <= mean) {
                    allAbove = false;
                }
                if (values[j] >= mean) {
                    allBelow = false;
                }
                if (!allAbove && !allBelow) {
                    break;
                }
            }
            if (allAbove || allBelow) {
                return false;
            }
        }

        // 检查6点递变
        for (int i = 0; i <= length - 6; i++) {
            boolean increasing = true;
            boolean decreasing = true;
            for (int j = i; j < i + 5; j++) {
                if (values[j] <= values[j + 1]) {
                    decreasing = false;
                }
                if (values[j] >= values[j + 1]) {
                    increasing = false;
                }
                if (!increasing && !decreasing) {
                    break;
                }
            }
            if (increasing || decreasing) {
                return false;
            }
        }

        return true;
    }
}
