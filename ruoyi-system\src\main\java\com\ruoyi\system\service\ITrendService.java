package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;

/**
 * 趋势对比Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ITrendService 
{
    /**
     * 获取趋势对比数据
     * 
     * @param paramNumbers 参数编号列表（逗号分隔）
     * @param performanceNames 性能名称列表（逗号分隔）
     * @return 趋势数据
     */
    public Map<String, Object> getTrendData(String paramNumbers, String performanceNames);

    /**
     * 获取所有参数编号
     * 
     * @return 参数编号列表
     */
    public List<String> getParamNumbers();

    /**
     * 获取所有性能名称
     * 
     * @return 性能名称列表
     */
    public List<String> getPerformanceNames();

    /**
     * 根据参数编号获取参数详情
     * 
     * @param paramNumber 参数编号
     * @return 参数详情
     */
    public Map<String, Object> getParamDetails(String paramNumber);
}