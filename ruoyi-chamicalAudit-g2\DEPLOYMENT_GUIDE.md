# 化学审计系统部署指南

## 系统概述

化学审计系统是基于若依框架开发的Web应用，整合了原JavaFX软件（chemical和chemical-refresh）的所有功能，实现了完整的化学数据审计、处理和导出功能。

## 部署前准备

### 1. 环境要求
- **JDK**: 1.8 或以上版本
- **数据库**: SQL Server 2012 或以上版本
- **应用服务器**: Tomcat 8.5 或以上版本
- **内存**: 建议4GB以上
- **磁盘空间**: 建议10GB以上（用于数据存储和导出文件）

### 2. 数据库准备
确保有以下数据库访问权限：
- **本地数据库**: 用于存储系统数据和处理结果
- **云端数据库**: 用于读取原始化学数据（************ SPC-G2数据库）

## 部署步骤

### 第一步：数据库初始化

1. **连接到本地SQL Server数据库**
2. **执行数据库初始化脚本**：
   ```sql
   -- 1. 先执行表结构和数据初始化
   执行文件: sql/init_chemical_audit_system.sql
   
   -- 2. 再执行菜单配置
   执行文件: sql/chemical_audit_menu.sql
   ```

3. **验证数据库创建**：
   ```sql
   -- 检查表是否创建成功
   SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
   WHERE TABLE_NAME LIKE 'chemical%' OR TABLE_NAME = 'rule_config'
   
   -- 应该看到以下表：
   -- chemical
   -- chemical_ys  
   -- rule_config
   -- chemical_task_monitor
   -- chemical_refresh_task
   -- chemical_export_record
   -- chemical_system_config
   -- chemical_process_log
   ```

### 第二步：应用配置

1. **修改数据库连接配置**：
   编辑 `application-druid.yml` 文件：
   ```yaml
   spring:
     datasource:
       druid:
         master:
           url: ***************************************************
           username: your_username
           password: your_password
   ```

2. **配置云端数据库连接**：
   编辑 `application.yml` 文件，添加：
   ```yaml
   chemical:
     database:
       cloud:
         url: jdbc:sqlserver://************;DatabaseName=SPC-G2;encrypt=true;trustServerCertificate=true;sslProtocol=TLSv1
         username: hhh
         password: root1234
     export:
       default:
         path: D:/chemical_exports
       max:
         records: 100000
   ```

3. **创建导出目录**：
   ```bash
   mkdir D:/chemical_exports
   # 确保应用有读写权限
   ```

### 第三步：应用部署

1. **编译项目**：
   ```bash
   mvn clean package -Dmaven.test.skip=true
   ```

2. **部署到Tomcat**：
   - 将生成的 `ruoyi-admin.war` 文件复制到Tomcat的webapps目录
   - 启动Tomcat服务器

3. **验证部署**：
   - 访问: `http://localhost:8080/ruoyi-admin`
   - 使用admin/admin123登录
   - 检查"化学审计系统"菜单是否正常显示

## 功能验证

### 1. 菜单权限验证
登录系统后，应该能看到以下菜单结构：
```
化学审计系统
├── 数据管理
│   ├── 化学数据
│   └── 应审数据
├── 任务管理
│   ├── 任务控制
│   └── 任务监控
├── 规则配置
│   └── 规则管理
├── 数据导出
│   └── 导出管理
└── 系统配置
    └── 参数配置
```

### 2. 数据库连接验证
1. **进入系统配置 → 参数配置**
2. **检查云端数据库配置参数**
3. **测试连接是否正常**

### 3. 功能模块验证
1. **规则配置**: 创建一个测试规则配置
2. **任务控制**: 启动一个统一数据处理任务
3. **任务监控**: 查看任务执行状态
4. **数据导出**: 执行一次CSV导出测试

## 系统配置

### 1. 默认系统参数
系统已预置以下配置参数，可在"系统配置 → 参数配置"中修改：

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| chemical.database.cloud.url | jdbc:sqlserver://************;DatabaseName=SPC-G2 | 云端数据库URL |
| chemical.database.cloud.username | hhh | 云端数据库用户名 |
| chemical.database.cloud.password | root1234 | 云端数据库密码 |
| chemical.export.default.path | D:/chemical_exports | 默认导出路径 |
| chemical.export.max.records | 100000 | 最大导出记录数 |
| chemical.task.max.concurrent | 5 | 最大并发任务数 |
| chemical.rule.default.cpk.target | 1.33 | 默认CPK目标值 |

### 2. 规则配置说明
系统支持按产品-过程-测试名称配置5大数据刷新规则：
- **控制线内调整**: 将超出控制限的数据调整到控制限内
- **移动极差调整**: 调整相邻数据点之间的极差
- **9点同侧检查**: 检查连续9个点是否都在均值同一侧
- **6点递变检查**: 检查连续6个点是否呈递增或递减趋势
- **CPK值调整**: 调整数据使CPK值达到目标值

## 使用流程

### 1. 基本使用流程
1. **配置规则**: 在"规则配置 → 规则管理"中设置各项目的规则开关
2. **启动任务**: 在"任务管理 → 任务控制"中启动统一数据处理任务
3. **监控进度**: 在"任务管理 → 任务监控"中查看任务执行状态
4. **导出数据**: 在"数据导出 → 导出管理"中选择时间范围和格式导出数据

### 2. 高级功能
- **批量规则配置**: 支持批量设置多个产品-过程-测试的规则
- **规则复制**: 可以复制现有规则配置到新的产品-过程-测试组合
- **实时监控**: 提供实时的任务执行统计和系统性能监控
- **错误处理**: 完善的错误日志记录和处理机制

## 故障排除

### 1. 常见问题

**问题1**: 启动时报错"Field taskMonitorService required a bean"
- **解决**: 确保ChemicalTaskMonitorServiceImpl类已正确创建并添加@Service注解

**问题2**: 菜单不显示
- **解决**: 检查chemical_audit_menu.sql是否正确执行，确认菜单权限分配

**问题3**: 云端数据库连接失败
- **解决**: 检查网络连接和数据库配置参数，确认防火墙设置

**问题4**: 导出文件为空
- **解决**: 检查导出路径权限，确认数据查询条件和时间范围

### 2. 日志查看
- **应用日志**: 查看Tomcat logs目录下的日志文件
- **系统日志**: 在"任务管理 → 任务监控"中查看任务执行日志
- **数据库日志**: 查询chemical_process_log表获取详细处理日志

### 3. 性能优化
- **数据库索引**: 系统已创建必要索引，如需优化可根据查询模式调整
- **内存配置**: 根据数据量调整JVM内存参数
- **并发控制**: 通过chemical.task.max.concurrent参数控制并发任务数

## 维护建议

### 1. 定期维护
- **数据清理**: 定期清理过期的任务监控记录和处理日志
- **备份**: 定期备份数据库和导出文件
- **监控**: 监控系统性能和磁盘空间使用情况

### 2. 升级注意事项
- **数据备份**: 升级前务必备份数据库
- **配置保存**: 保存自定义的系统配置参数
- **测试验证**: 在测试环境验证新版本功能

## 技术支持

如遇到技术问题，请提供以下信息：
1. 系统版本信息
2. 错误日志详情
3. 操作步骤描述
4. 系统环境信息

---

**注意**: 本系统整合了原JavaFX软件的所有功能，部署成功后即可完全替代原有的桌面应用程序。
