<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.ChemicalRefreshTaskMapper">
    
    <resultMap type="ChemicalRefreshTask" id="ChemicalRefreshTaskResult">
        <result property="refreshId"    column="refresh_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="totalGroups"    column="total_groups"    />
        <result property="modifiedRecords"    column="modified_records"    />
        <result property="durationMs"    column="duration_ms"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="exportFilePath"    column="export_file_path"    />
        <result property="exportBackupPath"    column="export_backup_path"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="layerNumbers"    column="layer_numbers"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectChemicalRefreshTaskVo">
        select refresh_id, task_name, start_time, end_time, total_groups, modified_records, 
               duration_ms, task_status, error_message, export_file_path, export_backup_path, 
               start_date, end_date, layer_numbers, create_by, create_time, update_time 
        from chemical_refresh_task
    </sql>

    <select id="selectChemicalRefreshTaskList" parameterType="ChemicalRefreshTask" resultMap="ChemicalRefreshTaskResult">
        <include refid="selectChemicalRefreshTaskVo"/>
        <where>  
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskStatus != null  and taskStatus != ''"> and task_status = #{taskStatus}</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectChemicalRefreshTaskByRefreshId" parameterType="Long" resultMap="ChemicalRefreshTaskResult">
        <include refid="selectChemicalRefreshTaskVo"/>
        where refresh_id = #{refreshId}
    </select>

    <select id="selectRecentRefreshTasks" parameterType="int" resultMap="ChemicalRefreshTaskResult">
        <include refid="selectChemicalRefreshTaskVo"/>
        order by create_time desc
        limit #{limit}
    </select>

    <select id="selectRunningRefreshTasks" resultMap="ChemicalRefreshTaskResult">
        <include refid="selectChemicalRefreshTaskVo"/>
        where task_status = 'RUNNING'
        order by create_time desc
    </select>

    <select id="countRefreshTasksByStatus" parameterType="String" resultType="int">
        select count(*) from chemical_refresh_task where task_status = #{taskStatus}
    </select>
        
    <insert id="insertChemicalRefreshTask" parameterType="ChemicalRefreshTask" useGeneratedKeys="true" keyProperty="refreshId">
        insert into chemical_refresh_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="totalGroups != null">total_groups,</if>
            <if test="modifiedRecords != null">modified_records,</if>
            <if test="durationMs != null">duration_ms,</if>
            <if test="taskStatus != null and taskStatus != ''">task_status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="exportFilePath != null">export_file_path,</if>
            <if test="exportBackupPath != null">export_backup_path,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="layerNumbers != null">layer_numbers,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="totalGroups != null">#{totalGroups},</if>
            <if test="modifiedRecords != null">#{modifiedRecords},</if>
            <if test="durationMs != null">#{durationMs},</if>
            <if test="taskStatus != null and taskStatus != ''">#{taskStatus},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="exportFilePath != null">#{exportFilePath},</if>
            <if test="exportBackupPath != null">#{exportBackupPath},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="layerNumbers != null">#{layerNumbers},</if>
            <if test="createBy != null">#{createBy},</if>
            getdate()
        </trim>
    </insert>

    <update id="updateChemicalRefreshTask" parameterType="ChemicalRefreshTask">
        update chemical_refresh_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="totalGroups != null">total_groups = #{totalGroups},</if>
            <if test="modifiedRecords != null">modified_records = #{modifiedRecords},</if>
            <if test="durationMs != null">duration_ms = #{durationMs},</if>
            <if test="taskStatus != null and taskStatus != ''">task_status = #{taskStatus},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="exportFilePath != null">export_file_path = #{exportFilePath},</if>
            <if test="exportBackupPath != null">export_backup_path = #{exportBackupPath},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="layerNumbers != null">layer_numbers = #{layerNumbers},</if>
            update_time = getdate()
        </trim>
        where refresh_id = #{refreshId}
    </update>

    <delete id="deleteChemicalRefreshTaskByRefreshId" parameterType="Long">
        delete from chemical_refresh_task where refresh_id = #{refreshId}
    </delete>

    <delete id="deleteChemicalRefreshTaskByRefreshIds" parameterType="String">
        delete from chemical_refresh_task where refresh_id in 
        <foreach item="refreshId" collection="array" open="(" separator="," close=")">
            #{refreshId}
        </foreach>
    </delete>

    <delete id="cleanHistoryRefreshTasks" parameterType="int">
        delete from chemical_refresh_task 
        where create_time &lt; dateadd(day, -#{days}, getdate())
    </delete>

</mapper>
