package com.ruoyi.audit.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 化学应审数据对象 chemical_ys
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ChemicalYs extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 组织ID */
    @Excel(name = "组织ID")
    private Long organizationId;

    /** 属性ID */
    @Excel(name = "属性ID")
    private Long attributeId;

    /** 检测日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "检测日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date examineDate;

    /** 班次 */
    @Excel(name = "班次")
    private String shift;

    /** 操作员工 */
    @Excel(name = "操作员工")
    private String staff;

    /** 工艺集名称 */
    @Excel(name = "工艺集名称")
    private String processSetName;

    /** 工艺名称 */
    @Excel(name = "工艺名称")
    private String processName;

    /** 产品集名称 */
    @Excel(name = "产品集名称")
    private String productSetName;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 测试集名称 */
    @Excel(name = "测试集名称")
    private String testSetName;

    /** 测试名称 */
    @Excel(name = "测试名称")
    private String testName;

    /** 样本大小 */
    @Excel(name = "样本大小")
    private String sampleSize;

    /** 层号 */
    @Excel(name = "层号")
    private String layerNumber;

    /** 上限 */
    @Excel(name = "上限")
    private String upperLimit;

    /** 中位规格 */
    @Excel(name = "中位规格")
    private String medianSpecification;

    /** 下限 */
    @Excel(name = "下限")
    private String downLimit;

    /** 调整后检测值1 */
    @Excel(name = "调整后检测值1")
    private String examine1;

    /** 调整后检测值2 */
    @Excel(name = "调整后检测值2")
    private String examine2;

    /** 检测值1真实值 */
    @Excel(name = "检测值1真实值")
    private String examine1Zs;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 最后更新人 */
    @Excel(name = "最后更新人")
    private String lastUpdatedBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date creationDate;

    /** 最后更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateDate;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 频率 */
    @Excel(name = "频率")
    private String frequency;

    /** 频率单位 */
    @Excel(name = "频率单位")
    private String frequencyUnit;

    /** 槽体名称 */
    @Excel(name = "槽体名称")
    private String slotBodyName;

    /** 项目组代码 */
    @Excel(name = "项目组代码")
    private String projectTeamCode;

    /** 项目组名称 */
    @Excel(name = "项目组名称")
    private String projectTeamName;

    /** 测试代码 */
    @Excel(name = "测试代码")
    private String testCode;

    /** 调整上限 */
    @Excel(name = "调整上限")
    private String adjustmentUpperLimit;

    /** 调整中值 */
    @Excel(name = "调整中值")
    private String adjustmentMid;

    /** 调整下限 */
    @Excel(name = "调整下限")
    private String adjustmentLowerLimit;

    /** 项目单位 */
    @Excel(name = "项目单位")
    private String projectUnit;

    /** 插入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "插入时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date insertionTime;

    /** 警告上限 */
    @Excel(name = "警告上限")
    private String warningUpperLimit;

    /** 警告中值 */
    @Excel(name = "警告中值")
    private String warningMid;

    /** 警告下限 */
    @Excel(name = "警告下限")
    private String warningLowerLimit;

    /** 是否被修改过 */
    @Excel(name = "是否被修改过")
    private Boolean isModified;

    /** 原始检测值1 */
    @Excel(name = "原始检测值1")
    private String originalExamine1;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setOrganizationId(Long organizationId) 
    {
        this.organizationId = organizationId;
    }

    public Long getOrganizationId() 
    {
        return organizationId;
    }

    public void setAttributeId(Long attributeId) 
    {
        this.attributeId = attributeId;
    }

    public Long getAttributeId() 
    {
        return attributeId;
    }

    public void setExamineDate(Date examineDate) 
    {
        this.examineDate = examineDate;
    }

    public Date getExamineDate() 
    {
        return examineDate;
    }

    public void setShift(String shift) 
    {
        this.shift = shift;
    }

    public String getShift() 
    {
        return shift;
    }

    public void setStaff(String staff) 
    {
        this.staff = staff;
    }

    public String getStaff() 
    {
        return staff;
    }

    public void setProcessSetName(String processSetName) 
    {
        this.processSetName = processSetName;
    }

    public String getProcessSetName() 
    {
        return processSetName;
    }

    public void setProcessName(String processName) 
    {
        this.processName = processName;
    }

    public String getProcessName() 
    {
        return processName;
    }

    public void setProductSetName(String productSetName) 
    {
        this.productSetName = productSetName;
    }

    public String getProductSetName() 
    {
        return productSetName;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }

    public void setTestSetName(String testSetName) 
    {
        this.testSetName = testSetName;
    }

    public String getTestSetName() 
    {
        return testSetName;
    }

    public void setTestName(String testName) 
    {
        this.testName = testName;
    }

    public String getTestName() 
    {
        return testName;
    }

    public void setSampleSize(String sampleSize) 
    {
        this.sampleSize = sampleSize;
    }

    public String getSampleSize() 
    {
        return sampleSize;
    }

    public void setLayerNumber(String layerNumber) 
    {
        this.layerNumber = layerNumber;
    }

    public String getLayerNumber() 
    {
        return layerNumber;
    }

    public void setUpperLimit(String upperLimit) 
    {
        this.upperLimit = upperLimit;
    }

    public String getUpperLimit() 
    {
        return upperLimit;
    }

    public void setMedianSpecification(String medianSpecification) 
    {
        this.medianSpecification = medianSpecification;
    }

    public String getMedianSpecification() 
    {
        return medianSpecification;
    }

    public void setDownLimit(String downLimit) 
    {
        this.downLimit = downLimit;
    }

    public String getDownLimit() 
    {
        return downLimit;
    }

    public void setExamine1(String examine1) 
    {
        this.examine1 = examine1;
    }

    public String getExamine1() 
    {
        return examine1;
    }

    public void setExamine2(String examine2) 
    {
        this.examine2 = examine2;
    }

    public String getExamine2() 
    {
        return examine2;
    }

    public void setExamine1Zs(String examine1Zs)
    {
        this.examine1Zs = examine1Zs;
    }

    public String getExamine1Zs()
    {
        return examine1Zs;
    }

    public void setCreatedBy(String createdBy)
    {
        this.createdBy = createdBy;
    }

    public String getCreatedBy()
    {
        return createdBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy)
    {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy()
    {
        return lastUpdatedBy;
    }

    public void setCreationDate(Date creationDate)
    {
        this.creationDate = creationDate;
    }

    public Date getCreationDate()
    {
        return creationDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate)
    {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Date getLastUpdateDate()
    {
        return lastUpdateDate;
    }

    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }

    public void setFrequency(String frequency)
    {
        this.frequency = frequency;
    }

    public String getFrequency()
    {
        return frequency;
    }

    public void setFrequencyUnit(String frequencyUnit)
    {
        this.frequencyUnit = frequencyUnit;
    }

    public String getFrequencyUnit()
    {
        return frequencyUnit;
    }

    public void setSlotBodyName(String slotBodyName)
    {
        this.slotBodyName = slotBodyName;
    }

    public String getSlotBodyName()
    {
        return slotBodyName;
    }

    public void setProjectTeamCode(String projectTeamCode)
    {
        this.projectTeamCode = projectTeamCode;
    }

    public String getProjectTeamCode()
    {
        return projectTeamCode;
    }

    public void setProjectTeamName(String projectTeamName)
    {
        this.projectTeamName = projectTeamName;
    }

    public String getProjectTeamName()
    {
        return projectTeamName;
    }

    public void setTestCode(String testCode)
    {
        this.testCode = testCode;
    }

    public String getTestCode()
    {
        return testCode;
    }

    public void setAdjustmentUpperLimit(String adjustmentUpperLimit)
    {
        this.adjustmentUpperLimit = adjustmentUpperLimit;
    }

    public String getAdjustmentUpperLimit()
    {
        return adjustmentUpperLimit;
    }

    public void setAdjustmentMid(String adjustmentMid)
    {
        this.adjustmentMid = adjustmentMid;
    }

    public String getAdjustmentMid()
    {
        return adjustmentMid;
    }

    public void setAdjustmentLowerLimit(String adjustmentLowerLimit)
    {
        this.adjustmentLowerLimit = adjustmentLowerLimit;
    }

    public String getAdjustmentLowerLimit()
    {
        return adjustmentLowerLimit;
    }

    public void setProjectUnit(String projectUnit)
    {
        this.projectUnit = projectUnit;
    }

    public String getProjectUnit()
    {
        return projectUnit;
    }

    public void setInsertionTime(Date insertionTime)
    {
        this.insertionTime = insertionTime;
    }

    public Date getInsertionTime()
    {
        return insertionTime;
    }

    public void setWarningUpperLimit(String warningUpperLimit)
    {
        this.warningUpperLimit = warningUpperLimit;
    }

    public String getWarningUpperLimit()
    {
        return warningUpperLimit;
    }

    public void setWarningMid(String warningMid)
    {
        this.warningMid = warningMid;
    }

    public String getWarningMid()
    {
        return warningMid;
    }

    public void setWarningLowerLimit(String warningLowerLimit)
    {
        this.warningLowerLimit = warningLowerLimit;
    }

    public String getWarningLowerLimit()
    {
        return warningLowerLimit;
    }

    public void setIsModified(Boolean isModified)
    {
        this.isModified = isModified;
    }

    public Boolean getIsModified()
    {
        return isModified;
    }

    public void setOriginalExamine1(String originalExamine1)
    {
        this.originalExamine1 = originalExamine1;
    }

    public String getOriginalExamine1()
    {
        return originalExamine1;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("organizationId", getOrganizationId())
            .append("attributeId", getAttributeId())
            .append("examineDate", getExamineDate())
            .append("shift", getShift())
            .append("staff", getStaff())
            .append("processSetName", getProcessSetName())
            .append("processName", getProcessName())
            .append("productSetName", getProductSetName())
            .append("productName", getProductName())
            .append("testSetName", getTestSetName())
            .append("testName", getTestName())
            .append("sampleSize", getSampleSize())
            .append("layerNumber", getLayerNumber())
            .append("upperLimit", getUpperLimit())
            .append("medianSpecification", getMedianSpecification())
            .append("downLimit", getDownLimit())
            .append("examine1", getExamine1())
            .append("examine2", getExamine2())
            .append("examine1Zs", getExamine1Zs())
            .append("createdBy", getCreatedBy())
            .append("lastUpdatedBy", getLastUpdatedBy())
            .append("creationDate", getCreationDate())
            .append("lastUpdateDate", getLastUpdateDate())
            .append("status", getStatus())
            .append("frequency", getFrequency())
            .append("frequencyUnit", getFrequencyUnit())
            .append("slotBodyName", getSlotBodyName())
            .append("projectTeamCode", getProjectTeamCode())
            .append("projectTeamName", getProjectTeamName())
            .append("testCode", getTestCode())
            .append("adjustmentUpperLimit", getAdjustmentUpperLimit())
            .append("adjustmentMid", getAdjustmentMid())
            .append("adjustmentLowerLimit", getAdjustmentLowerLimit())
            .append("projectUnit", getProjectUnit())
            .append("insertionTime", getInsertionTime())
            .append("warningUpperLimit", getWarningUpperLimit())
            .append("warningMid", getWarningMid())
            .append("warningLowerLimit", getWarningLowerLimit())
            .append("isModified", getIsModified())
            .append("originalExamine1", getOriginalExamine1())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
