package com.ruoyi.web.controller.material;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.TestPlanGroup;
import com.ruoyi.system.service.ITestPlanGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 测试方案组Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/material/testPlanGroup")
public class TestPlanGroupController extends BaseController
{
    @Autowired
    private ITestPlanGroupService testPlanGroupService;

    /**
     * 查询测试方案组列表
     */
    @PreAuthorize("@ss.hasPermi('material:testPlanGroup:list')")
    @GetMapping("/list")
    public TableDataInfo list(TestPlanGroup testPlanGroup)
    {
        startPage();
        List<TestPlanGroup> list = testPlanGroupService.selectTestPlanGroupList(testPlanGroup);
        return getDataTable(list);
    }

    /**
     * 导出测试方案组列表
     */
    @PreAuthorize("@ss.hasPermi('material:testPlanGroup:export')")
    @Log(title = "测试方案组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestPlanGroup testPlanGroup)
    {
        List<TestPlanGroup> list = testPlanGroupService.selectTestPlanGroupList(testPlanGroup);
        ExcelUtil<TestPlanGroup> util = new ExcelUtil<TestPlanGroup>(TestPlanGroup.class);
        util.exportExcel(response, list, "测试方案组数据");
    }

    /**
     * 获取测试方案组详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:testPlanGroup:query')")
    @GetMapping(value = "/{planGroupId}")
    public AjaxResult getInfo(@PathVariable("planGroupId") Long planGroupId)
    {
        return AjaxResult.success(testPlanGroupService.selectTestPlanGroupByPlanGroupId(planGroupId));
    }

    /**
     * 新增测试方案组
     */
    @PreAuthorize("@ss.hasPermi('material:testPlanGroup:add')")
    @Log(title = "测试方案组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestPlanGroup testPlanGroup)
    {
        return toAjax(testPlanGroupService.insertTestPlanGroup(testPlanGroup));
    }

    /**
     * 修改测试方案组
     */
    @PreAuthorize("@ss.hasPermi('material:testPlanGroup:edit')")
    @Log(title = "测试方案组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestPlanGroup testPlanGroup)
    {
        return toAjax(testPlanGroupService.updateTestPlanGroup(testPlanGroup));
    }

    /**
     * 删除测试方案组
     */
    @PreAuthorize("@ss.hasPermi('material:testPlanGroup:remove')")
    @Log(title = "测试方案组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planGroupIds}")
    public AjaxResult remove(@PathVariable Long[] planGroupIds)
    {
        return toAjax(testPlanGroupService.deleteTestPlanGroupByPlanGroupIds(planGroupIds));
    }

    /**
     * 获取测试方案组选项数据
     */
    @GetMapping("/options")
    public AjaxResult getOptions(@RequestParam(required = false) String type)
    {
        List<String> options = testPlanGroupService.selectTestPlanGroupOptions(type);
        return AjaxResult.success(options);
    }
}
