<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProcessParamGroupMapper">
    
    <resultMap type="ProcessParamGroup" id="ProcessParamGroupResult">
        <result property="groupId"    column="group_id"    />
        <result property="materialId"    column="material_id"    />
        <result property="processType"    column="process_type"    />
        <result property="paramNumber"    column="param_number"    />
        <result property="attachments"    column="attachments"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="materialName"    column="material_name"    />
        <result property="supplierName"    column="supplier_name"    />
    </resultMap>

    <resultMap id="ProcessParamGroupProcessParamItemResult" type="ProcessParamGroup" extends="ProcessParamGroupResult">
        <collection property="processParamItemList" notNullColumn="sub_item_id" javaType="java.util.List" resultMap="ProcessParamItemResult" />
    </resultMap>

    <resultMap type="ProcessParamItem" id="ProcessParamItemResult">
        <result property="itemId"    column="sub_item_id"    />
        <result property="groupId"    column="sub_group_id"    />
        <result property="paramName"    column="sub_param_name"    />
        <result property="paramValue"    column="sub_param_value"    />
        <result property="unit"    column="sub_unit"    />
        <result property="attachments"    column="sub_attachments"    />
        <result property="remark"    column="sub_remark"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="updateTime"    column="sub_update_time"    />
    </resultMap>

    <sql id="selectProcessParamGroupVo">
        select g.group_id, g.material_id, g.process_type, g.param_number, g.attachments, g.remark, g.create_by, g.create_time, g.update_by, g.update_time, m.material_name, m.supplier_name
        from process_param_group g
        left join materials m on g.material_id = m.material_id
    </sql>

    <select id="selectProcessParamGroupList" parameterType="ProcessParamGroup" resultMap="ProcessParamGroupResult">
        <include refid="selectProcessParamGroupVo"/>
        <where>  
            <if test="materialId != null "> and g.material_id = #{materialId}</if>
            <if test="processType != null  and processType != ''"> and g.process_type like concat('%', #{processType}, '%')</if>
            <if test="paramNumber != null  and paramNumber != ''"> and g.param_number like concat('%', #{paramNumber}, '%')</if>
        </where>
    </select>
    
    <select id="selectProcessParamGroupByGroupId" parameterType="Long" resultMap="ProcessParamGroupProcessParamItemResult">
        select g.group_id, g.material_id, g.process_type, g.param_number, g.attachments, g.remark, g.create_by, g.create_time, g.update_by, g.update_time, m.material_name, m.supplier_name,
 i.item_id as sub_item_id, i.group_id as sub_group_id, i.param_name as sub_param_name, i.param_value as sub_param_value, i.unit as sub_unit, i.attachments as sub_attachments, i.remark as sub_remark, i.create_by as sub_create_by, i.create_time as sub_create_time, i.update_by as sub_update_by, i.update_time as sub_update_time
        from process_param_group g
        left join materials m on g.material_id = m.material_id
        left join process_param_item i on i.group_id = g.group_id
        where g.group_id = #{groupId}
    </select>

    <select id="selectProcessParamGroupByMaterialId" parameterType="Long" resultMap="ProcessParamGroupProcessParamItemResult">
        select g.group_id, g.material_id, g.process_type, g.param_number, g.attachments, g.remark, g.create_by, g.create_time, g.update_by, g.update_time, m.material_name,
 i.item_id as sub_item_id, i.group_id as sub_group_id, i.param_name as sub_param_name, i.param_value as sub_param_value, i.unit as sub_unit, i.attachments as sub_attachments, i.remark as sub_remark, i.create_by as sub_create_by, i.create_time as sub_create_time, i.update_by as sub_update_by, i.update_time as sub_update_time
        from process_param_group g
        left join materials m on g.material_id = m.material_id
        left join process_param_item i on i.group_id = g.group_id
        where g.material_id = #{materialId}
        order by g.group_id, i.item_id
    </select>
        
    <insert id="insertProcessParamGroup" parameterType="ProcessParamGroup" useGeneratedKeys="true" keyProperty="groupId">
        insert into process_param_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialId != null">material_id,</if>
            <if test="processType != null and processType != ''">process_type,</if>
            <if test="paramNumber != null and paramNumber != ''">param_number,</if>
            <if test="attachments != null">attachments,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialId != null">#{materialId},</if>
            <if test="processType != null and processType != ''">#{processType},</if>
            <if test="paramNumber != null and paramNumber != ''">#{paramNumber},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateProcessParamGroup" parameterType="ProcessParamGroup">
        update process_param_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialId != null">material_id = #{materialId},</if>
            <if test="processType != null and processType != ''">process_type = #{processType},</if>
            <if test="paramNumber != null and paramNumber != ''">param_number = #{paramNumber},</if>
            attachments = #{attachments},
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where group_id = #{groupId}
    </update>

    <delete id="deleteProcessParamGroupByGroupId" parameterType="Long">
        delete from process_param_group where group_id = #{groupId}
    </delete>

    <delete id="deleteProcessParamGroupByGroupIds" parameterType="String">
        delete from process_param_group where group_id in 
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <delete id="deleteProcessParamItemByGroupIds" parameterType="String">
        delete from process_param_item where group_id in 
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <delete id="deleteProcessParamItemByGroupId" parameterType="Long">
        delete from process_param_item where group_id = #{groupId}
    </delete>

    <insert id="batchProcessParamItem">
        insert into process_param_item( item_id, group_id, param_name, param_value, unit, attachments, remark, create_by, create_time, update_by, update_time) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.itemId}, #{item.groupId}, #{item.paramName}, #{item.paramValue}, #{item.unit}, #{item.attachments}, #{item.remark}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <select id="selectProcessTypeOptions" resultType="String">
        select distinct process_type from process_param_group where process_type is not null and process_type != '' order by process_type
    </select>
    
    <select id="selectParamNumberOptions" resultType="String">
        select distinct param_number from process_param_group where param_number is not null and param_number != '' order by param_number
    </select>
    
    <select id="selectCompleteDataByMaterialId" parameterType="Long" resultType="Map">
        select 
            m.material_name,
            m.supplier_name,
            m.material_model,
            m.material_description,
            g.process_type,
            g.param_number,
            i.param_name,
            i.param_value,
            i.unit,
            m.create_by as material_create_by,
            m.create_time as material_create_time,
            g.create_by as group_create_by,
            g.create_time as group_create_time,
            i.create_by as item_create_by,
            i.create_time as item_create_time
        from materials m
        left join process_param_group g on m.material_id = g.material_id
        left join process_param_item i on g.group_id = i.group_id
        where m.material_id = #{materialId}
        order by g.group_id, i.item_id
    </select>

</mapper>
