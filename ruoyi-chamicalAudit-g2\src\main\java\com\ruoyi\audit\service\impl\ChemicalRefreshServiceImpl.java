package com.ruoyi.audit.service.impl;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.audit.domain.Chemical;
import com.ruoyi.audit.domain.ChemicalRefreshTask;
import com.ruoyi.audit.domain.ChemicalYs;
import com.ruoyi.audit.domain.RuleConfig;
import com.ruoyi.audit.service.IRuleConfigService;
import com.ruoyi.audit.service.impl.DataRefreshRuleEngine;
import com.ruoyi.audit.mapper.ChemicalRefreshTaskMapper;
import com.ruoyi.audit.mapper.ChemicalYsMapper;
import com.ruoyi.audit.service.IChemicalRefreshService;

/**
 * 控制限制参数类
 */
class ControlLimits {
    public double fMean;
    public double fSp;
    public double upperControlLimit;
    public double lowerControlLimit;

    public ControlLimits() {
    }

    public ControlLimits(double fMean, double fSp) {
        this.fMean = fMean;
        this.fSp = fSp;
        calculateControlLimits();
    }

    private void calculateControlLimits() {
        double threeSigma = 3 * fSp;
        this.upperControlLimit = fMean + threeSigma / 2;
        this.lowerControlLimit = fMean - threeSigma / 2;
    }
}

/**
 * 计算值类
 */
class CalculatedValues {
    public double upperControlLimit;
    public double lowerControlLimit;
    public double mean;
    public double standardDeviation;
    public double threeSigma;
    public double upperAdjustmentRange;
    public double lowerAdjustmentRange;

    public CalculatedValues(double mean, double upperControlLimit, double lowerControlLimit, double standardDeviation) {
        this.mean = mean;
        this.upperControlLimit = upperControlLimit;
        this.lowerControlLimit = lowerControlLimit;
        this.standardDeviation = standardDeviation;
        this.threeSigma = 3 * standardDeviation;
        this.upperAdjustmentRange = (upperControlLimit - mean) * 0.5;
        this.lowerAdjustmentRange = (mean - lowerControlLimit) * 0.5;
    }
}

/**
 * 化学数据刷新Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ChemicalRefreshServiceImpl implements IChemicalRefreshService 
{
    private static final Logger log = LoggerFactory.getLogger(ChemicalRefreshServiceImpl.class);

    @Autowired
    private ChemicalRefreshTaskMapper refreshTaskMapper;

    @Autowired
    private ChemicalYsMapper chemicalYsMapper;

    @Autowired
    private IRuleConfigService ruleConfigService;

    @Autowired
    private DataRefreshRuleEngine ruleEngine;

    // 数据库连接配置
    @Value("${chemical.database.local.url:*****************************************************************}")
    private String localDatabaseUrl;

    @Value("${chemical.database.local.username:sa}")
    private String localDatabaseUsername;

    @Value("${chemical.database.local.password:}")
    private String localDatabasePassword;

    @Value("${chemical.database.cloud.url:jdbc:sqlserver://************;DatabaseName=SPC-G2;encrypt=true;trustServerCertificate=true;sslProtocol=TLSv1}")
    private String cloudDatabaseUrl;

    @Value("${chemical.database.cloud.username:sa}")
    private String cloudDatabaseUsername;

    @Value("${chemical.database.cloud.password:}")
    private String cloudDatabasePassword;

    // 导出路径配置
    @Value("${chemical.export.default.path:E:\\测试数据\\应审抛转数据\\G2}")
    private String defaultExportPath;

    /**
     * 执行数据刷新任务
     */
    @Override
    @Transactional
    public Map<String, Object> executeRefreshTask(Date startDate, Date endDate, 
                                                  String[] layerNumbers, String exportPath, String backupPath) 
    {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        // 检查是否有正在运行的任务
        if (hasRunningRefreshTask()) {
            result.put("success", false);
            result.put("message", "已有刷新任务正在运行，请等待完成后再试");
            return result;
        }

        // 创建刷新任务记录
        ChemicalRefreshTask refreshTask = new ChemicalRefreshTask();
        refreshTask.setTaskName("数据刷新任务_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()));
        refreshTask.setStartTime(new Date());
        refreshTask.setTaskStatus("RUNNING");
        refreshTask.setStartDate(startDate);
        refreshTask.setEndDate(endDate);
        refreshTask.setLayerNumbers(String.join(",", layerNumbers));
        refreshTask.setExportFilePath(exportPath);
        refreshTask.setExportBackupPath(backupPath);
        refreshTask.setCreateBy("system");
        
        insertRefreshTask(refreshTask);

        try {
            // 执行数据刷新逻辑
            Map<String, Object> processResult = processChemicalDataRefresh(startDate, endDate, layerNumbers);
            
            if ((Boolean) processResult.get("success")) {
                // 导出数据到CSV
                Map<String, Object> exportResult = exportRefreshDataToCsv(startDate, endDate, layerNumbers, exportPath);
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                // 更新任务状态
                refreshTask.setEndTime(new Date());
                refreshTask.setTaskStatus("COMPLETED");
                refreshTask.setTotalGroups((Integer) processResult.get("totalGroups"));
                refreshTask.setModifiedRecords((Integer) processResult.get("modifiedRecords"));
                refreshTask.setDurationMs(duration);
                updateRefreshTask(refreshTask);
                
                result.put("success", true);
                result.put("message", "数据刷新完成");
                result.put("taskId", refreshTask.getRefreshId());
                result.put("totalGroups", processResult.get("totalGroups"));
                result.put("modifiedRecords", processResult.get("modifiedRecords"));
                result.put("duration", duration);
                result.put("exportFile", exportResult.get("filePath"));
                
                log.info("数据刷新任务完成，任务ID: {}, 耗时: {}ms", refreshTask.getRefreshId(), duration);
                
            } else {
                // 处理失败
                refreshTask.setEndTime(new Date());
                refreshTask.setTaskStatus("FAILED");
                refreshTask.setErrorMessage((String) processResult.get("message"));
                updateRefreshTask(refreshTask);
                
                result.put("success", false);
                result.put("message", processResult.get("message"));
            }
            
        } catch (Exception e) {
            log.error("数据刷新任务执行失败", e);
            
            // 更新任务状态为失败
            refreshTask.setEndTime(new Date());
            refreshTask.setTaskStatus("FAILED");
            refreshTask.setErrorMessage(e.getMessage());
            updateRefreshTask(refreshTask);
            
            result.put("success", false);
            result.put("message", "数据刷新失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 处理化学数据刷新逻辑
     */
    @Override
    public Map<String, Object> processChemicalDataRefresh(Date startDate, Date endDate, String[] layerNumbers) 
    {
        Map<String, Object> result = new HashMap<>();
        
        try (Connection localConnection = getLocalConnection();
             Connection cloudConnection = getCloudConnection()) {
            
            log.info("开始数据刷新处理，时间范围: {} - {}", startDate, endDate);
            
            // 查询需要刷新的数据
            List<ChemicalYs> dataToRefresh = chemicalYsMapper.selectChemicalYsByDateRange(startDate, endDate, layerNumbers);
            
            if (dataToRefresh.isEmpty()) {
                result.put("success", true);
                result.put("message", "没有需要刷新的数据");
                result.put("totalGroups", 0);
                result.put("modifiedRecords", 0);
                return result;
            }
            
            // 按工艺、产品、测试分组
            Map<String, List<ChemicalYs>> groupedData = dataToRefresh.stream()
                .collect(Collectors.groupingBy(item -> 
                    item.getProcessName() + "|" + item.getProductName() + "|" + item.getTestName()));
            
            int totalGroups = groupedData.size();
            int modifiedRecords = 0;
            
            log.info("共找到 {} 个项目组，{} 条记录需要处理", totalGroups, dataToRefresh.size());
            
            // 处理每个分组
            for (Map.Entry<String, List<ChemicalYs>> entry : groupedData.entrySet()) {
                String groupKey = entry.getKey();
                List<ChemicalYs> groupData = entry.getValue();
                
                try {
                    // 处理该分组的数据
                    int groupModified = processGroupData(groupData, cloudConnection);
                    modifiedRecords += groupModified;
                    
                    log.debug("处理分组 {}: {} 条记录被修改", groupKey, groupModified);
                    
                } catch (Exception e) {
                    log.error("处理分组 {} 失败", groupKey, e);
                }
            }
            
            result.put("success", true);
            result.put("message", "数据刷新处理完成");
            result.put("totalGroups", totalGroups);
            result.put("modifiedRecords", modifiedRecords);
            
            log.info("数据刷新处理完成，共处理 {} 个分组，修改 {} 条记录", totalGroups, modifiedRecords);
            
        } catch (Exception e) {
            log.error("数据刷新处理失败", e);
            result.put("success", false);
            result.put("message", "数据刷新处理失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 处理分组数据 - 使用新的5大规则引擎
     */
    private int processGroupData(List<ChemicalYs> groupData, Connection cloudConnection) throws SQLException
    {
        if (groupData.isEmpty()) {
            return 0;
        }

        ChemicalYs firstItem = groupData.get(0);
        String processName = firstItem.getProcessName();
        String productName = firstItem.getProductName();
        String testName = firstItem.getTestName();

        log.info("处理分组数据: {} - {} - {} (记录数: {})", processName, productName, testName, groupData.size());

        // 获取规则配置
        RuleConfig ruleConfig = ruleConfigService.selectRuleConfigByNames(productName, processName, testName);
        if (ruleConfig == null) {
            ruleConfig = ruleConfigService.getDefaultRuleConfig();
            log.debug("使用默认规则配置");
        } else {
            log.debug("使用自定义规则配置: {}", ruleConfig.getRuleDescription());
        }

        // 从云端数据库获取控制限制参数
        DataRefreshRuleEngine.ControlLimits controlLimits = getControlLimitsFromCloud(cloudConnection, processName, productName, testName);

        if (controlLimits == null) {
            log.warn("未找到控制限制参数: {} - {} - {}", processName, productName, testName);
            return 0;
        }

        // 转换ChemicalYs为Chemical列表（为了兼容规则引擎）
        List<Chemical> chemicals = convertToChemicalList(groupData);

        // 应用所有数据刷新规则
        int modifiedCount = ruleEngine.applyAllRules(chemicals, controlLimits, ruleConfig);

        // 将修改后的数据转换回ChemicalYs并更新数据库
        for (int i = 0; i < chemicals.size(); i++) {
            Chemical chemical = chemicals.get(i);
            ChemicalYs originalYs = groupData.get(i);

            // 检查是否有修改
            if (!chemical.getExamine1().equals(originalYs.getExamine1()) ||
                !chemical.getExamine2().equals(originalYs.getExamine2())) {

                // 更新ChemicalYs对象
                originalYs.setExamine1(chemical.getExamine1());
                originalYs.setExamine2(chemical.getExamine2());
                originalYs.setExamine1Zs(chemical.getExamine1ZS());
                originalYs.setIsModified(true);
                originalYs.setOriginalExamine1(chemical.getExamine1ZS());

                // 更新数据库
                try {
                    chemicalYsMapper.updateChemicalYs(originalYs);
                    log.debug("更新记录: {} -> {}", chemical.getExamine1ZS(), chemical.getExamine1());
                } catch (Exception e) {
                    log.error("更新数据库失败，ID: {}", originalYs.getId(), e);
                }
            }
        }

        log.info("分组处理完成: {} - {} - {}, 修改记录数: {}", processName, productName, testName, modifiedCount);
        return modifiedCount;
    }

    /**
     * 转换ChemicalYs列表为Chemical列表
     */
    private List<Chemical> convertToChemicalList(List<ChemicalYs> chemicalYsList) {
        return chemicalYsList.stream().map(ys -> {
            Chemical chemical = new Chemical();
            chemical.setId(ys.getId());
            chemical.setExamine1(ys.getExamine1());
            chemical.setExamine2(ys.getExamine2());
            chemical.setExamine1ZS(ys.getExamine1() != null ? ys.getExamine1() : ""); // 保存原始值
            chemical.setUpperLimit(ys.getUpperLimit());
            chemical.setDownLimit(ys.getDownLimit());
            chemical.setProcessName(ys.getProcessName());
            chemical.setProductName(ys.getProductName());
            chemical.setTestName(ys.getTestName());
            return chemical;
        }).collect(Collectors.toList());
    }

    /**
     * 从云端数据库获取控制限制参数
     */
    private DataRefreshRuleEngine.ControlLimits getControlLimitsFromCloud(Connection cloudConnection, String processName,
                                                   String productName, String testName) throws SQLException
    {
        String sql = "SELECT fMean, fSp FROM dbo.SPC_ControlLimits " +
                    "WHERE ProcessName = ? AND ProductName = ? AND TestName = ?";

        try (PreparedStatement stmt = cloudConnection.prepareStatement(sql)) {
            stmt.setString(1, processName);
            stmt.setString(2, productName);
            stmt.setString(3, testName);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    double fMean = rs.getDouble("fMean");
                    double fSp = rs.getDouble("fSp");
                    return new DataRefreshRuleEngine.ControlLimits(fMean, fSp);
                }
            }
        }

        return null;
    }

    /**
     * 计算控制值
     */
    private CalculatedValues calculateControlValues(ControlLimits controlLimits, String testName)
    {
        double intermediateValue = 6 * controlLimits.fSp;

        // 特殊测试项目的样本量调整
        Map<String, Integer> sampleSizeMap = new HashMap<>();
        sampleSizeMap.put("PD全线微蚀量", 3);
        sampleSizeMap.put("TR微蚀微蚀量", 5);

        if (sampleSizeMap.containsKey(testName)) {
            int sampleSize = sampleSizeMap.get(testName);
            intermediateValue = intermediateValue / Math.sqrt(sampleSize);
        }

        double upperControlLimit = controlLimits.fMean + intermediateValue / 2;
        double lowerControlLimit = controlLimits.fMean - intermediateValue / 2;

        return new CalculatedValues(controlLimits.fMean, upperControlLimit, lowerControlLimit, controlLimits.fSp);
    }

    /**
     * 应用数据调整算法
     */
    private double applyDataAdjustmentAlgorithm(double examine1Value, CalculatedValues calculatedValues)
    {
        Random random = new Random();

        // 实现原有JavaFX中的数据调整逻辑
        if (examine1Value > calculatedValues.upperControlLimit) {
            // 调整到上限至中值范围内的随机值
            double adjustedValue = calculatedValues.upperControlLimit - (random.nextDouble() * calculatedValues.upperAdjustmentRange);
            return adjustedValue;
        } else if (examine1Value < calculatedValues.lowerControlLimit) {
            // 调整到下限至中值范围内的随机值
            double adjustedValue = calculatedValues.lowerControlLimit + (random.nextDouble() * calculatedValues.lowerAdjustmentRange);
            return adjustedValue;
        }

        return examine1Value; // 在控制范围内，不调整
    }

    /**
     * 四舍五入检测值
     */
    private double roundExamineValue(double value)
    {
        return Math.round(value * 1000.0) / 1000.0;
    }

    /**
     * 获取本地数据库连接
     */
    private Connection getLocalConnection() throws SQLException
    {
        return DriverManager.getConnection(localDatabaseUrl, localDatabaseUsername, localDatabasePassword);
    }

    /**
     * 获取云端数据库连接
     */
    private Connection getCloudConnection() throws SQLException
    {
        return DriverManager.getConnection(cloudDatabaseUrl, cloudDatabaseUsername, cloudDatabasePassword);
    }

    /**
     * 导出刷新数据到CSV文件
     */
    @Override
    public Map<String, Object> exportRefreshDataToCsv(Date startDate, Date endDate,
                                                      String[] layerNumbers, String exportPath)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询要导出的数据
            List<ChemicalYs> exportData = chemicalYsMapper.selectChemicalYsByDateRange(startDate, endDate, layerNumbers);

            if (exportData.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有数据可导出");
                return result;
            }

            // 生成文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String fileName = "chemical_refresh_" + sdf.format(new Date()) + ".csv";
            String filePath = exportPath + "/" + fileName;

            // 写入CSV文件
            try (FileWriter writer = new FileWriter(filePath)) {
                // 写入CSV头部
                writer.write("id,organization_id,attribute_id,examine_date,shift,staff,process_name," +
                           "product_name,test_name,layer_number,upper_limit,median_specification,down_limit," +
                           "examine1,examine2,examine1_zs,is_modified,original_examine1\n");

                // 写入数据
                for (ChemicalYs chemical : exportData) {
                    writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                        nvl(chemical.getId()),
                        nvl(chemical.getOrganizationId()),
                        nvl(chemical.getAttributeId()),
                        nvl(chemical.getExamineDate()),
                        nvl(chemical.getShift()),
                        nvl(chemical.getStaff()),
                        nvl(chemical.getProcessName()),
                        nvl(chemical.getProductName()),
                        nvl(chemical.getTestName()),
                        nvl(chemical.getLayerNumber()),
                        nvl(chemical.getUpperLimit()),
                        nvl(chemical.getMedianSpecification()),
                        nvl(chemical.getDownLimit()),
                        nvl(chemical.getExamine1()),
                        nvl(chemical.getExamine2()),
                        nvl(chemical.getExamine1Zs()),
                        nvl(chemical.getIsModified()),
                        nvl(chemical.getOriginalExamine1())
                    ));
                }
            }

            result.put("success", true);
            result.put("message", "数据导出成功");
            result.put("filePath", filePath);
            result.put("recordCount", exportData.size());

            log.info("成功导出 {} 条记录到文件: {}", exportData.size(), filePath);

        } catch (IOException e) {
            log.error("导出CSV文件失败", e);
            result.put("success", false);
            result.put("message", "导出失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 空值处理
     */
    private String nvl(Object value)
    {
        return value == null ? "" : value.toString();
    }

    // 实现接口中的其他方法

    @Override
    public List<ChemicalRefreshTask> selectRefreshTaskList(ChemicalRefreshTask refreshTask)
    {
        return refreshTaskMapper.selectChemicalRefreshTaskList(refreshTask);
    }

    @Override
    public ChemicalRefreshTask selectRefreshTaskById(Long refreshId)
    {
        return refreshTaskMapper.selectChemicalRefreshTaskByRefreshId(refreshId);
    }

    @Override
    public int insertRefreshTask(ChemicalRefreshTask refreshTask)
    {
        return refreshTaskMapper.insertChemicalRefreshTask(refreshTask);
    }

    @Override
    public int updateRefreshTask(ChemicalRefreshTask refreshTask)
    {
        return refreshTaskMapper.updateChemicalRefreshTask(refreshTask);
    }

    @Override
    public int deleteRefreshTaskByIds(Long[] refreshIds)
    {
        return refreshTaskMapper.deleteChemicalRefreshTaskByRefreshIds(refreshIds);
    }

    @Override
    public List<ChemicalRefreshTask> getRecentRefreshTasks(int limit)
    {
        return refreshTaskMapper.selectRecentRefreshTasks(limit);
    }

    @Override
    public boolean hasRunningRefreshTask()
    {
        List<ChemicalRefreshTask> runningTasks = refreshTaskMapper.selectRunningRefreshTasks();
        return !runningTasks.isEmpty();
    }

    @Override
    public Map<String, Object> stopRunningRefreshTask()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            List<ChemicalRefreshTask> runningTasks = refreshTaskMapper.selectRunningRefreshTasks();

            for (ChemicalRefreshTask task : runningTasks) {
                task.setTaskStatus("STOPPED");
                task.setEndTime(new Date());
                task.setErrorMessage("任务被手动停止");
                updateRefreshTask(task);
            }

            result.put("success", true);
            result.put("message", "已停止 " + runningTasks.size() + " 个运行中的任务");

        } catch (Exception e) {
            log.error("停止运行中的刷新任务失败", e);
            result.put("success", false);
            result.put("message", "停止任务失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getRefreshTaskStatistics()
    {
        Map<String, Object> statistics = new HashMap<>();

        try {
            int runningCount = refreshTaskMapper.countRefreshTasksByStatus("RUNNING");
            int completedCount = refreshTaskMapper.countRefreshTasksByStatus("COMPLETED");
            int failedCount = refreshTaskMapper.countRefreshTasksByStatus("FAILED");

            statistics.put("runningCount", runningCount);
            statistics.put("completedCount", completedCount);
            statistics.put("failedCount", failedCount);
            statistics.put("totalCount", runningCount + completedCount + failedCount);

        } catch (Exception e) {
            log.error("获取刷新任务统计信息失败", e);
            statistics.put("error", e.getMessage());
        }

        return statistics;
    }

    @Override
    public int cleanHistoryRefreshTasks(int days)
    {
        try {
            return refreshTaskMapper.cleanHistoryRefreshTasks(days);
        } catch (Exception e) {
            log.error("清理历史刷新任务记录失败", e);
            return 0;
        }
    }
}
