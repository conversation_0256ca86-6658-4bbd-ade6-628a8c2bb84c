package com.ruoyi.web.controller.material;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ProcessParamGroup;
import com.ruoyi.system.service.IProcessParamGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 工艺参数组Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/material/processParamGroup")
public class ProcessParamGroupController extends BaseController
{
    @Autowired
    private IProcessParamGroupService processParamGroupService;

    /**
     * 查询工艺参数组列表
     */
    @PreAuthorize("@ss.hasPermi('material:processParamGroup:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessParamGroup processParamGroup)
    {
        startPage();
        List<ProcessParamGroup> list = processParamGroupService.selectProcessParamGroupList(processParamGroup);
        return getDataTable(list);
    }

    /**
     * 根据材料ID查询工艺参数组列表
     */
    @GetMapping("/listByMaterialId/{materialId}")
    public AjaxResult listByMaterialId(@PathVariable("materialId") Long materialId)
    {
        List<ProcessParamGroup> list = processParamGroupService.selectProcessParamGroupByMaterialId(materialId);
        return AjaxResult.success(list);
    }

    /**
     * 导出工艺参数组列表
     */
    @PreAuthorize("@ss.hasPermi('material:processParamGroup:export')")
    @Log(title = "工艺参数组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcessParamGroup processParamGroup)
    {
        List<ProcessParamGroup> list = processParamGroupService.selectProcessParamGroupList(processParamGroup);
        ExcelUtil<ProcessParamGroup> util = new ExcelUtil<ProcessParamGroup>(ProcessParamGroup.class);
        util.exportExcel(response, list, "工艺参数组数据");
    }

    /**
     * 获取工艺参数组详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:processParamGroup:query')")
    @GetMapping(value = "/{groupId}")
    public AjaxResult getInfo(@PathVariable("groupId") Long groupId)
    {
        return AjaxResult.success(processParamGroupService.selectProcessParamGroupByGroupId(groupId));
    }

    /**
     * 新增工艺参数组
     */
    @PreAuthorize("@ss.hasPermi('material:processParamGroup:add')")
    @Log(title = "工艺参数组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessParamGroup processParamGroup)
    {
        return toAjax(processParamGroupService.insertProcessParamGroup(processParamGroup));
    }

    /**
     * 修改工艺参数组
     */
    @PreAuthorize("@ss.hasPermi('material:processParamGroup:edit')")
    @Log(title = "工艺参数组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessParamGroup processParamGroup)
    {
        return toAjax(processParamGroupService.updateProcessParamGroup(processParamGroup));
    }

    /**
     * 删除工艺参数组
     */
    @PreAuthorize("@ss.hasPermi('material:processParamGroup:remove')")
    @Log(title = "工艺参数组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{groupIds}")
    public AjaxResult remove(@PathVariable Long[] groupIds)
    {
        return toAjax(processParamGroupService.deleteProcessParamGroupByGroupIds(groupIds));
    }

    /**
     * 获取工艺参数组选项数据
     */
    @GetMapping("/options")
    public AjaxResult options(String type)
    {
        List<String> options = processParamGroupService.selectProcessParamGroupOptions(type);
        return AjaxResult.success(options);
    }
}