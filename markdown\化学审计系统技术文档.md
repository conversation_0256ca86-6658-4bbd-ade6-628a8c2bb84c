# 化学审计系统技术文档

## 1. 项目概述

### 1.1 项目背景
本项目旨在将原有的JavaFX桌面应用程序（chemical和chemical-refresh）迁移到若依框架下，实现Web化的化学数据审计系统。系统主要功能包括：
- 从Pulsar消息队列接收化学检测数据
- 对数据进行处理和调整
- 提供数据查询、导出、监控等功能
- 支持数据刷新和规则管理

### 1.2 技术架构
- **后端框架**: 若依框架 (Spring Boot + MyBatis)
- **前端框架**: Vue.js + Element UI
- **数据库**: SQL Server
- **消息队列**: Apache Pulsar
- **文件导出**: Apache POI (Excel), CSV

### 1.3 系统模块
- **数据监控模块**: 实时监控任务状态和数据处理进度
- **任务控制模块**: 控制数据读取、处理任务的启动、暂停、停止
- **数据管理模块**: 管理原始数据、应审数据、数据日志
- **规则管理模块**: 管理数据处理规则
- **数据导出模块**: 支持CSV和Excel格式导出
- **系统配置模块**: 管理系统参数配置

## 2. 数据库设计

### 2.1 核心表结构

#### 2.1.1 chemical (化学检测数据主表)
存储从Pulsar消息队列接收的原始化学检测数据。

```sql
CREATE TABLE chemical (
    id VARCHAR(50) PRIMARY KEY,                    -- 主键ID
    organization_id BIGINT,                        -- 组织ID
    attribute_id BIGINT,                           -- 属性ID
    examine_date DATETIME,                         -- 检测日期
    shift VARCHAR(20),                             -- 班次
    staff VARCHAR(100),                            -- 操作员工
    department_code VARCHAR(50),                   -- 部门代码
    process_name VARCHAR(100),                     -- 工艺名称
    product_name VARCHAR(100),                     -- 产品名称
    test_name VARCHAR(100),                        -- 测试名称
    layer_number VARCHAR(50),                      -- 层号
    upper_limit VARCHAR(20),                       -- 上限
    median_specification VARCHAR(20),              -- 中位规格
    down_limit VARCHAR(20),                        -- 下限
    examine1 VARCHAR(20),                          -- 检测值1
    examine2 VARCHAR(20),                          -- 检测值2
    is_exported BIT DEFAULT 0,                     -- 是否已导出
    not_process BIT DEFAULT 0,                     -- 是否不处理
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间
    -- 其他字段...
);
```

#### 2.1.2 chemical_ys (化学应审数据表)
存储经过处理和调整后的化学检测数据。

```sql
CREATE TABLE chemical_ys (
    id VARCHAR(50) PRIMARY KEY,                    -- 主键ID (关联chemical表)
    examine1 VARCHAR(20),                          -- 调整后检测值1
    examine2 VARCHAR(20),                          -- 调整后检测值2
    examine1_zs VARCHAR(20),                       -- 检测值1真实值
    is_modified BIT DEFAULT 0,                     -- 是否被修改过
    original_examine1 VARCHAR(20),                 -- 原始检测值1
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间
    -- 其他字段与chemical表类似...
);
```

#### 2.1.3 chemical_rule (处理规则表)
存储特殊的数据处理规则。

```sql
CREATE TABLE chemical_rule (
    rule_id BIGINT IDENTITY(1,1) PRIMARY KEY,      -- 规则ID
    product_name VARCHAR(100) NOT NULL,            -- 产品名称
    process_name VARCHAR(100) NOT NULL,            -- 工艺名称
    test_name VARCHAR(100) NOT NULL,               -- 测试名称
    is_refresh BIT DEFAULT 0,                      -- 是否刷新
    rule_type VARCHAR(20) DEFAULT 'NORMAL',        -- 规则类型
    rule_desc VARCHAR(200),                        -- 规则描述
    rule_config TEXT,                              -- 规则配置(JSON格式)
    status CHAR(1) DEFAULT '0',                    -- 状态
    create_time DATETIME DEFAULT GETDATE()         -- 创建时间
);
```

#### 2.1.4 chemical_task_monitor (任务监控表)
记录任务执行状态和监控信息。

```sql
CREATE TABLE chemical_task_monitor (
    task_id BIGINT IDENTITY(1,1) PRIMARY KEY,      -- 任务ID
    task_name VARCHAR(100) NOT NULL,               -- 任务名称
    task_type VARCHAR(20) NOT NULL,                -- 任务类型
    task_status VARCHAR(20) DEFAULT 'WAITING',     -- 任务状态
    start_time DATETIME,                           -- 开始时间
    end_time DATETIME,                             -- 结束时间
    total_records INT DEFAULT 0,                   -- 总记录数
    processed_records INT DEFAULT 0,               -- 已处理记录数
    success_records INT DEFAULT 0,                 -- 成功记录数
    error_records INT DEFAULT 0,                   -- 错误记录数
    progress_percent DECIMAL(5,2) DEFAULT 0,       -- 进度百分比
    create_time DATETIME DEFAULT GETDATE()         -- 创建时间
);
```

### 2.2 数据库初始化
执行以下SQL文件进行数据库初始化：
1. `sql/chemical_audit_tables.sql` - 创建表结构
2. `sql/chemical_audit_menu.sql` - 创建菜单权限

## 3. 后端架构设计

### 3.1 包结构
```
com.ruoyi.system
├── domain/                 # 实体类
│   ├── Chemical.java
│   ├── ChemicalYs.java
│   ├── ChemicalRule.java
│   └── ChemicalTaskMonitor.java
├── mapper/                 # 数据访问层
│   ├── ChemicalMapper.java
│   ├── ChemicalYsMapper.java
│   ├── ChemicalRuleMapper.java
│   └── ChemicalTaskMonitorMapper.java
├── service/                # 服务接口
│   ├── IChemicalService.java
│   ├── IChemicalTaskService.java
│   └── IChemicalExportService.java
└── service/impl/           # 服务实现
    ├── ChemicalServiceImpl.java
    ├── ChemicalTaskServiceImpl.java
    └── ChemicalExportServiceImpl.java
```

### 3.2 核心服务类

#### 3.2.1 IChemicalService
化学数据管理服务，提供基础的CRUD操作和数据处理功能。

主要方法：
- `selectChemicalList()` - 查询化学数据列表
- `insertChemical()` - 新增化学数据
- `processChemicalData()` - 处理化学数据
- `processMessageData()` - 处理Pulsar消息数据

#### 3.2.2 IChemicalTaskService
任务控制服务，管理数据读取、处理任务的生命周期。

主要方法：
- `startDataReadingTask()` - 启动数据读取任务
- `pauseDataReadingTask()` - 暂停数据读取任务
- `stopDataReadingTask()` - 停止数据读取任务
- `getTaskStatus()` - 获取任务状态
- `getTaskMonitorInfo()` - 获取任务监控信息

#### 3.2.3 IChemicalExportService
数据导出服务，支持CSV和Excel格式导出。

主要方法：
- `exportToCSV()` - 导出CSV文件
- `exportToExcel()` - 导出Excel文件
- `getExportHistory()` - 获取导出历史记录
- `downloadExportFile()` - 下载导出文件

### 3.3 数据处理流程

#### 3.3.1 Pulsar消息处理流程
1. 初始化Pulsar客户端和消费者
2. 监听指定主题的消息
3. 接收到消息后解析为Chemical对象
4. 验证数据完整性
5. 插入到chemical表
6. 记录处理日志

#### 3.3.2 数据处理流程
1. 查询未处理的chemical数据
2. 连接远程SPC-G2数据库获取控制限制参数
3. 根据规则计算调整后的检测值
4. 插入处理后的数据到chemical_ys表
5. 更新原数据的处理状态

## 4. 前端架构设计

### 4.1 页面结构
```
src/views/chemical/
├── monitor/                # 数据监控
│   └── index.vue
├── task/                   # 任务控制
│   └── index.vue
├── data/                   # 数据管理
│   ├── chemical/
│   │   └── index.vue
│   ├── chemical-ys/
│   │   └── index.vue
│   └── log/
│       └── index.vue
├── rule/                   # 规则管理
│   └── index.vue
├── export/                 # 数据导出
│   └── index.vue
└── config/                 # 系统配置
    └── index.vue
```

### 4.2 核心组件

#### 4.2.1 数据监控页面 (monitor/index.vue)
- 实时显示任务状态
- 显示处理进度和统计信息
- 实时日志输出
- 系统状态监控

#### 4.2.2 任务控制页面 (task/index.vue)
- 任务启动、暂停、停止控制
- 任务配置管理
- Pulsar连接状态监控
- 任务历史记录

#### 4.2.3 数据管理页面
- 原始数据查询和管理
- 应审数据查询和编辑
- 数据重新处理功能
- 数据状态管理

## 5. 核心功能实现

### 5.1 Pulsar消息队列集成
```java
// Pulsar客户端配置
PulsarClient pulsarClient = PulsarClient.builder()
    .serviceUrl("pulsar://pulsar.scc.com:6650")
    .authentication(AuthenticationFactory.token(TOKEN))
    .build();

// 消费者配置
Consumer<byte[]> consumer = pulsarClient.newConsumer(Schema.BYTES)
    .subscriptionName("G2-Chemical-Web")
    .subscriptionType(SubscriptionType.Exclusive)
    .topic("persistent://spc/331-interface/labMedicineData")
    .subscribe();
```

### 5.2 数据处理算法
```java
// 控制值计算
private CalculatedValues calculateControlValues(ControlLimits controlLimits, String testName) {
    double intermediateValue = 6 * controlLimits.fSp;
    
    // 特殊测试项目的样本量调整
    Map<String, Integer> sampleSizeMap = new HashMap<>();
    sampleSizeMap.put("PD全线微蚀量", 3);
    sampleSizeMap.put("TR微蚀微蚀量", 5);
    
    if (sampleSizeMap.containsKey(testName)) {
        int sampleSize = sampleSizeMap.get(testName);
        intermediateValue = intermediateValue / Math.sqrt(sampleSize);
    }
    
    double upperControlLimit = controlLimits.fMean + intermediateValue / 2;
    double lowerControlLimit = controlLimits.fMean - intermediateValue / 2;
    double lcl1 = (7.0 / 8.0) * lowerControlLimit + (1.0 / 8.0) * upperControlLimit;
    double ucl1 = (1.0 / 8.0) * lowerControlLimit + (7.0 / 8.0) * upperControlLimit;
    
    return new CalculatedValues(upperControlLimit, lowerControlLimit, lcl1, ucl1);
}
```

### 5.3 数据导出功能
支持CSV和Excel两种格式的数据导出：
- 按时间范围筛选数据
- 按层号筛选数据
- 自动生成文件名
- 支持文件下载和历史记录管理

## 6. 系统配置

### 6.1 必需的系统配置
```properties
# Pulsar配置
pulsar.broker.url=pulsar://pulsar.scc.com:6650
pulsar.auth.token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzcGMtMzMxIn0.U7QrwJTShmbcucMtIqqzC5gfK8mbgsSAF20yyed1W6A
pulsar.topic.medicine=persistent://spc/331-interface/labMedicineData
pulsar.subscription.name=G2-Chemical-Web

# 数据库配置
database.local.url=*****************************************************************
database.cloud.url=jdbc:sqlserver://************;DatabaseName=SPC-G2;encrypt=true;trustServerCertificate=true;sslProtocol=TLSv1

# 导出配置
export.file.path=E:\\测试数据\\应审抛转数据\\G2
export.backup.path=

# 任务配置
task.schedule.interval=30
data.process.batch.size=100
```

### 6.2 菜单权限配置
系统提供完整的菜单权限体系：
- 一级菜单：化学审计
- 二级菜单：数据监控、任务控制、数据管理、规则管理、数据导出、系统配置
- 按钮权限：查询、新增、修改、删除、导出、启动任务、暂停任务等

## 7. 部署说明

### 7.1 环境要求
- JDK 1.8+
- SQL Server 2012+
- Apache Pulsar
- Node.js 12+ (前端构建)

### 7.2 部署步骤
1. 执行数据库初始化脚本
2. 配置application.yml中的数据库连接
3. 配置Pulsar连接参数
4. 构建前端项目
5. 启动后端服务
6. 访问系统并配置权限

### 7.3 注意事项
- 确保Pulsar服务正常运行
- 确保远程SPC-G2数据库连接正常
- 配置正确的文件导出路径
- 定期清理历史任务记录和导出文件

## 8. API接口文档

### 8.1 化学数据管理接口

#### 8.1.1 查询化学数据列表
- **接口地址**: `GET /chemical/data/list`
- **请求参数**:
  ```json
  {
    "pageNum": 1,
    "pageSize": 10,
    "examineDate": "2024-01-01",
    "departmentCode": "G2-001",
    "processName": "微蚀",
    "productName": "PD全线",
    "testName": "微蚀量",
    "layerNumber": "L1",
    "isExported": false,
    "notProcess": false
  }
  ```
- **响应数据**:
  ```json
  {
    "code": 200,
    "msg": "查询成功",
    "rows": [...],
    "total": 100
  }
  ```

#### 8.1.2 新增化学数据
- **接口地址**: `POST /chemical/data`
- **请求参数**: Chemical对象JSON
- **响应数据**: 标准响应格式

#### 8.1.3 修改化学数据
- **接口地址**: `PUT /chemical/data`
- **请求参数**: Chemical对象JSON
- **响应数据**: 标准响应格式

#### 8.1.4 删除化学数据
- **接口地址**: `DELETE /chemical/data/{ids}`
- **请求参数**: 数据ID数组
- **响应数据**: 标准响应格式

### 8.2 任务控制接口

#### 8.2.1 启动数据读取任务
- **接口地址**: `POST /chemical/task/start`
- **响应数据**:
  ```json
  {
    "code": 200,
    "msg": "任务启动成功",
    "data": {
      "success": true,
      "taskId": 123,
      "message": "数据读取任务已启动"
    }
  }
  ```

#### 8.2.2 获取任务状态
- **接口地址**: `GET /chemical/task/status`
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "dataReading": {
        "status": "RUNNING",
        "taskName": "Pulsar数据读取",
        "startTime": "2024-01-01 10:00:00",
        "processedRecords": 1000
      },
      "dataProcessing": {...},
      "logProcessing": {...}
    }
  }
  ```

### 8.3 数据导出接口

#### 8.3.1 导出CSV文件
- **接口地址**: `POST /chemical/export/csv`
- **请求参数**:
  ```json
  {
    "startDate": "2024-01-01 00:00:00",
    "endDate": "2024-01-31 23:59:59",
    "layerNumbers": ["L1", "L2", "L3"]
  }
  ```

#### 8.3.2 导出Excel文件
- **接口地址**: `POST /chemical/export/excel`
- **请求参数**: 同CSV导出

### 8.4 监控接口

#### 8.4.1 获取系统概览
- **接口地址**: `GET /chemical/monitor/overview`
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "dataStatistics": {
        "totalCount": 10000,
        "unprocessedCount": 100,
        "exportedCount": 8000,
        "todayNewCount": 50,
        "processRate": 99.0,
        "exportRate": 80.0
      },
      "taskStatus": {...},
      "systemStatus": {...},
      "pulsarStatus": {...}
    }
  }
  ```

## 9. 前端组件文档

### 9.1 数据监控页面 (monitor/index.vue)

#### 9.1.1 功能特性
- 实时显示系统概览数据（总数据量、未处理数据、已导出数据、今日新增）
- 任务状态监控（数据读取、数据处理、日志处理任务状态）
- 系统性能监控（内存使用率、CPU使用率、处理成功率）
- 实时日志显示，支持自动滚动和日志级别过滤
- 自动刷新机制，每30秒更新概览数据，每5秒更新日志

#### 9.1.2 主要方法
- `loadData()`: 加载系统概览数据
- `loadRealtimeLogs()`: 加载实时日志
- `refreshPerformance()`: 刷新性能指标
- `startAutoRefresh()`: 开始自动刷新
- `clearLogs()`: 清空日志

### 9.2 任务控制页面 (task/index.vue)

#### 9.2.1 功能特性
- 三个任务面板：数据读取任务、数据处理任务、日志处理任务
- 任务控制按钮：启动、暂停、停止、重启
- Pulsar连接状态监控和控制
- 任务历史记录查询和详情查看
- 实时状态轮询，每10秒更新一次

#### 9.2.2 主要方法
- `startDataReading()`: 启动数据读取任务
- `pauseDataReading()`: 暂停数据读取任务
- `stopDataReading()`: 停止数据读取任务
- `restartDataReading()`: 重启数据读取任务
- `initPulsarClient()`: 初始化Pulsar客户端
- `closePulsarClient()`: 关闭Pulsar客户端

### 9.3 数据管理页面 (data/chemical/index.vue)

#### 9.3.1 功能特性
- 化学数据列表查询，支持多条件筛选
- 数据的增删改查操作
- 批量操作：标记已导出、标记不处理、重置状态
- 数据重新处理功能
- 数据详情查看
- 数据导出功能

#### 9.3.2 主要方法
- `getList()`: 获取数据列表
- `handleAdd()`: 新增数据
- `handleUpdate()`: 修改数据
- `handleDelete()`: 删除数据
- `handleReprocess()`: 重新处理数据
- `batchMarkAsExported()`: 批量标记已导出

## 10. 部署指南

### 10.1 环境准备
```bash
# 1. 安装JDK 1.8+
java -version

# 2. 安装Node.js 12+
node -v
npm -v

# 3. 安装SQL Server 2012+
# 确保SQL Server服务正常运行

# 4. 安装Apache Pulsar
# 下载并启动Pulsar服务
```

### 10.2 数据库初始化
```sql
-- 1. 创建数据库
CREATE DATABASE [g2-qis];

-- 2. 执行表结构脚本
-- 运行 sql/chemical_audit_tables.sql

-- 3. 执行菜单权限脚本
-- 运行 sql/chemical_audit_menu.sql
```

### 10.3 后端部署
```bash
# 1. 修改配置文件
# 编辑 application.yml，配置数据库连接和Pulsar参数

# 2. 编译打包
mvn clean package -Dmaven.test.skip=true

# 3. 启动服务
java -jar ruoyi-admin.jar
```

### 10.4 前端部署
```bash
# 1. 安装依赖
npm install

# 2. 修改配置
# 编辑 .env.production，配置后端API地址

# 3. 构建生产版本
npm run build:prod

# 4. 部署到Web服务器
# 将dist目录内容部署到Nginx或Apache
```

### 10.5 Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }

    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 11. 运维监控

### 11.1 日志管理
- 应用日志：`logs/ruoyi.log`
- 错误日志：`logs/error.log`
- 访问日志：Nginx access.log

### 11.2 性能监控
- JVM监控：使用JConsole或VisualVM
- 数据库监控：SQL Server性能监视器
- 应用监控：集成Micrometer + Prometheus

### 11.3 告警配置
- 数据库连接异常告警
- Pulsar连接断开告警
- 数据处理失败率过高告警
- 系统资源使用率过高告警

## 12. 常见问题

### 12.1 Pulsar连接问题
**问题**: 无法连接到Pulsar服务
**解决方案**:
1. 检查Pulsar服务是否正常运行
2. 验证网络连接和防火墙设置
3. 确认认证Token是否正确
4. 检查主题和订阅配置

### 12.2 数据处理异常
**问题**: 数据处理失败或处理缓慢
**解决方案**:
1. 检查远程数据库连接
2. 优化SQL查询性能
3. 调整批处理大小
4. 增加错误重试机制

### 12.3 前端页面加载慢
**问题**: 页面加载速度慢
**解决方案**:
1. 启用Gzip压缩
2. 配置CDN加速
3. 优化图片和静态资源
4. 使用懒加载和分页

---

*本文档将随着项目进展持续更新*
