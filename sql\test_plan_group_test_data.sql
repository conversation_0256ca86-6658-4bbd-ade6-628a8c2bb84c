-- 测试方案配置测试数据
-- 基于codebuddy.sql中的表结构创建测试数据

-- 清空现有数据
DELETE FROM test_param_item;
DELETE FROM test_plan_group;

-- 重置自增ID
ALTER TABLE test_plan_group AUTO_INCREMENT = 1;
ALTER TABLE test_param_item AUTO_INCREMENT = 1;

-- 插入测试方案组数据
INSERT INTO test_plan_group (plan_code, performance_type, performance_name, test_equipment, attachments, remark, create_by, create_time) VALUES
('TP001', '机械性能', '拉伸强度测试方案', '万能试验机', 'http://example.com/file1.pdf,http://example.com/file2.jpg', '标准拉伸测试方案，适用于金属材料', 'admin', NOW()),
('TP002', '热性能', '热膨胀系数测试方案', '热膨胀仪', 'http://example.com/file3.doc', '热膨胀性能测试，温度范围-50℃到200℃', 'admin', NOW()),
('TP003', '电性能', '电阻率测试方案', '四探针测试仪', NULL, '电阻率测试方案，适用于导电材料', 'admin', NOW()),
('TP004', '机械性能', '冲击韧性测试方案', '冲击试验机', 'http://example.com/file4.pdf', '冲击韧性测试标准，符合GB/T标准', 'admin', NOW()),
('TP005', '化学性能', '耐腐蚀性测试方案', '盐雾试验箱', 'http://example.com/file5.xlsx,http://example.com/file6.png', '耐腐蚀性能评估，盐雾试验', 'admin', NOW()),
('TP006', '热性能', '导热系数测试方案', '导热系数测试仪', NULL, '导热性能测试，稳态法测量', 'admin', NOW()),
('TP007', '机械性能', '硬度测试方案', '硬度计', 'http://example.com/file7.pdf', '硬度测试标准方案，多种硬度制式', 'admin', NOW()),
('TP008', '电性能', '介电常数测试方案', '介电常数测试仪', 'http://example.com/file8.doc,http://example.com/file9.jpg', '介电性能测试，频率范围1Hz-1MHz', 'admin', NOW()),
('TP009', '机械性能', '疲劳测试方案', '疲劳试验机', NULL, '疲劳性能测试，循环加载', 'admin', NOW()),
('TP010', '热性能', '热稳定性测试方案', '热重分析仪', 'http://example.com/file10.pdf', '热稳定性评估，TGA分析', 'admin', NOW());

-- 插入测试参数明细数据
-- TP001 拉伸强度测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(1, '拉伸强度', '≥500', 'MPa', NULL, '最小拉伸强度要求', 'admin', NOW()),
(1, '屈服强度', '≥350', 'MPa', NULL, '最小屈服强度要求', 'admin', NOW()),
(1, '延伸率', '≥15', '%', NULL, '最小延伸率要求', 'admin', NOW()),
(1, '试验速度', '2±0.5', 'mm/min', NULL, '拉伸试验速度', 'admin', NOW()),
(1, '试样数量', '5', '个', NULL, '每组试验试样数量', 'admin', NOW());

-- TP002 热膨胀系数测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(2, '线膨胀系数', '10-20', '×10⁻⁶/K', NULL, '温度范围20-100℃', 'admin', NOW()),
(2, '测试温度范围', '-50~200', '℃', NULL, '测试温度范围', 'admin', NOW()),
(2, '升温速率', '5±1', '℃/min', NULL, '升温速率控制', 'admin', NOW()),
(2, '试样长度', '25±0.1', 'mm', NULL, '标准试样长度', 'admin', NOW());

-- TP003 电阻率测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(3, '体积电阻率', '≤10⁻³', 'Ω·cm', NULL, '导电材料要求', 'admin', NOW()),
(3, '表面电阻率', '≤10²', 'Ω/sq', NULL, '表面电阻率要求', 'admin', NOW()),
(3, '测试电压', '1', 'V', NULL, '测试电压', 'admin', NOW()),
(3, '测试时间', '60', 's', NULL, '稳定测试时间', 'admin', NOW());

-- TP004 冲击韧性测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(4, '冲击韧性', '≥30', 'J/cm²', NULL, '最小冲击韧性要求', 'admin', NOW()),
(4, '试样尺寸', '10×10×55', 'mm', NULL, '标准试样尺寸', 'admin', NOW()),
(4, '缺口深度', '2±0.1', 'mm', NULL, 'V型缺口深度', 'admin', NOW()),
(4, '试验温度', '20±2', '℃', NULL, '室温试验', 'admin', NOW());

-- TP005 耐腐蚀性测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(5, '盐雾浓度', '5±1', '%', NULL, 'NaCl溶液浓度', 'admin', NOW()),
(5, '试验温度', '35±2', '℃', NULL, '盐雾试验温度', 'admin', NOW()),
(5, '试验时间', '96', 'h', NULL, '连续试验时间', 'admin', NOW()),
(5, '腐蚀等级', '≤3', '级', NULL, '腐蚀等级要求', 'admin', NOW());

-- TP006 导热系数测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(6, '导热系数', '≥100', 'W/(m·K)', NULL, '最小导热系数要求', 'admin', NOW()),
(6, '测试温度', '25±1', '℃', NULL, '测试环境温度', 'admin', NOW()),
(6, '试样厚度', '10±0.1', 'mm', NULL, '标准试样厚度', 'admin', NOW()),
(6, '热流密度', '1000±50', 'W/m²', NULL, '施加热流密度', 'admin', NOW());

-- TP007 硬度测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(7, '布氏硬度', '150-200', 'HB', NULL, '布氏硬度范围', 'admin', NOW()),
(7, '洛氏硬度', '20-30', 'HRC', NULL, '洛氏硬度范围', 'admin', NOW()),
(7, '压头直径', '10', 'mm', NULL, '布氏硬度压头', 'admin', NOW()),
(7, '试验力', '3000', 'kgf', NULL, '布氏硬度试验力', 'admin', NOW()),
(7, '保持时间', '15', 's', NULL, '试验力保持时间', 'admin', NOW());

-- TP008 介电常数测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(8, '介电常数', '3.0-5.0', '', NULL, '相对介电常数', 'admin', NOW()),
(8, '介电损耗', '≤0.01', '', NULL, '介电损耗因子', 'admin', NOW()),
(8, '测试频率', '1000', 'Hz', NULL, '测试频率', 'admin', NOW()),
(8, '测试电压', '1', 'V', NULL, '测试电压', 'admin', NOW());

-- TP009 疲劳测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(9, '疲劳寿命', '≥10⁶', '次', NULL, '最小疲劳寿命', 'admin', NOW()),
(9, '应力比', '0.1', '', NULL, 'R=σmin/σmax', 'admin', NOW()),
(9, '加载频率', '10', 'Hz', NULL, '循环加载频率', 'admin', NOW()),
(9, '最大应力', '300', 'MPa', NULL, '循环最大应力', 'admin', NOW());

-- TP010 热稳定性测试方案的参数
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, attachments, remark, create_by, create_time) VALUES
(10, '分解温度', '≥250', '℃', NULL, '5%质量损失温度', 'admin', NOW()),
(10, '升温速率', '10', '℃/min', NULL, 'TGA升温速率', 'admin', NOW()),
(10, '气氛', 'N₂', '', NULL, '氮气保护气氛', 'admin', NOW()),
(10, '试样质量', '10±2', 'mg', NULL, 'TGA试样质量', 'admin', NOW());

-- 验证数据插入结果
SELECT 
    COUNT(*) as '测试方案组总数',
    COUNT(DISTINCT performance_type) as '性能类型数',
    COUNT(DISTINCT test_equipment) as '测试设备数'
FROM test_plan_group;

SELECT 
    COUNT(*) as '测试参数明细总数',
    COUNT(DISTINCT param_name) as '不同参数名称数',
    COUNT(DISTINCT unit) as '不同单位数'
FROM test_param_item;

-- 显示各方案组的参数数量
SELECT 
    g.plan_code as '方案编号',
    g.performance_type as '性能类型',
    g.performance_name as '方案名称',
    COUNT(p.test_param_id) as '参数数量'
FROM test_plan_group g
LEFT JOIN test_param_item p ON g.plan_group_id = p.plan_group_id
GROUP BY g.plan_group_id, g.plan_code, g.performance_type, g.performance_name
ORDER BY g.plan_code;

SELECT '测试方案配置测试数据创建完成！' as '状态';
