-- =============================================
-- 修复模块名称拼写错误
-- 将 ruoyi-chamicalAudit-g2 修正为 ruoyi-chemicalAudit-g2
-- =============================================

-- 注意：此脚本主要用于文档说明，实际的模块重命名需要在文件系统层面进行
-- 建议的操作步骤：

-- 1. 停止应用服务
-- 2. 重命名模块目录：ruoyi-chamicalAudit-g2 -> ruoyi-chemicalAudit-g2
-- 3. 更新 pom.xml 中的模块引用
-- 4. 更新包名引用（如果需要）
-- 5. 重新编译和部署

-- 数据库层面不需要修改，因为表名和字段名都是正确的

-- 如果已经有数据，确保备份数据库
PRINT '请确保在重命名模块前备份数据库和代码';
PRINT '模块重命名步骤：';
PRINT '1. 停止应用服务';
PRINT '2. 重命名目录：ruoyi-chamicalAudit-g2 -> ruoyi-chemicalAudit-g2';
PRINT '3. 更新父级 pom.xml 中的 <module> 引用';
PRINT '4. 更新其他模块中对该模块的依赖引用';
PRINT '5. 重新编译：mvn clean compile';
PRINT '6. 重新启动应用';
