<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold; font-size: 16px;">测试方案管理</span>
        <div style="float: right;">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">批量删除</el-button>
          <el-button type="success" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
        </div>
      </div>

      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px" style="margin-bottom: 15px;">
        <el-form-item label="方案编号" prop="planCode">
          <el-autocomplete
            v-model="queryParams.planCode"
            :fetch-suggestions="queryPlanCodeSuggestions"
            placeholder="请输入方案编号"
            clearable
            style="width: 200px;"
            @select="handlePlanCodeSelect"
            @focus="handlePlanCodeFocus"
            :trigger-on-focus="true"
          />
        </el-form-item>
        <el-form-item label="性能类型" prop="performanceType">
          <el-autocomplete
            v-model="queryParams.performanceType"
            :fetch-suggestions="queryPerformanceTypeSuggestions"
            placeholder="请输入性能类型"
            clearable
            style="width: 200px;"
            @focus="handlePerformanceTypeFocus"
            :trigger-on-focus="true"
          />
        </el-form-item>
        <el-form-item label="测试设备" prop="testEquipment">
          <el-autocomplete
            v-model="queryParams.testEquipment"
            :fetch-suggestions="queryTestEquipmentSuggestions"
            placeholder="请输入测试设备"
            clearable
            style="width: 200px;"
            @focus="handleTestEquipmentFocus"
            :trigger-on-focus="true"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="testPlanList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        ref="multipleTable"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="planCode" label="方案编号" min-width="150" show-overflow-tooltip />
        <el-table-column prop="performanceType" label="性能类型" width="120" />
        <el-table-column prop="performanceName" label="性能名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="testEquipment" label="测试设备" width="120" />
        <el-table-column prop="testParameter" label="测试参数" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateBy" label="更新人" width="100" />
        <el-table-column prop="updateTime" label="更新时间" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="附件" width="80" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.attachments && scope.row.attachments.trim()" size="mini" type="text" @click.stop="handleViewAttachments(scope.row.attachments)">查看</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="handleCopy(scope.row)">复制</el-button>
            <el-button size="mini" type="text" style="color: #f56c6c" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        style="margin-top: 15px;"
      />
    </el-card>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body v-drag>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="方案编号" prop="planCode">
              <el-input v-model="form.planCode" placeholder="请输入方案编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性能类型" prop="performanceType">
              <el-input v-model="form.performanceType" placeholder="请输入性能类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="性能名称" prop="performanceName">
              <el-input v-model="form.performanceName" placeholder="请输入性能名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测试设备" prop="testEquipment">
              <el-input v-model="form.testEquipment" placeholder="请输入测试设备" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="测试参数">
          <el-input v-model="form.testParameter" type="textarea" placeholder="请输入测试参数" :rows="4" />
        </el-form-item>
        <el-form-item label="附件上传">
          <el-upload
            ref="upload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="fileList"
            :on-success="handleUploadSuccess"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持多文件上传，单个文件大小不超过10MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 附件查看对话框 -->
    <el-dialog title="附件列表" :visible.sync="attachmentDialogVisible" width="600px" append-to-body v-drag>
      <el-table :data="attachmentList" style="width: 100%">
        <el-table-column prop="name" label="文件名" show-overflow-tooltip />
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="downloadAttachment(scope.row.url, scope.row.name)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  listTestPlan, getTestPlan, delTestPlan, addTestPlan, updateTestPlan,
  exportTestPlan, getTestPlanOptions
} from "@/api/material/testPlan";
import { getToken } from "@/utils/auth";

export default {
  name: "TestPlan",
  directives: {
    // 拖拽指令
    drag: {
      bind(el) {
        const dialogHeaderEl = el.querySelector('.el-dialog__header');
        const dragDom = el.querySelector('.el-dialog');
        dialogHeaderEl.style.cursor = 'move';

        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);

        dialogHeaderEl.onmousedown = (e) => {
          // 鼠标按下，计算当前元素距离可视区的距离
          const disX = e.clientX - dialogHeaderEl.offsetLeft;
          const disY = e.clientY - dialogHeaderEl.offsetTop;

          // 获取到的值带px 正则匹配替换
          let styL, styT;

          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
          if (sty.left.includes('%')) {
            styL = +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100);
            styT = +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100);
          } else {
            styL = +sty.left.replace(/px/g, '');
            styT = +sty.top.replace(/px/g, '');
          }

          document.onmousemove = function (e) {
            // 通过事件委托，计算移动的距离
            const l = e.clientX - disX;
            const t = e.clientY - disY;

            // 移动当前元素
            dragDom.style.left = `${l + styL}px`;
            dragDom.style.top = `${t + styT}px`;

            // 将此时的位置传出去
            // binding.value({x:e.pageX,y:e.pageY})
          };

          document.onmouseup = function (e) {
            document.onmousemove = null;
            document.onmouseup = null;
          };
        }
      }
    }
  },
  data() {
    return {
      // 上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 测试方案表格数据
      testPlanList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planCode: null,
        performanceType: null,
        testEquipment: null
      },
      // 表单参数
      form: {},
      // 表单校验
      // 表单校验
      rules: {
        planCode: [
          { required: true, message: "方案编号不能为空", trigger: "blur" }
        ],
        performanceType: [
          { required: true, message: "性能类型不能为空", trigger: "blur" }
        ]
      },

      // 附件相关
      fileList: [],
      attachmentDialogVisible: false,
      attachmentList: [],

      // 搜索建议数据
      planCodeSuggestions: [],
      performanceTypeSuggestions: [],
      testEquipmentSuggestions: []
    };
  },
  created() {
    this.getList();
    this.loadSuggestions();
  },
  methods: {
    /** 加载搜索建议数据 */
    loadSuggestions() {
      // 获取方案编号建议
      getTestPlanOptions({ type: 'planCode' }).then(response => {
        console.log('方案编号选项响应：', response);
        if (response.data && Array.isArray(response.data)) {
          this.planCodeSuggestions = response.data.map(item => ({ value: item }));
        }
      }).catch(error => {
        console.error('获取方案编号选项失败：', error);
      });

      // 获取性能类型建议
      getTestPlanOptions({ type: 'performanceType' }).then(response => {
        console.log('性能类型选项响应：', response);
        if (response.data && Array.isArray(response.data)) {
          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));
        }
      }).catch(error => {
        console.error('获取性能类型选项失败：', error);
      });

      // 获取测试设备建议
      getTestPlanOptions({ type: 'testEquipment' }).then(response => {
        console.log('测试设备选项响应：', response);
        if (response.data && Array.isArray(response.data)) {
          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));
        }
      }).catch(error => {
        console.error('获取测试设备选项失败：', error);
      });
    },

    /** 方案编号搜索建议 */
    queryPlanCodeSuggestions(queryString, cb) {
      let suggestions = this.planCodeSuggestions;
      if (queryString) {
        suggestions = this.planCodeSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 性能类型搜索建议 */
    queryPerformanceTypeSuggestions(queryString, cb) {
      let suggestions = this.performanceTypeSuggestions;
      if (queryString) {
        suggestions = this.performanceTypeSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 测试设备搜索建议 */
    queryTestEquipmentSuggestions(queryString, cb) {
      let suggestions = this.testEquipmentSuggestions;
      if (queryString) {
        suggestions = this.testEquipmentSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 性能类型焦点事件 */
    handlePerformanceTypeFocus() {
      console.log('性能类型焦点事件触发');
      // 重新加载性能类型建议
      getTestPlanOptions({ type: 'performanceType' }).then(response => {
        console.log('性能类型焦点事件响应：', response);
        if (response.data && Array.isArray(response.data)) {
          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));
        }
      }).catch(error => {
        console.error('获取性能类型选项失败：', error);
      });
    },

    /** 测试设备焦点事件 */
    handleTestEquipmentFocus() {
      console.log('测试设备焦点事件触发');
      // 重新加载测试设备建议
      getTestPlanOptions({ type: 'testEquipment' }).then(response => {
        console.log('测试设备焦点事件响应：', response);
        if (response.data && Array.isArray(response.data)) {
          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));
        }
      }).catch(error => {
        console.error('获取测试设备选项失败：', error);
      });
    },

    /** 方案编号焦点事件 */
    handlePlanCodeFocus() {
      console.log('方案编号焦点事件触发');
      // 重新加载方案编号建议
      getTestPlanOptions({ type: 'planCode' }).then(response => {
        console.log('方案编号焦点事件响应：', response);
        if (response.data && Array.isArray(response.data)) {
          this.planCodeSuggestions = response.data.map(item => ({ value: item }));
        }
      }).catch(error => {
        console.error('获取方案编号选项失败：', error);
      });
    },

    /** 解析附件数据 */
    parseAttachments(attachments) {
      console.log('解析附件数据：', attachments, '类型：', typeof attachments);
      if (!attachments) {
        return [];
      }

      try {
        // 如果已经是数组，直接返回
        if (Array.isArray(attachments)) {
          return attachments.map((item, index) => ({
            name: item.name || `附件${index + 1}`,
            url: item.url || item,
            uid: item.uid || Date.now() + index,
            status: 'success'
          }));
        }

        // 如果是字符串，尝试解析
        if (typeof attachments === 'string') {
          const trimmed = attachments.trim();
          if (!trimmed) {
            return [];
          }

          // 尝试解析JSON格式
          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
            try {
              const parsed = JSON.parse(trimmed);
              if (Array.isArray(parsed)) {
                return parsed.map((item, index) => ({
                  name: item.name || `附件${index + 1}`,
                  url: item.url || item,
                  uid: item.uid || Date.now() + index,
                  status: 'success'
                }));
              }
            } catch (jsonError) {
              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);
            }
          }

          // 按逗号分割处理
          const urls = trimmed.split(',').filter(url => url.trim());
          console.log('分割后的URL列表：', urls);
          return urls.map((url, index) => {
            const cleanUrl = url.trim();
            const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;
            return {
              name: fileName,
              url: cleanUrl,
              uid: Date.now() + index,
              status: 'success'
            };
          });
        }
      } catch (error) {
        console.error('解析附件数据时发生错误：', error);
      }

      return [];
    },

    /** 方案编号选择事件 */
    handlePlanCodeSelect(item) {
      this.queryParams.planCode = item.value;
      this.handleQuery();
    },

    /** 查询测试方案列表 */
    getList() {
      this.loading = true;
      listTestPlan(this.queryParams).then(response => {
        this.testPlanList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 表单重置 */
    /** 表单重置 */
    reset() {
      this.form = {
        testPlanId: null,
        planCode: null,
        performanceType: null,
        performanceName: null,
        testEquipment: null,
        testParameter: null,
        attachments: null,
        remark: null
      };
      this.fileList = [];
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 多选框选中数据 */
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.testPlanId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 行点击选择 */
    handleRowClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row);
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加测试方案";
    },

    /** 修改按钮操作 */
    /** 修改按钮操作 */
    handleEdit(row) {
      this.reset();
      const testPlanId = row.testPlanId || this.ids[0];
      getTestPlan(testPlanId).then(response => {
        console.log('编辑获取的数据：', response.data);
        this.form = response.data;
        // 处理附件数据
        this.fileList = this.parseAttachments(response.data.attachments);
        console.log('编辑时解析的文件列表：', this.fileList);
        this.open = true;
        this.title = "修改测试方案";
      });
    },

    /** 复制按钮操作 */
    handleCopy(row) {
      this.reset();
      getTestPlan(row.testPlanId).then(response => {
        this.form = { ...response.data };
        this.form.testPlanId = null;
        this.form.planCode = this.form.planCode + "_副本";
        // 处理附件数据
        this.fileList = this.parseAttachments(response.data.attachments);
        this.open = true;
        this.title = "复制测试方案";
      });
    },

    /** 提交按钮 */
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 将文件列表转换为逗号分隔的URL字符串
          this.form.attachments = this.fileList.length > 0
            ? this.fileList.map(file => file.url).join(',')
            : null;

          // 设置创建人和更新人
          if (this.form.testPlanId != null) {
            // 更新操作，设置更新人
            this.form.updateBy = this.$store.state.user.name;
          } else {
            // 新增操作，设置创建人
            this.form.createBy = this.$store.state.user.name;
          }

          if (this.form.testPlanId != null) {
            updateTestPlan(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTestPlan(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      if (row && row.testPlanId) {
        // 单个删除
        const testPlanIds = row.testPlanId;
        this.$modal.confirm('是否确认删除测试方案"' + row.planCode + '"？').then(function() {
          return delTestPlan(testPlanIds);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      } else {
        // 批量删除
        const testPlanIds = this.ids;
        this.$modal.confirm('是否确认删除选中的' + testPlanIds.length + '条测试方案数据？').then(function() {
          return delTestPlan(testPlanIds);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('material/testPlan/export', {
        ...this.queryParams
      }, `test_plan_${new Date().getTime()}.xlsx`);
    },

    /** 附件上传成功 */
    handleUploadSuccess(response, file, fileList) {
      console.log('上传成功回调：', { response, file, fileList });
      if (response.code === 200) {
        // 确保fileList是数组
        if (Array.isArray(fileList)) {
          this.fileList = fileList.map(item => ({
            name: item.name,
            url: item.response ? item.response.url : item.url,
            size: this.formatFileSize(item.size || item.raw?.size),
            uid: item.uid,
            status: 'success'
          }));
        } else {
          console.error('fileList不是数组：', fileList);
          this.fileList = [];
        }
        this.$modal.msgSuccess("上传成功");
      } else {
        this.$modal.msgError(response.msg || "上传失败");
      }
    },

    /** 附件移除 */
    handleFileRemove(file, fileList) {
      console.log('附件移除回调：', { file, fileList });
      // 确保fileList是数组，并且正确处理空数组的情况
      if (Array.isArray(fileList)) {
        this.fileList = fileList.map(item => ({
          name: item.name,
          url: item.response ? item.response.url : item.url,
          size: this.formatFileSize(item.size || item.raw?.size),
          uid: item.uid,
          status: item.status || 'success'
        }));
      } else {
        console.error('fileList不是数组：', fileList);
        this.fileList = [];
      }
      this.$modal.msgSuccess("附件删除成功");
    },

    /** 附件上传前检查 */
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
      }
      return isLt10M;
    },

    /** 查看附件 */
    handleViewAttachments(attachments) {
      console.log('查看附件被调用，附件数据：', attachments, '类型：', typeof attachments);

      this.attachmentList = [];

      if (!attachments) {
        console.log('附件数据为空');
        this.attachmentDialogVisible = true;
        return;
      }

      try {
        if (typeof attachments === 'string') {
          const trimmed = attachments.trim();
          if (!trimmed) {
            console.log('附件字符串为空');
            this.attachmentDialogVisible = true;
            return;
          }

          // 尝试解析JSON格式
          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
            try {
              const parsed = JSON.parse(trimmed);
              if (Array.isArray(parsed)) {
                this.attachmentList = parsed.map((item, index) => ({
                  name: item.name || `附件${index + 1}`,
                  url: item.url || item,
                  size: item.size || '未知大小'
                }));
              }
            } catch (jsonError) {
              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);
              // 按逗号分割处理
              this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {
                const cleanUrl = url.trim();
                const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;
                return {
                  name: fileName,
                  url: cleanUrl,
                  size: '未知大小'
                };
              });
            }
          } else {
            // 按逗号分割处理
            this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {
              const cleanUrl = url.trim();
              const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;
              return {
                name: fileName,
                url: cleanUrl,
                size: '未知大小'
              };
            });
          }
        } else if (Array.isArray(attachments)) {
          this.attachmentList = attachments.map((item, index) => ({
            name: item.name || `附件${index + 1}`,
            url: item.url || item,
            size: item.size || '未知大小'
          }));
        }
      } catch (error) {
        console.error('解析附件数据时发生错误：', error);
        this.attachmentList = [];
      }

      console.log('解析后的附件列表：', this.attachmentList);
      this.attachmentDialogVisible = true;
    },

    /** 下载附件 */
    downloadAttachment(url, name) {
      const link = document.createElement('a');
      link.href = url;
      link.download = name;
      link.click();
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size || size === 0) return '0 B';
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB';
      }
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: center;
}

.el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
}
</style>
