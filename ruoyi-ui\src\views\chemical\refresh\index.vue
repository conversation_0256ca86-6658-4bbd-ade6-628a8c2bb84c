<template>
  <div class="app-container">
    <!-- 刷新状态卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="status-card">
          <div slot="header" class="clearfix">
            <span>运行状态</span>
          </div>
          <div class="status-content">
            <el-tag :type="refreshStatus.hasRunningTask ? 'success' : 'info'" size="medium">
              {{ refreshStatus.hasRunningTask ? '运行中' : '空闲' }}
            </el-tag>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div slot="header" class="clearfix">
            <span>已完成任务</span>
          </div>
          <div class="status-content">
            <span class="status-number">{{ refreshStatus.completedCount || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div slot="header" class="clearfix">
            <span>失败任务</span>
          </div>
          <div class="status-content">
            <span class="status-number error">{{ refreshStatus.failedCount || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div slot="header" class="clearfix">
            <span>总任务数</span>
          </div>
          <div class="status-content">
            <span class="status-number">{{ refreshStatus.totalCount || 0 }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh-right"
          size="mini"
          @click="handleNewRefresh"
          :disabled="refreshStatus.hasRunningTask"
          v-hasPermi="['chemical:refresh:execute']"
        >新建刷新任务</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-close"
          size="mini"
          @click="handleStopRefresh"
          :disabled="!refreshStatus.hasRunningTask"
          v-hasPermi="['chemical:refresh:stop']"
        >停止运行任务</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['chemical:refresh:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleCleanHistory"
          v-hasPermi="['chemical:refresh:clean']"
        >清理历史</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 任务列表 -->
    <el-table v-loading="loading" :data="refreshTaskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务ID" prop="refreshId" width="80" />
      <el-table-column label="任务名称" prop="taskName" width="200" show-overflow-tooltip />
      <el-table-column label="开始时间" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="耗时" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.durationMs">{{ formatDuration(scope.row.durationMs) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="处理组数" prop="totalGroups" width="100" />
      <el-table-column label="修改记录" prop="modifiedRecords" width="100" />
      <el-table-column label="任务状态" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.taskStatus === 'RUNNING'" type="primary" size="mini">运行中</el-tag>
          <el-tag v-else-if="scope.row.taskStatus === 'COMPLETED'" type="success" size="mini">已完成</el-tag>
          <el-tag v-else-if="scope.row.taskStatus === 'FAILED'" type="danger" size="mini">失败</el-tag>
          <el-tag v-else-if="scope.row.taskStatus === 'STOPPED'" type="warning" size="mini">已停止</el-tag>
          <el-tag v-else type="info" size="mini">{{ scope.row.taskStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据范围" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.startDate && scope.row.endDate">
            {{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }} ~ {{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="层号" prop="layerNumbers" width="120" show-overflow-tooltip />
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['chemical:refresh:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['chemical:refresh:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新建刷新任务对话框 -->
    <el-dialog title="新建数据刷新任务" :visible.sync="refreshDialogVisible" width="50%" append-to-body>
      <el-form :model="refreshForm" :rules="refreshRules" ref="refreshForm" label-width="100px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="refreshForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            v-model="refreshForm.startDate"
            type="date"
            placeholder="选择开始日期"
            value-format="yyyy-MM-dd"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="refreshForm.endDate"
            type="date"
            placeholder="选择结束日期"
            value-format="yyyy-MM-dd"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="层号选择" prop="layerNumbers">
          <el-checkbox-group v-model="refreshForm.layerNumbers">
            <el-checkbox v-for="layer in layerOptions" :key="layer" :label="layer">{{ layer }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="导出路径">
          <el-input v-model="refreshForm.exportPath" placeholder="请输入导出路径" />
        </el-form-item>
        <el-form-item label="备份路径">
          <el-input v-model="refreshForm.backupPath" placeholder="请输入备份路径" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="executeRefresh" :loading="refreshLoading">
          {{ refreshLoading ? '执行中...' : '开始执行' }}
        </el-button>
        <el-button @click="cancelRefresh">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" :visible.sync="detailVisible" width="60%" append-to-body>
      <el-descriptions :column="2" border v-if="detailData">
        <el-descriptions-item label="任务ID">{{ detailData.refreshId }}</el-descriptions-item>
        <el-descriptions-item label="任务名称">{{ detailData.taskName }}</el-descriptions-item>
        <el-descriptions-item label="任务状态">
          <el-tag v-if="detailData.taskStatus === 'RUNNING'" type="primary" size="mini">运行中</el-tag>
          <el-tag v-else-if="detailData.taskStatus === 'COMPLETED'" type="success" size="mini">已完成</el-tag>
          <el-tag v-else-if="detailData.taskStatus === 'FAILED'" type="danger" size="mini">失败</el-tag>
          <el-tag v-else-if="detailData.taskStatus === 'STOPPED'" type="warning" size="mini">已停止</el-tag>
          <el-tag v-else type="info" size="mini">{{ detailData.taskStatus }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ parseTime(detailData.startTime) }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ parseTime(detailData.endTime) }}</el-descriptions-item>
        <el-descriptions-item label="耗时">{{ formatDuration(detailData.durationMs) }}</el-descriptions-item>
        <el-descriptions-item label="处理组数">{{ detailData.totalGroups }}</el-descriptions-item>
        <el-descriptions-item label="修改记录数">{{ detailData.modifiedRecords }}</el-descriptions-item>
        <el-descriptions-item label="数据开始时间">{{ parseTime(detailData.startDate) }}</el-descriptions-item>
        <el-descriptions-item label="数据结束时间">{{ parseTime(detailData.endDate) }}</el-descriptions-item>
        <el-descriptions-item label="层号">{{ detailData.layerNumbers }}</el-descriptions-item>
        <el-descriptions-item label="导出路径">{{ detailData.exportFilePath }}</el-descriptions-item>
        <el-descriptions-item label="备份路径">{{ detailData.exportBackupPath }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="错误信息" :span="2" v-if="detailData.errorMessage">
          <el-alert :title="detailData.errorMessage" type="error" :closable="false"></el-alert>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRefreshTask, getRefreshTask, delRefreshTask, executeRefresh,
  stopRefresh, getRefreshStatus, cleanHistory, exportRefreshTask
} from "@/api/chemical/refresh";

export default {
  name: "ChemicalRefresh",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 刷新任务表格数据
      refreshTaskList: [],
      // 刷新状态
      refreshStatus: {
        hasRunningTask: false,
        completedCount: 0,
        failedCount: 0,
        totalCount: 0
      },
      // 新建刷新对话框
      refreshDialogVisible: false,
      refreshLoading: false,
      refreshForm: {
        taskName: '',
        startDate: '',
        endDate: '',
        layerNumbers: [],
        exportPath: 'E:\\测试数据\\应审抛转数据\\G2',
        backupPath: ''
      },
      refreshRules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ],
        startDate: [
          { required: true, message: "开始时间不能为空", trigger: "change" }
        ],
        endDate: [
          { required: true, message: "结束时间不能为空", trigger: "change" }
        ],
        layerNumbers: [
          { required: true, message: "请至少选择一个层号", trigger: "change" }
        ]
      },
      // 详情对话框
      detailVisible: false,
      detailData: null,
      // 层号选项
      layerOptions: ['L1', 'L2', 'L3', 'L4', 'L5', 'L6', 'L7', 'L8'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        taskStatus: null
      }
    };
  },
  created() {
    this.getList();
    this.getStatus();
    // 定时刷新状态
    this.statusTimer = setInterval(() => {
      this.getStatus();
    }, 5000);
  },
  beforeDestroy() {
    if (this.statusTimer) {
      clearInterval(this.statusTimer);
    }
  },
  methods: {
    /** 查询刷新任务列表 */
    getList() {
      this.loading = true;
      listRefreshTask(this.queryParams).then(response => {
        this.refreshTaskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 获取刷新状态 */
    getStatus() {
      getRefreshStatus().then(response => {
        this.refreshStatus = response.data;
      });
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.refreshId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 新建刷新任务 */
    handleNewRefresh() {
      this.resetRefreshForm();
      this.refreshDialogVisible = true;
    },

    /** 重置刷新表单 */
    resetRefreshForm() {
      // 设置默认时间范围为最近7天
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 7);

      this.refreshForm = {
        taskName: '数据刷新任务_' + this.formatDateTime(new Date()),
        startDate: this.formatDate(startDate),
        endDate: this.formatDate(endDate),
        layerNumbers: ['L1', 'L2', 'L3', 'L4'],
        exportPath: 'E:\\测试数据\\应审抛转数据\\G2',
        backupPath: ''
      };
    },

    /** 执行刷新任务 */
    executeRefresh() {
      this.$refs["refreshForm"].validate(valid => {
        if (valid) {
          this.refreshLoading = true;

          const params = {
            startDate: this.refreshForm.startDate + ' 00:00:00',
            endDate: this.refreshForm.endDate + ' 23:59:59',
            layerNumbers: this.refreshForm.layerNumbers,
            exportPath: this.refreshForm.exportPath,
            backupPath: this.refreshForm.backupPath
          };

          executeRefresh(params).then(response => {
            this.refreshLoading = false;
            this.refreshDialogVisible = false;

            if (response.code === 200) {
              this.$modal.msgSuccess("数据刷新任务启动成功");
              this.getList();
              this.getStatus();
            } else {
              this.$modal.msgError(response.msg || "数据刷新失败");
            }
          }).catch(error => {
            this.refreshLoading = false;
            this.$modal.msgError("数据刷新失败: " + (error.message || "未知错误"));
          });
        }
      });
    },

    /** 取消刷新 */
    cancelRefresh() {
      this.refreshDialogVisible = false;
      this.resetRefreshForm();
    },

    /** 停止运行任务 */
    handleStopRefresh() {
      this.$modal.confirm('确认停止正在运行的刷新任务？').then(() => {
        return stopRefresh();
      }).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("任务停止成功");
          this.getList();
          this.getStatus();
        } else {
          this.$modal.msgError(response.msg || "停止任务失败");
        }
      }).catch(() => {});
    },

    /** 查看任务详情 */
    handleView(row) {
      getRefreshTask(row.refreshId).then(response => {
        this.detailData = response.data;
        this.detailVisible = true;
      });
    },

    /** 删除任务 */
    handleDelete(row) {
      const refreshIds = row.refreshId || this.ids;
      this.$modal.confirm('是否确认删除刷新任务编号为"' + refreshIds + '"的数据项？').then(() => {
        return delRefreshTask(refreshIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出 */
    handleExport() {
      this.download('chemical/refresh/export', {
        ...this.queryParams
      }, `refresh_task_${new Date().getTime()}.xlsx`);
    },

    /** 清理历史记录 */
    handleCleanHistory() {
      this.$prompt('请输入要保留的天数', '清理历史记录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入有效的天数'
      }).then(({ value }) => {
        return cleanHistory(value);
      }).then(response => {
        this.$modal.msgSuccess("清理完成，共清理 " + response.data + " 条记录");
        this.getList();
      }).catch(() => {});
    },

    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    /** 格式化日期时间 */
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');
      const second = String(date.getSeconds()).padStart(2, '0');
      return `${year}${month}${day}_${hour}${minute}${second}`;
    },

    /** 格式化耗时 */
    formatDuration(ms) {
      if (!ms) return '-';

      const seconds = Math.floor(ms / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);

      if (hours > 0) {
        return `${hours}小时${minutes % 60}分${seconds % 60}秒`;
      } else if (minutes > 0) {
        return `${minutes}分${seconds % 60}秒`;
      } else {
        return `${seconds}秒`;
      }
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.status-card {
  text-align: center;
}

.status-content {
  padding: 20px 0;
}

.status-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.status-number.error {
  color: #F56C6C;
}
</style>
