<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.ChemicalTaskMonitorMapper">
    
    <resultMap type="ChemicalTaskMonitor" id="ChemicalTaskMonitorResult">
        <result property="monitorId"    column="monitor_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskType"    column="task_type"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="totalRecords"    column="total_records"    />
        <result property="processedRecords"    column="processed_records"    />
        <result property="successRecords"    column="success_records"    />
        <result property="errorRecords"    column="error_records"    />
        <result property="modifiedRecords"    column="modified_records"    />
        <result property="progressPercent"    column="progress_percent"    />
        <result property="executionTime"    column="execution_time"    />
        <result property="executionDuration"    column="execution_duration"    />
        <result property="executionMessage"    column="execution_message"    />
        <result property="cpuUsage"    column="cpu_usage"    />
        <result property="memoryUsage"    column="memory_usage"    />
        <result property="diskUsage"    column="disk_usage"    />
        <result property="networkIn"    column="network_in"    />
        <result property="networkOut"    column="network_out"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="taskConfig"    column="task_config"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectChemicalTaskMonitorVo">
        select monitor_id, task_id, task_name, task_type, task_status, start_time, end_time,
               total_records, processed_records, success_records, error_records, modified_records,
               progress_percent, execution_time, execution_duration, execution_message,
               cpu_usage, memory_usage, disk_usage, network_in, network_out, error_message,
               task_config, create_by, create_time, update_by, update_time
        from chemical_task_monitor
    </sql>

    <select id="selectChemicalTaskMonitorList" parameterType="ChemicalTaskMonitor" resultMap="ChemicalTaskMonitorResult">
        <include refid="selectChemicalTaskMonitorVo"/>
        <where>  
            <if test="taskName != null and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskStatus != null and taskStatus != ''"> and task_status = #{taskStatus}</if>
            <if test="startTime != null"> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null"> and end_time &lt;= #{endTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectChemicalTaskMonitorById" parameterType="Long" resultMap="ChemicalTaskMonitorResult">
        <include refid="selectChemicalTaskMonitorVo"/>
        where monitor_id = #{id}
    </select>

    <select id="selectChemicalTaskMonitorByMonitorId" parameterType="Long" resultMap="ChemicalTaskMonitorResult">
        <include refid="selectChemicalTaskMonitorVo"/>
        where monitor_id = #{monitorId}
    </select>

    <select id="selectLatestTaskMonitor" parameterType="String" resultMap="ChemicalTaskMonitorResult">
        <include refid="selectChemicalTaskMonitorVo"/>
        where task_name = #{taskName}
        order by create_time desc
        limit 1
    </select>

    <select id="selectTaskMonitorByDateRange" resultMap="ChemicalTaskMonitorResult">
        <include refid="selectChemicalTaskMonitorVo"/>
        where create_time between #{startDate} and #{endDate}
        order by create_time desc
    </select>
        
    <insert id="insertChemicalTaskMonitor" parameterType="ChemicalTaskMonitor" useGeneratedKeys="true" keyProperty="monitorId">
        insert into chemical_task_monitor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="taskStatus != null and taskStatus != ''">task_status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="processedRecords != null">processed_records,</if>
            <if test="errorRecords != null">error_records,</if>
            <if test="cpuUsage != null">cpu_usage,</if>
            <if test="memoryUsage != null">memory_usage,</if>
            <if test="diskUsage != null">disk_usage,</if>
            <if test="networkIn != null">network_in,</if>
            <if test="networkOut != null">network_out,</if>
            <if test="errorMessage != null">error_message,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="taskStatus != null and taskStatus != ''">#{taskStatus},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="processedRecords != null">#{processedRecords},</if>
            <if test="errorRecords != null">#{errorRecords},</if>
            <if test="cpuUsage != null">#{cpuUsage},</if>
            <if test="memoryUsage != null">#{memoryUsage},</if>
            <if test="diskUsage != null">#{diskUsage},</if>
            <if test="networkIn != null">#{networkIn},</if>
            <if test="networkOut != null">#{networkOut},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            getdate()
        </trim>
    </insert>

    <update id="updateChemicalTaskMonitor" parameterType="ChemicalTaskMonitor">
        update chemical_task_monitor
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="taskStatus != null and taskStatus != ''">task_status = #{taskStatus},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="processedRecords != null">processed_records = #{processedRecords},</if>
            <if test="errorRecords != null">error_records = #{errorRecords},</if>
            <if test="cpuUsage != null">cpu_usage = #{cpuUsage},</if>
            <if test="memoryUsage != null">memory_usage = #{memoryUsage},</if>
            <if test="diskUsage != null">disk_usage = #{diskUsage},</if>
            <if test="networkIn != null">network_in = #{networkIn},</if>
            <if test="networkOut != null">network_out = #{networkOut},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            update_time = getdate()
        </trim>
        where monitor_id = #{monitorId}
    </update>

    <delete id="deleteChemicalTaskMonitorById" parameterType="Long">
        delete from chemical_task_monitor where monitor_id = #{id}
    </delete>

    <delete id="deleteChemicalTaskMonitorByIds" parameterType="String">
        delete from chemical_task_monitor where monitor_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteChemicalTaskMonitorByMonitorId" parameterType="Long">
        delete from chemical_task_monitor where monitor_id = #{monitorId}
    </delete>

    <delete id="deleteChemicalTaskMonitorByMonitorIds" parameterType="String">
        delete from chemical_task_monitor where monitor_id in
        <foreach item="monitorId" collection="array" open="(" separator="," close=")">
            #{monitorId}
        </foreach>
    </delete>

    <delete id="cleanOldTaskMonitors" parameterType="int">
        delete from chemical_task_monitor
        where create_time &lt; dateadd(day, -#{days}, getdate())
    </delete>

    <!-- 统计查询方法 -->
    <select id="selectTaskExecutionStats" resultType="map">
        select
            count(*) as totalTasks,
            sum(case when task_status = 'SUCCESS' then 1 else 0 end) as successTasks,
            sum(case when task_status = 'FAILED' then 1 else 0 end) as failedTasks,
            sum(case when task_status = 'RUNNING' then 1 else 0 end) as runningTasks,
            avg(case when execution_duration is not null then execution_duration else 0 end) as averageDuration
        from chemical_task_monitor
        where create_time &gt;= dateadd(hour, -24, getdate())
    </select>

    <select id="selectDataProcessingStats" resultType="map">
        select
            sum(processed_records) as totalRecords,
            sum(case when task_status = 'SUCCESS' then processed_records else 0 end) as processedRecords,
            sum(case when task_status = 'SUCCESS' then modified_records else 0 end) as modifiedRecords,
            sum(error_records) as errorRecords,
            case when sum(processed_records) > 0 then
                cast(sum(case when task_status = 'SUCCESS' then processed_records else 0 end) as float) / sum(processed_records) * 100
                else 0 end as processingRate
        from chemical_task_monitor
        where create_time &gt;= dateadd(hour, -24, getdate())
    </select>

    <select id="selectErrorLogStats" resultType="map">
        select
            task_name as taskName,
            error_message as errorMessage,
            create_time as createTime,
            error_records as errorCount
        from chemical_task_monitor
        where task_status = 'FAILED' and create_time &gt;= dateadd(hour, -24, getdate())
        order by create_time desc
    </select>

    <select id="selectPerformanceMonitorData" resultType="map">
        select
            task_name as taskName,
            cpu_usage as cpuUsage,
            memory_usage as memoryUsage,
            disk_usage as diskUsage,
            network_in as networkIn,
            network_out as networkOut,
            execution_duration as executionDuration,
            create_time as createTime
        from chemical_task_monitor
        where create_time &gt;= dateadd(hour, -24, getdate())
        order by create_time desc
    </select>

    <!-- 新增的查询方法 -->
    <select id="selectDataTrends" parameterType="int" resultType="map">
        select
            convert(varchar(10), create_time, 120) as date,
            count(*) as taskCount,
            sum(case when task_status = 'SUCCESS' then 1 else 0 end) as successCount,
            sum(case when task_status = 'FAILED' then 1 else 0 end) as failedCount,
            avg(case when execution_duration is not null then execution_duration else 0 end) as avgDuration
        from chemical_task_monitor
        where create_time &gt;= dateadd(day, -#{days}, getdate())
        group by convert(varchar(10), create_time, 120)
        order by date desc
    </select>

    <select id="selectErrorStatistics" parameterType="int" resultType="map">
        select
            count(*) as totalErrors,
            count(distinct task_name) as errorTaskTypes,
            max(create_time) as lastErrorTime
        from chemical_task_monitor
        where task_status = 'FAILED' and create_time &gt;= dateadd(hour, -#{hours}, getdate())
    </select>

    <select id="selectSystemAlerts" resultType="map">
        select
            'TASK_FAILED' as alertType,
            task_name as alertSource,
            error_message as alertMessage,
            create_time as alertTime,
            'HIGH' as alertLevel
        from chemical_task_monitor
        where task_status = 'FAILED' and create_time &gt;= dateadd(hour, -24, getdate())
        union all
        select
            'LONG_RUNNING' as alertType,
            task_name as alertSource,
            'Task running for more than 2 hours' as alertMessage,
            start_time as alertTime,
            'MEDIUM' as alertLevel
        from chemical_task_monitor
        where task_status = 'RUNNING'
          and start_time &lt; dateadd(hour, -2, getdate())
        order by alertTime desc
    </select>

</mapper>
