package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ProcessParamItem;

/**
 * 工艺参数明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ProcessParamItemMapper 
{
    /**
     * 查询工艺参数明细
     * 
     * @param itemId 工艺参数明细主键
     * @return 工艺参数明细
     */
    public ProcessParamItem selectProcessParamItemByItemId(Long itemId);

    /**
     * 查询工艺参数明细列表
     * 
     * @param processParamItem 工艺参数明细
     * @return 工艺参数明细集合
     */
    public List<ProcessParamItem> selectProcessParamItemList(ProcessParamItem processParamItem);

    /**
     * 根据参数组ID查询工艺参数明细列表
     * 
     * @param groupId 参数组ID
     * @return 工艺参数明细集合
     */
    public List<ProcessParamItem> selectProcessParamItemByGroupId(Long groupId);

    /**
     * 新增工艺参数明细
     * 
     * @param processParamItem 工艺参数明细
     * @return 结果
     */
    public int insertProcessParamItem(ProcessParamItem processParamItem);

    /**
     * 修改工艺参数明细
     * 
     * @param processParamItem 工艺参数明细
     * @return 结果
     */
    public int updateProcessParamItem(ProcessParamItem processParamItem);

    /**
     * 删除工艺参数明细
     * 
     * @param itemId 工艺参数明细主键
     * @return 结果
     */
    public int deleteProcessParamItemByItemId(Long itemId);

    /**
    /**
     * 批量删除工艺参数明细
     *
     * @param itemIds 需要删除的工艺参数明细主键集合
     * @return 结果
     */
    public int deleteProcessParamItemByItemIds(Long[] itemIds);

    /**
     * 获取参数名称选项
     *
     * @return 参数名称列表
     */
    public List<String> selectParamNameOptions();

    /**
     * 获取参数单位选项
     *
     * @return 参数单位列表
     */
    public List<String> selectUnitOptions();
}