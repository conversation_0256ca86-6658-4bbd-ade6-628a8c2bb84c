package com.ruoyi.audit.service;

import java.util.Map;

/**
 * 化学审计任务Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IChemicalTaskService 
{
    /**
     * 启动数据读取任务
     * 
     * @return 结果
     */
    public Map<String, Object> startDataReadingTask();

    /**
     * 暂停数据读取任务
     * 
     * @return 结果
     */
    public Map<String, Object> pauseDataReadingTask();

    /**
     * 停止数据读取任务
     * 
     * @return 结果
     */
    public Map<String, Object> stopDataReadingTask();

    /**
     * 重启数据读取任务
     * 
     * @return 结果
     */
    public Map<String, Object> restartDataReadingTask();

    /**
     * 获取任务状态
     * 
     * @return 任务状态信息
     */
    public Map<String, Object> getTaskStatus();

    /**
     * 获取任务监控信息
     * 
     * @return 监控信息
     */
    public Map<String, Object> getTaskMonitorInfo();

    /**
     * 启动数据处理任务
     * 
     * @return 结果
     */
    public Map<String, Object> startDataProcessingTask();

    /**
     * 启动日志处理任务
     * 
     * @return 结果
     */
    public Map<String, Object> startLogProcessingTask();

    /**
     * 获取实时日志信息
     * 
     * @param lastLogId 最后日志ID
     * @return 日志信息
     */
    public Map<String, Object> getRealtimeLogs(Long lastLogId);

    /**
     * 清空日志信息
     * 
     * @return 结果
     */
    public Map<String, Object> clearLogs();

    /**
     * 获取系统运行状态
     * 
     * @return 系统状态
     */
    public Map<String, Object> getSystemStatus();

    /**
     * 初始化Pulsar客户端
     * 
     * @return 结果
     */
    public boolean initializePulsarClient();

    /**
     * 关闭Pulsar客户端
     * 
     * @return 结果
     */
    public boolean closePulsarClient();

    /**
     * 检查Pulsar连接状态
     * 
     * @return 连接状态
     */
    public boolean checkPulsarConnection();

    /**
     * 获取Pulsar消费者状态
     *
     * @return 消费者状态
     */
    public Map<String, Object> getPulsarConsumerStatus();

    /**
     * 启动统一数据处理任务
     * 整合原来分离的两个软件功能
     *
     * @param params 任务参数
     * @return 启动结果
     */
    public Map<String, Object> startUnifiedDataProcessingTask(Map<String, Object> params);

    /**
     * 获取统一数据处理统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getUnifiedProcessingStatistics();
}
