package com.ruoyi.audit.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.audit.domain.ChemicalTaskMonitor;

/**
 * 任务监控Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ChemicalTaskMonitorMapper 
{
    /**
     * 查询任务监控
     *
     * @param id 任务监控主键
     * @return 任务监控
     */
    public ChemicalTaskMonitor selectChemicalTaskMonitorById(Long id);

    /**
     * 查询任务监控
     *
     * @param taskId 任务监控主键
     * @return 任务监控
     */
    public ChemicalTaskMonitor selectChemicalTaskMonitorByTaskId(Long taskId);

    /**
     * 查询任务监控列表
     * 
     * @param chemicalTaskMonitor 任务监控
     * @return 任务监控集合
     */
    public List<ChemicalTaskMonitor> selectChemicalTaskMonitorList(ChemicalTaskMonitor chemicalTaskMonitor);

    /**
     * 新增任务监控
     * 
     * @param chemicalTaskMonitor 任务监控
     * @return 结果
     */
    public int insertChemicalTaskMonitor(ChemicalTaskMonitor chemicalTaskMonitor);

    /**
     * 修改任务监控
     * 
     * @param chemicalTaskMonitor 任务监控
     * @return 结果
     */
    public int updateChemicalTaskMonitor(ChemicalTaskMonitor chemicalTaskMonitor);

    /**
     * 删除任务监控
     *
     * @param id 任务监控主键
     * @return 结果
     */
    public int deleteChemicalTaskMonitorById(Long id);

    /**
     * 删除任务监控
     *
     * @param taskId 任务监控主键
     * @return 结果
     */
    public int deleteChemicalTaskMonitorByTaskId(Long taskId);

    /**
     * 批量删除任务监控
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChemicalTaskMonitorByIds(Long[] ids);

    /**
     * 批量删除任务监控
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChemicalTaskMonitorByTaskIds(Long[] taskIds);

    /**
     * 根据任务名称查询任务
     * 
     * @param taskName 任务名称
     * @return 任务监控
     */
    public ChemicalTaskMonitor selectTaskByName(String taskName);

    /**
     * 查询运行中的任务
     * 
     * @return 任务监控集合
     */
    public List<ChemicalTaskMonitor> selectRunningTasks();

    /**
     * 查询最新的任务记录
     * 
     * @param taskType 任务类型
     * @return 任务监控
     */
    public ChemicalTaskMonitor selectLatestTaskByType(String taskType);

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param taskStatus 任务状态
     * @return 结果
     */
    public int updateTaskStatus(Long taskId, String taskStatus);

    /**
     * 更新任务进度
     * 
     * @param chemicalTaskMonitor 任务监控
     * @return 结果
     */
    public int updateTaskProgress(ChemicalTaskMonitor chemicalTaskMonitor);

    /**
     * 清理历史任务记录
     * 
     * @param days 保留天数
     * @return 结果
     */
    public int cleanHistoryTasks(int days);

    /**
     * 统计任务数量
     * 
     * @param taskStatus 任务状态
     * @return 任务数量
     */
    public int countTasksByStatus(String taskStatus);

    /**
     * 统计今日任务数量
     *
     * @return 今日任务数量
     */
    public int countTodayTasks();

    /**
     * 查询任务执行统计
     *
     * @return 任务执行统计
     */
    public Map<String, Object> selectTaskExecutionStats();

    /**
     * 查询数据处理统计
     *
     * @return 数据处理统计
     */
    public Map<String, Object> selectDataProcessingStats();

    /**
     * 查询错误日志统计
     *
     * @return 错误日志统计
     */
    public List<Map<String, Object>> selectErrorLogStats();

    /**
     * 查询性能监控数据
     *
     * @return 性能监控数据
     */
    public List<Map<String, Object>> selectPerformanceMonitorData();
}
