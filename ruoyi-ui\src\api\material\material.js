import request from '@/utils/request'

// 查询材料信息列表
export function listMaterial(query) {
  return request({
    url: '/material/material/list',
    method: 'get',
    params: query
  })
}

// 查询材料信息详细
export function getMaterial(materialId) {
  return request({
    url: '/material/material/' + materialId,
    method: 'get'
  })
}

// 新增材料信息
export function addMaterial(data) {
  // 处理附件数据
  if (data.attachmentList && Array.isArray(data.attachmentList)) {
    data.attachments = JSON.stringify(data.attachmentList);
    delete data.attachmentList;
  }
  return request({
    url: '/material/material',
    method: 'post',
    data: data
  })
}

// 修改材料信息
export function updateMaterial(data) {
  // 处理附件数据
  if (data.attachmentList && Array.isArray(data.attachmentList)) {
    data.attachments = JSON.stringify(data.attachmentList);
    delete data.attachmentList;
  }
  return request({
    url: '/material/material',
    method: 'put',
    data: data
  })
}

// 删除材料信息
export function delMaterial(materialId) {
  return request({
    url: '/material/material/' + materialId,
    method: 'delete'
  })
}

// 导出材料信息
export function exportMaterial(query) {
  return request({
    url: '/material/material/export',
    method: 'post',
    data: query
  })
}

// 获取材料选项数据
export function getMaterialOptions(query) {
  return request({
    url: '/material/material/options',
    method: 'get',
    params: query
  })
}

// 导入材料数据
export function importMaterial(data) {
  return request({
    url: '/material/material/importData',
    method: 'post',
    data: data
  })
}

// 下载导入模板
export function downloadTemplate() {
  return request({
    url: '/material/material/importTemplate',
    method: 'post'
  })
}

// 上传附件
export function uploadAttachment(data) {
  return request({
    url: '/material/material/upload',
    method: 'post',
    data: data
  })
}

// 下载附件
export function downloadAttachment(fileName) {
  return request({
    url: '/material/material/download/' + fileName,
    method: 'get',
    responseType: 'blob'
  })
}

