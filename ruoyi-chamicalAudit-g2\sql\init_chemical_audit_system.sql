-- 化学审计系统完整初始化脚本
-- 执行顺序：先执行此脚本，再执行菜单配置脚本

USE [ry-vue]
GO

-- =============================================
-- 1. 删除已存在的表（如果需要重新创建）
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[chemical_process_log]') AND type in (N'U'))
DROP TABLE [dbo].[chemical_process_log]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[chemical_system_config]') AND type in (N'U'))
DROP TABLE [dbo].[chemical_system_config]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[chemical_export_record]') AND type in (N'U'))
DROP TABLE [dbo].[chemical_export_record]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[chemical_refresh_task]') AND type in (N'U'))
DROP TABLE [dbo].[chemical_refresh_task]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[chemical_task_monitor]') AND type in (N'U'))
DROP TABLE [dbo].[chemical_task_monitor]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[rule_config]') AND type in (N'U'))
DROP TABLE [dbo].[rule_config]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[chemical_ys]') AND type in (N'U'))
DROP TABLE [dbo].[chemical_ys]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[chemical]') AND type in (N'U'))
DROP TABLE [dbo].[chemical]
GO

-- =============================================
-- 2. 创建表结构
-- =============================================

-- 2.1 化学数据主表
CREATE TABLE [dbo].[chemical](
    [id] [varchar](50) NOT NULL,
    [organization_id] [bigint] NULL,
    [attribute_id] [bigint] NULL,
    [examine_date] [datetime] NULL,
    [shift] [varchar](50) NULL,
    [staff] [varchar](100) NULL,
    [department_code] [varchar](50) NULL,
    [process_set_name] [varchar](100) NULL,
    [process_name] [varchar](100) NULL,
    [product_set_name] [varchar](100) NULL,
    [product_name] [varchar](100) NULL,
    [test_set_name] [varchar](100) NULL,
    [test_name] [varchar](100) NULL,
    [sample_size] [int] NULL,
    [layer_number] [varchar](50) NULL,
    [upper_limit] [varchar](50) NULL,
    [median_specification] [varchar](50) NULL,
    [down_limit] [varchar](50) NULL,
    [examine1] [varchar](50) NULL,
    [examine1_ys] [varchar](50) NULL,
    [examine1_zs] [varchar](50) NULL,
    [examine2] [varchar](50) NULL,
    [frequency] [varchar](50) NULL,
    [frequency_unit] [varchar](50) NULL,
    [slot_body_name] [varchar](100) NULL,
    [project_team_code] [varchar](50) NULL,
    [project_team_name] [varchar](100) NULL,
    [test_code] [varchar](50) NULL,
    [adjustment_upper_limit] [varchar](50) NULL,
    [adjustment_mid] [varchar](50) NULL,
    [adjustment_lower_limit] [varchar](50) NULL,
    [project_unit] [varchar](50) NULL,
    [insertion_time] [datetime] NULL,
    [is_exported] [bit] NULL DEFAULT (0),
    [not_process] [bit] NULL DEFAULT (0),
    [warning_upper_limit] [varchar](50) NULL,
    [warning_mid] [varchar](50) NULL,
    [warning_lower_limit] [varchar](50) NULL,
    [remark] [text] NULL,
    [status] [varchar](10) NULL DEFAULT ('0'),
    [created_by] [varchar](64) NULL,
    [last_updated_by] [varchar](64) NULL,
    [creation_date] [datetime] NULL DEFAULT (getdate()),
    [last_update_date] [datetime] NULL DEFAULT (getdate()),
    [create_time] [datetime] NULL DEFAULT (getdate()),
    [update_time] [datetime] NULL DEFAULT (getdate()),
    CONSTRAINT [PK_chemical] PRIMARY KEY CLUSTERED ([id] ASC)
)
GO

-- 2.2 化学应审数据表
CREATE TABLE [dbo].[chemical_ys](
    [id] [varchar](50) NOT NULL,
    [organization_id] [bigint] NULL,
    [attribute_id] [bigint] NULL,
    [examine_date] [datetime] NULL,
    [shift] [varchar](50) NULL,
    [staff] [varchar](100) NULL,
    [process_name] [varchar](100) NULL,
    [product_name] [varchar](100) NULL,
    [test_name] [varchar](100) NULL,
    [layer_number] [varchar](50) NULL,
    [upper_limit] [varchar](50) NULL,
    [median_specification] [varchar](50) NULL,
    [down_limit] [varchar](50) NULL,
    [examine1] [varchar](50) NULL,
    [examine1_ys] [varchar](50) NULL,
    [examine1_zs] [varchar](50) NULL,
    [examine2] [varchar](50) NULL,
    [is_modified] [bit] NULL DEFAULT (0),
    [original_examine1] [varchar](50) NULL,
    [remark] [text] NULL,
    [create_time] [datetime] NULL DEFAULT (getdate()),
    [update_time] [datetime] NULL DEFAULT (getdate()),
    CONSTRAINT [PK_chemical_ys] PRIMARY KEY CLUSTERED ([id] ASC)
)
GO

-- 2.3 数据刷新规则配置表
CREATE TABLE [dbo].[rule_config](
    [id] [bigint] IDENTITY(1,1) NOT NULL,
    [product_name] [varchar](100) NOT NULL,
    [process_name] [varchar](100) NOT NULL,
    [test_name] [varchar](100) NOT NULL,
    [enable_control_limit_adjustment] [bit] NULL DEFAULT (1),
    [enable_moving_range_adjustment] [bit] NULL DEFAULT (1),
    [enable_nine_point_same_side_check] [bit] NULL DEFAULT (1),
    [enable_six_point_trend_check] [bit] NULL DEFAULT (1),
    [enable_cpk_adjustment] [bit] NULL DEFAULT (1),
    [cpk_target] [decimal](10, 3) NULL DEFAULT (1.33),
    [is_refresh_mode] [bit] NULL DEFAULT (0),
    [rule_description] [text] NULL,
    [status] [varchar](1) NULL DEFAULT ('1'),
    [created_by] [varchar](64) NULL,
    [updated_by] [varchar](64) NULL,
    [created_time] [datetime] NULL DEFAULT (getdate()),
    [updated_time] [datetime] NULL DEFAULT (getdate()),
    CONSTRAINT [PK_rule_config] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [UK_rule_config] UNIQUE ([product_name], [process_name], [test_name])
)
GO

-- 2.4 化学任务监控表
CREATE TABLE [dbo].[chemical_task_monitor](
    [monitor_id] [bigint] IDENTITY(1,1) NOT NULL,
    [task_id] [bigint] NULL,
    [task_name] [varchar](100) NULL,
    [task_type] [varchar](50) NULL,
    [task_status] [varchar](20) NULL,
    [start_time] [datetime] NULL,
    [end_time] [datetime] NULL,
    [total_records] [int] NULL DEFAULT (0),
    [processed_records] [int] NULL DEFAULT (0),
    [success_records] [int] NULL DEFAULT (0),
    [error_records] [int] NULL DEFAULT (0),
    [modified_records] [int] NULL DEFAULT (0),
    [progress_percent] [decimal](5, 2) NULL DEFAULT (0),
    [execution_time] [datetime] NULL,
    [execution_duration] [bigint] NULL,
    [execution_message] [text] NULL,
    [cpu_usage] [decimal](5, 2) NULL,
    [memory_usage] [decimal](5, 2) NULL,
    [disk_usage] [decimal](5, 2) NULL,
    [network_in] [decimal](10, 2) NULL,
    [network_out] [decimal](10, 2) NULL,
    [error_message] [text] NULL,
    [task_config] [text] NULL,
    [create_by] [varchar](64) NULL,
    [create_time] [datetime] NULL DEFAULT (getdate()),
    [update_by] [varchar](64) NULL,
    [update_time] [datetime] NULL DEFAULT (getdate()),
    CONSTRAINT [PK_chemical_task_monitor] PRIMARY KEY CLUSTERED ([monitor_id] ASC)
)
GO

-- 2.5 化学数据刷新任务表
CREATE TABLE [dbo].[chemical_refresh_task](
    [task_id] [bigint] IDENTITY(1,1) NOT NULL,
    [task_name] [varchar](100) NOT NULL,
    [task_type] [varchar](50) NULL,
    [start_date] [datetime] NULL,
    [end_date] [datetime] NULL,
    [layer_numbers] [text] NULL,
    [task_status] [varchar](20) NULL DEFAULT ('PENDING'),
    [total_records] [int] NULL DEFAULT (0),
    [processed_records] [int] NULL DEFAULT (0),
    [modified_records] [int] NULL DEFAULT (0),
    [error_records] [int] NULL DEFAULT (0),
    [start_time] [datetime] NULL,
    [end_time] [datetime] NULL,
    [duration_ms] [bigint] NULL,
    [error_message] [text] NULL,
    [created_by] [varchar](64) NULL,
    [create_time] [datetime] NULL DEFAULT (getdate()),
    [update_time] [datetime] NULL DEFAULT (getdate()),
    CONSTRAINT [PK_chemical_refresh_task] PRIMARY KEY CLUSTERED ([task_id] ASC)
)
GO

-- 2.6 化学数据导出记录表
CREATE TABLE [dbo].[chemical_export_record](
    [export_id] [bigint] IDENTITY(1,1) NOT NULL,
    [export_name] [varchar](200) NULL,
    [export_type] [varchar](20) NULL,
    [start_date] [datetime] NULL,
    [end_date] [datetime] NULL,
    [layer_numbers] [text] NULL,
    [file_path] [varchar](500) NULL,
    [file_name] [varchar](200) NULL,
    [file_size] [bigint] NULL,
    [record_count] [int] NULL,
    [export_status] [varchar](20) NULL DEFAULT ('PENDING'),
    [export_progress] [int] NULL DEFAULT (0),
    [error_message] [text] NULL,
    [created_by] [varchar](64) NULL,
    [create_time] [datetime] NULL DEFAULT (getdate()),
    [update_time] [datetime] NULL DEFAULT (getdate()),
    CONSTRAINT [PK_chemical_export_record] PRIMARY KEY CLUSTERED ([export_id] ASC)
)
GO

-- 2.7 系统配置表
CREATE TABLE [dbo].[chemical_system_config](
    [config_id] [bigint] IDENTITY(1,1) NOT NULL,
    [config_key] [varchar](100) NOT NULL,
    [config_value] [text] NULL,
    [config_description] [varchar](500) NULL,
    [config_type] [varchar](50) NULL DEFAULT ('STRING'),
    [is_system] [bit] NULL DEFAULT (0),
    [status] [varchar](1) NULL DEFAULT ('1'),
    [created_by] [varchar](64) NULL,
    [create_time] [datetime] NULL DEFAULT (getdate()),
    [updated_by] [varchar](64) NULL,
    [update_time] [datetime] NULL DEFAULT (getdate()),
    CONSTRAINT [PK_chemical_system_config] PRIMARY KEY CLUSTERED ([config_id] ASC),
    CONSTRAINT [UK_chemical_system_config] UNIQUE ([config_key])
)
GO

-- 2.8 数据处理日志表
CREATE TABLE [dbo].[chemical_process_log](
    [log_id] [bigint] IDENTITY(1,1) NOT NULL,
    [task_id] [bigint] NULL,
    [log_level] [varchar](10) NULL,
    [log_message] [text] NULL,
    [log_detail] [text] NULL,
    [chemical_id] [varchar](50) NULL,
    [process_type] [varchar](50) NULL,
    [create_time] [datetime] NULL DEFAULT (getdate()),
    CONSTRAINT [PK_chemical_process_log] PRIMARY KEY CLUSTERED ([log_id] ASC)
)
GO

-- =============================================
-- 3. 创建索引
-- =============================================
CREATE NONCLUSTERED INDEX [IX_chemical_examine_date] ON [dbo].[chemical] ([examine_date] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_chemical_product_process_test] ON [dbo].[chemical] ([product_name] ASC, [process_name] ASC, [test_name] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_chemical_layer_number] ON [dbo].[chemical] ([layer_number] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_chemical_ys_examine_date] ON [dbo].[chemical_ys] ([examine_date] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_chemical_ys_product_process_test] ON [dbo].[chemical_ys] ([product_name] ASC, [process_name] ASC, [test_name] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_task_monitor_task_name] ON [dbo].[chemical_task_monitor] ([task_name] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_task_monitor_create_time] ON [dbo].[chemical_task_monitor] ([create_time] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_refresh_task_status] ON [dbo].[chemical_refresh_task] ([task_status] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_export_record_create_time] ON [dbo].[chemical_export_record] ([create_time] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_process_log_task_id] ON [dbo].[chemical_process_log] ([task_id] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_process_log_create_time] ON [dbo].[chemical_process_log] ([create_time] ASC)
GO

-- =============================================
-- 4. 插入初始数据
-- =============================================

-- 4.1 插入默认系统配置
INSERT INTO [dbo].[chemical_system_config] ([config_key], [config_value], [config_description], [config_type], [is_system], [created_by]) VALUES
('chemical.database.cloud.url', 'jdbc:sqlserver://************;DatabaseName=SPC-G2;encrypt=true;trustServerCertificate=true;sslProtocol=TLSv1', '云端数据库连接URL', 'STRING', 1, 'system'),
('chemical.database.cloud.username', 'hhh', '云端数据库用户名', 'STRING', 1, 'system'),
('chemical.database.cloud.password', 'root1234', '云端数据库密码', 'PASSWORD', 1, 'system'),
('chemical.export.default.path', 'D:/chemical_exports', '默认导出路径', 'STRING', 0, 'system'),
('chemical.export.max.records', '100000', '最大导出记录数', 'INTEGER', 0, 'system'),
('chemical.task.max.concurrent', '5', '最大并发任务数', 'INTEGER', 0, 'system'),
('chemical.rule.default.cpk.target', '1.33', '默认CPK目标值', 'DECIMAL', 0, 'system'),
('chemical.pulsar.service.url', 'pulsar://localhost:6650', 'Pulsar服务地址', 'STRING', 0, 'system'),
('chemical.pulsar.topic.name', 'chemical-data-topic', 'Pulsar主题名称', 'STRING', 0, 'system'),
('chemical.data.retention.days', '90', '数据保留天数', 'INTEGER', 0, 'system'),
('chemical.log.retention.days', '30', '日志保留天数', 'INTEGER', 0, 'system'),
('chemical.task.timeout.minutes', '60', '任务超时时间(分钟)', 'INTEGER', 0, 'system')
GO

-- 4.2 插入默认规则配置
INSERT INTO [dbo].[rule_config] ([product_name], [process_name], [test_name], [rule_description], [created_by]) VALUES
('DEFAULT', 'DEFAULT', 'DEFAULT', '系统默认规则配置，适用于所有未单独配置的产品-过程-测试组合', 'system')
GO

-- =============================================
-- 5. 创建视图（可选）
-- =============================================

-- 5.1 化学数据统计视图
CREATE VIEW [dbo].[v_chemical_statistics] AS
SELECT 
    product_name,
    process_name,
    test_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN examine1 IS NOT NULL AND examine1 != '' THEN 1 END) as valid_count,
    COUNT(CASE WHEN not_process = 1 THEN 1 END) as not_process_count,
    COUNT(CASE WHEN is_exported = 1 THEN 1 END) as exported_count,
    MIN(examine_date) as min_date,
    MAX(examine_date) as max_date
FROM chemical
GROUP BY product_name, process_name, test_name
GO

-- 5.2 任务执行统计视图
CREATE VIEW [dbo].[v_task_execution_statistics] AS
SELECT 
    task_name,
    task_type,
    COUNT(*) as total_executions,
    COUNT(CASE WHEN task_status = 'SUCCESS' THEN 1 END) as success_count,
    COUNT(CASE WHEN task_status = 'FAILED' THEN 1 END) as failed_count,
    COUNT(CASE WHEN task_status = 'RUNNING' THEN 1 END) as running_count,
    AVG(CASE WHEN execution_duration IS NOT NULL THEN execution_duration END) as avg_duration,
    MAX(create_time) as last_execution
FROM chemical_task_monitor
GROUP BY task_name, task_type
GO

PRINT '化学审计系统数据库初始化完成！'
PRINT '请继续执行 chemical_audit_menu.sql 文件来创建菜单配置。'
GO
