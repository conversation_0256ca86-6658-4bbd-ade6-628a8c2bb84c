<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.RuleConfigMapper">
    
    <resultMap type="RuleConfig" id="RuleConfigResult">
        <result property="id"    column="id"    />
        <result property="productName"    column="product_name"    />
        <result property="processName"    column="process_name"    />
        <result property="testName"    column="test_name"    />
        <result property="enableControlLimitAdjustment"    column="enable_control_limit_adjustment"    />
        <result property="enableMovingRangeAdjustment"    column="enable_moving_range_adjustment"    />
        <result property="enableNinePointSameSideCheck"    column="enable_nine_point_same_side_check"    />
        <result property="enableSixPointTrendCheck"    column="enable_six_point_trend_check"    />
        <result property="enableCpkAdjustment"    column="enable_cpk_adjustment"    />
        <result property="cpkTarget"    column="cpk_target"    />
        <result property="isRefreshMode"    column="is_refresh_mode"    />
        <result property="ruleDescription"    column="rule_description"    />
        <result property="status"    column="status"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
    </resultMap>

    <sql id="selectRuleConfigVo">
        select id, product_name, process_name, test_name, enable_control_limit_adjustment, 
               enable_moving_range_adjustment, enable_nine_point_same_side_check, 
               enable_six_point_trend_check, enable_cpk_adjustment, cpk_target, 
               is_refresh_mode, rule_description, status, created_by, updated_by, 
               created_time, updated_time 
        from rule_config
    </sql>

    <select id="selectRuleConfigList" parameterType="RuleConfig" resultMap="RuleConfigResult">
        <include refid="selectRuleConfigVo"/>
        <where>  
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="testName != null  and testName != ''"> and test_name like concat('%', #{testName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by created_time desc
    </select>
    
    <select id="selectRuleConfigById" parameterType="Long" resultMap="RuleConfigResult">
        <include refid="selectRuleConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectRuleConfigByNames" resultMap="RuleConfigResult">
        <include refid="selectRuleConfigVo"/>
        where product_name = #{productName} and process_name = #{processName} and test_name = #{testName}
        and status = '1'
        order by created_time desc
        limit 1
    </select>
        
    <insert id="insertRuleConfig" parameterType="RuleConfig" useGeneratedKeys="true" keyProperty="id">
        insert into rule_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productName != null">product_name,</if>
            <if test="processName != null">process_name,</if>
            <if test="testName != null">test_name,</if>
            <if test="enableControlLimitAdjustment != null">enable_control_limit_adjustment,</if>
            <if test="enableMovingRangeAdjustment != null">enable_moving_range_adjustment,</if>
            <if test="enableNinePointSameSideCheck != null">enable_nine_point_same_side_check,</if>
            <if test="enableSixPointTrendCheck != null">enable_six_point_trend_check,</if>
            <if test="enableCpkAdjustment != null">enable_cpk_adjustment,</if>
            <if test="cpkTarget != null">cpk_target,</if>
            <if test="isRefreshMode != null">is_refresh_mode,</if>
            <if test="ruleDescription != null">rule_description,</if>
            <if test="status != null">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productName != null">#{productName},</if>
            <if test="processName != null">#{processName},</if>
            <if test="testName != null">#{testName},</if>
            <if test="enableControlLimitAdjustment != null">#{enableControlLimitAdjustment},</if>
            <if test="enableMovingRangeAdjustment != null">#{enableMovingRangeAdjustment},</if>
            <if test="enableNinePointSameSideCheck != null">#{enableNinePointSameSideCheck},</if>
            <if test="enableSixPointTrendCheck != null">#{enableSixPointTrendCheck},</if>
            <if test="enableCpkAdjustment != null">#{enableCpkAdjustment},</if>
            <if test="cpkTarget != null">#{cpkTarget},</if>
            <if test="isRefreshMode != null">#{isRefreshMode},</if>
            <if test="ruleDescription != null">#{ruleDescription},</if>
            <if test="status != null">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
         </trim>
    </insert>

    <update id="updateRuleConfig" parameterType="RuleConfig">
        update rule_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="productName != null">product_name = #{productName},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="testName != null">test_name = #{testName},</if>
            <if test="enableControlLimitAdjustment != null">enable_control_limit_adjustment = #{enableControlLimitAdjustment},</if>
            <if test="enableMovingRangeAdjustment != null">enable_moving_range_adjustment = #{enableMovingRangeAdjustment},</if>
            <if test="enableNinePointSameSideCheck != null">enable_nine_point_same_side_check = #{enableNinePointSameSideCheck},</if>
            <if test="enableSixPointTrendCheck != null">enable_six_point_trend_check = #{enableSixPointTrendCheck},</if>
            <if test="enableCpkAdjustment != null">enable_cpk_adjustment = #{enableCpkAdjustment},</if>
            <if test="cpkTarget != null">cpk_target = #{cpkTarget},</if>
            <if test="isRefreshMode != null">is_refresh_mode = #{isRefreshMode},</if>
            <if test="ruleDescription != null">rule_description = #{ruleDescription},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRuleConfigById" parameterType="Long">
        delete from rule_config where id = #{id}
    </delete>

    <delete id="deleteRuleConfigByIds" parameterType="String">
        delete from rule_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
