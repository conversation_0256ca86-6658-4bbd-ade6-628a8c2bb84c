package com.ruoyi.audit.service.impl;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.audit.domain.ChemicalTaskMonitor;
import com.ruoyi.audit.mapper.ChemicalTaskMonitorMapper;
import com.ruoyi.audit.service.IChemicalTaskMonitorService;

/**
 * 化学任务监控Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ChemicalTaskMonitorServiceImpl implements IChemicalTaskMonitorService 
{
    private static final Logger log = LoggerFactory.getLogger(ChemicalTaskMonitorServiceImpl.class);

    @Autowired
    private ChemicalTaskMonitorMapper chemicalTaskMonitorMapper;

    /**
     * 查询化学任务监控
     * 
     * @param id 化学任务监控主键
     * @return 化学任务监控
     */
    @Override
    public ChemicalTaskMonitor selectChemicalTaskMonitorById(Long id)
    {
        return chemicalTaskMonitorMapper.selectChemicalTaskMonitorById(id);
    }

    /**
     * 查询化学任务监控列表
     * 
     * @param chemicalTaskMonitor 化学任务监控
     * @return 化学任务监控
     */
    @Override
    public List<ChemicalTaskMonitor> selectChemicalTaskMonitorList(ChemicalTaskMonitor chemicalTaskMonitor)
    {
        return chemicalTaskMonitorMapper.selectChemicalTaskMonitorList(chemicalTaskMonitor);
    }

    /**
     * 新增化学任务监控
     * 
     * @param chemicalTaskMonitor 化学任务监控
     * @return 结果
     */
    @Override
    public int insertChemicalTaskMonitor(ChemicalTaskMonitor chemicalTaskMonitor)
    {
        chemicalTaskMonitor.setCreateTime(new Date());
        return chemicalTaskMonitorMapper.insertChemicalTaskMonitor(chemicalTaskMonitor);
    }

    /**
     * 修改化学任务监控
     * 
     * @param chemicalTaskMonitor 化学任务监控
     * @return 结果
     */
    @Override
    public int updateChemicalTaskMonitor(ChemicalTaskMonitor chemicalTaskMonitor)
    {
        chemicalTaskMonitor.setUpdateTime(new Date());
        return chemicalTaskMonitorMapper.updateChemicalTaskMonitor(chemicalTaskMonitor);
    }

    /**
     * 批量删除化学任务监控
     * 
     * @param ids 需要删除的化学任务监控主键
     * @return 结果
     */
    @Override
    public int deleteChemicalTaskMonitorByIds(Long[] ids)
    {
        return chemicalTaskMonitorMapper.deleteChemicalTaskMonitorByIds(ids);
    }

    /**
     * 删除化学任务监控信息
     * 
     * @param id 化学任务监控主键
     * @return 结果
     */
    @Override
    public int deleteChemicalTaskMonitorById(Long id)
    {
        return chemicalTaskMonitorMapper.deleteChemicalTaskMonitorById(id);
    }

    /**
     * 获取系统监控信息
     * 
     * @return 系统监控信息
     */
    @Override
    public Map<String, Object> getSystemMonitorInfo()
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取内存信息
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long totalMemory = memoryBean.getHeapMemoryUsage().getMax();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long freeMemory = totalMemory - usedMemory;
            
            result.put("totalMemory", totalMemory);
            result.put("usedMemory", usedMemory);
            result.put("freeMemory", freeMemory);
            result.put("memoryUsagePercent", (double) usedMemory / totalMemory * 100);
            
            // 获取CPU信息
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            result.put("availableProcessors", osBean.getAvailableProcessors());
            result.put("systemLoadAverage", osBean.getSystemLoadAverage());
            
            // 获取JVM信息
            result.put("jvmName", System.getProperty("java.vm.name"));
            result.put("jvmVersion", System.getProperty("java.vm.version"));
            result.put("javaVersion", System.getProperty("java.version"));
            
            // 获取操作系统信息
            result.put("osName", osBean.getName());
            result.put("osArch", osBean.getArch());
            result.put("osVersion", osBean.getVersion());
            
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取系统监控信息失败", e);
            result.put("success", false);
            result.put("message", "获取系统监控信息失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取任务执行统计
     * 
     * @return 任务执行统计
     */
    @Override
    public Map<String, Object> getTaskExecutionStats()
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询最近24小时的任务执行统计
            Map<String, Object> stats = chemicalTaskMonitorMapper.selectTaskExecutionStats();
            
            result.put("totalTasks", stats.get("totalTasks"));
            result.put("successTasks", stats.get("successTasks"));
            result.put("failedTasks", stats.get("failedTasks"));
            result.put("runningTasks", stats.get("runningTasks"));
            result.put("averageDuration", stats.get("averageDuration"));
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取任务执行统计失败", e);
            result.put("success", false);
            result.put("message", "获取任务执行统计失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取数据处理统计
     * 
     * @return 数据处理统计
     */
    @Override
    public Map<String, Object> getDataProcessingStats()
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询数据处理统计
            Map<String, Object> stats = chemicalTaskMonitorMapper.selectDataProcessingStats();
            
            result.put("totalRecords", stats.get("totalRecords"));
            result.put("processedRecords", stats.get("processedRecords"));
            result.put("modifiedRecords", stats.get("modifiedRecords"));
            result.put("errorRecords", stats.get("errorRecords"));
            result.put("processingRate", stats.get("processingRate"));
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取数据处理统计失败", e);
            result.put("success", false);
            result.put("message", "获取数据处理统计失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取错误日志统计
     * 
     * @return 错误日志统计
     */
    @Override
    public Map<String, Object> getErrorLogStats()
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询错误日志统计
            List<Map<String, Object>> errorStats = chemicalTaskMonitorMapper.selectErrorLogStats();
            
            result.put("errorLogs", errorStats);
            result.put("totalErrors", errorStats.size());
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取错误日志统计失败", e);
            result.put("success", false);
            result.put("message", "获取错误日志统计失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取性能监控数据
     * 
     * @return 性能监控数据
     */
    @Override
    public Map<String, Object> getPerformanceMonitorData()
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询性能监控数据
            List<Map<String, Object>> performanceData = chemicalTaskMonitorMapper.selectPerformanceMonitorData();
            
            result.put("performanceData", performanceData);
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取性能监控数据失败", e);
            result.put("success", false);
            result.put("message", "获取性能监控数据失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 记录任务执行信息
     * 
     * @param taskName 任务名称
     * @param status 执行状态
     * @param message 执行消息
     * @param duration 执行耗时
     * @return 结果
     */
    @Override
    public int recordTaskExecution(String taskName, String status, String message, Long duration)
    {
        try {
            ChemicalTaskMonitor monitor = new ChemicalTaskMonitor();
            monitor.setTaskName(taskName);
            monitor.setTaskStatus(status);
            monitor.setExecutionMessage(message);
            monitor.setExecutionDuration(duration);
            monitor.setExecutionTime(new Date());
            monitor.setCreateTime(new Date());
            
            return chemicalTaskMonitorMapper.insertChemicalTaskMonitor(monitor);
            
        } catch (Exception e) {
            log.error("记录任务执行信息失败", e);
            return 0;
        }
    }
}
