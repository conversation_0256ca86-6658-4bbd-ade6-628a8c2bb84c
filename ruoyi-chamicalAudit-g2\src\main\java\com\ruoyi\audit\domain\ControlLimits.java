package com.ruoyi.audit.domain;

/**
 * 控制限制参数
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ControlLimits 
{
    /** 均值 */
    private double fMean;
    
    /** 标准差 */
    private double fSp;
    
    /** 上控制限 */
    private double upperControlLimit;
    
    /** 下控制限 */
    private double lowerControlLimit;
    
    /** 工艺名称 */
    private String processName;
    
    /** 产品名称 */
    private String productName;
    
    /** 测试名称 */
    private String testName;

    public ControlLimits() {
    }

    public ControlLimits(double fMean, double fSp) {
        this.fMean = fMean;
        this.fSp = fSp;
        calculateControlLimits();
    }

    public ControlLimits(double fMean, double fSp, String processName, String productName, String testName) {
        this.fMean = fMean;
        this.fSp = fSp;
        this.processName = processName;
        this.productName = productName;
        this.testName = testName;
        calculateControlLimits();
    }

    /**
     * 计算控制限制
     */
    private void calculateControlLimits() {
        double threeSigma = 3 * fSp;
        this.upperControlLimit = fMean + threeSigma / 2;
        this.lowerControlLimit = fMean - threeSigma / 2;
    }

    public double getfMean() {
        return fMean;
    }

    public void setfMean(double fMean) {
        this.fMean = fMean;
        calculateControlLimits();
    }

    public double getfSp() {
        return fSp;
    }

    public void setfSp(double fSp) {
        this.fSp = fSp;
        calculateControlLimits();
    }

    public double getUpperControlLimit() {
        return upperControlLimit;
    }

    public double getLowerControlLimit() {
        return lowerControlLimit;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getTestName() {
        return testName;
    }

    public void setTestName(String testName) {
        this.testName = testName;
    }

    @Override
    public String toString() {
        return "ControlLimits{" +
                "fMean=" + fMean +
                ", fSp=" + fSp +
                ", upperControlLimit=" + upperControlLimit +
                ", lowerControlLimit=" + lowerControlLimit +
                ", processName='" + processName + '\'' +
                ", productName='" + productName + '\'' +
                ", testName='" + testName + '\'' +
                '}';
    }
}
