<template>
  <div class="app-container">
    <!-- 页面标题和说明 -->
    <div class="page-header">
      <div class="page-title">
        <i class="el-icon-document"></i>
        <span>测试方案配置管理</span>
      </div>
      <div class="page-description">
        <p>📋 管理测试方案组和测试参数的二层级联配置系统</p>
        <el-alert
          title="使用提示：点击测试方案组行查看对应的测试参数明细"
          type="info"
          :closable="false"
          show-icon
          style="margin-top: 10px;">
        </el-alert>
      </div>
    </div>

    <!-- 测试方案组表格 -->
    <el-card class="plan-group-card enhanced-card" style="margin-bottom: 20px;">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-document"></i>
          <span class="header-title">测试方案组管理</span>
          <el-badge :value="planGroupTotal" class="item-count-badge" type="primary" />
        </div>
        <div class="header-right">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddPlanGroup">
            <span>新增方案组</span>
          </el-button>
          <el-button type="danger" icon="el-icon-delete" size="small" :disabled="planGroupMultiple" @click="handleBatchDeletePlanGroup">
            <span>批量删除</span>
          </el-button>
          <el-button type="success" icon="el-icon-download" size="small" @click="handleExportPlanGroup">
            <span>导出</span>
          </el-button>
        </div>
      </div>

      <!-- 测试方案组查询条件 -->
      <div class="search-section">
        <el-form :model="planGroupQueryParams" ref="planGroupQueryForm" size="small" :inline="true" label-width="80px" class="search-form">
          <el-form-item label="方案编号" prop="planCode">
            <el-autocomplete
              v-model="planGroupQueryParams.planCode"
              :fetch-suggestions="queryPlanCodeSuggestions"
              placeholder="请输入方案编号"
              clearable
              style="width: 220px;"
              @select="handlePlanCodeSelect"
              @focus="handlePlanCodeFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-document"
            />
          </el-form-item>
          <el-form-item label="性能类型" prop="performanceType">
            <el-autocomplete
              v-model="planGroupQueryParams.performanceType"
              :fetch-suggestions="queryPerformanceTypeSuggestions"
              placeholder="请输入性能类型"
              clearable
              style="width: 220px;"
              @focus="handlePerformanceTypeFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-lightning"
            />
          </el-form-item>
          <el-form-item label="测试设备" prop="testEquipment">
            <el-autocomplete
              v-model="planGroupQueryParams.testEquipment"
              :fetch-suggestions="queryTestEquipmentSuggestions"
              placeholder="请输入测试设备"
              clearable
              style="width: 220px;"
              @focus="handleTestEquipmentFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-cpu"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handlePlanGroupQuery" class="search-btn">
              <span>搜索</span>
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetPlanGroupQuery" class="reset-btn">
              <span>重置</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 测试方案组表格 -->
      <div class="table-container">
        <el-table
          v-loading="planGroupLoading"
          :data="planGroupList"
          style="width: 100%"
          @selection-change="handlePlanGroupSelectionChange"
          @row-click="handlePlanGroupRowClick"
          highlight-current-row
          :row-class-name="getPlanGroupRowClassName"
          ref="planGroupTable"
          class="enhanced-table"
          element-loading-text="正在加载测试方案数据..."
          element-loading-spinner="el-icon-loading"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column type="index" label="序号" width="60" align="center">
            <template slot-scope="scope">
              <span class="index-number">{{ scope.$index + 1 + (planGroupQueryParams.pageNum - 1) * planGroupQueryParams.pageSize }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="planCode" label="方案编号" min-width="160" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="plan-code-cell">
                <i class="el-icon-document plan-icon"></i>
                <span class="plan-code">{{ scope.row.planCode }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="performanceType" label="性能类型" width="130">
            <template slot-scope="scope">
              <el-tag size="small" :type="getPerformanceTypeTag(scope.row.performanceType)">
                {{ scope.row.performanceType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="performanceName" label="性能名称" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="performance-name-cell">
                <i class="el-icon-lightning performance-icon"></i>
                <span>{{ scope.row.performanceName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="testEquipment" label="测试设备" width="130">
            <template slot-scope="scope">
              <div class="equipment-cell">
                <i class="el-icon-cpu equipment-icon"></i>
                <span>{{ scope.row.testEquipment || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="创建人" width="100">
            <template slot-scope="scope">
              <div class="user-info">
                <i class="el-icon-user"></i>
                <span>{{ scope.row.createBy || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template slot-scope="scope">
              <div class="time-info">
                <i class="el-icon-time"></i>
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updateBy" label="更新人" width="100">
            <template slot-scope="scope">
              <div class="user-info">
                <i class="el-icon-user"></i>
                <span>{{ scope.row.updateBy || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="160">
            <template slot-scope="scope">
              <div class="time-info">
                <i class="el-icon-time"></i>
                <span>{{ parseTime(scope.row.updateTime) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="附件" width="80" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.attachments && scope.row.attachments.trim()"
                size="mini"
                type="text"
                @click.stop="handleViewPlanGroupAttachments(scope.row.attachments)"
                class="attachment-btn"
              >
                <i class="el-icon-paperclip"></i>
                查看
              </el-button>
              <span v-else class="empty-data">-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button size="mini" type="text" @click="handleEditPlanGroup(scope.row)" class="edit-btn">
                  <i class="el-icon-edit"></i>
                  编辑
                </el-button>
                <el-button size="mini" type="text" @click="handleCopyPlanGroup(scope.row)" class="copy-btn">
                  <i class="el-icon-copy-document"></i>
                  复制
                </el-button>
                <el-button size="mini" type="text" @click="handleDeletePlanGroup(scope.row)" class="delete-btn">
                  <i class="el-icon-delete"></i>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        v-show="planGroupTotal > 0"
        :total="planGroupTotal"
        :page.sync="planGroupQueryParams.pageNum"
        :limit.sync="planGroupQueryParams.pageSize"
        @pagination="getPlanGroupList"
        style="margin-top: 15px;"
      />
    </el-card>

    <!-- 测试参数明细表格 -->
    <el-card class="test-param-card enhanced-card" v-show="currentPlanGroup">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-data-line"></i>
          <span class="header-title">测试参数明细</span>
          <div class="plan-group-indicator" v-if="currentPlanGroup">
            <el-tag type="warning" size="small">
              <i class="el-icon-document"></i>
              {{ currentPlanGroup.planCode }}
            </el-tag>
          </div>
          <el-badge :value="testParamTotal" class="item-count-badge" type="warning" />
        </div>
        <div class="header-right">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAddTestParam" :disabled="!currentPlanGroup">
            <span>新增参数</span>
          </el-button>
          <el-button type="danger" icon="el-icon-delete" size="small" :disabled="testParamMultiple || !currentPlanGroup" @click="handleBatchDeleteTestParam">
            <span>批量删除</span>
          </el-button>
          <el-button type="success" icon="el-icon-download" size="small" @click="handleExportTestParam" :disabled="!currentPlanGroup">
            <span>导出</span>
          </el-button>
        </div>
      </div>

      <!-- 测试参数明细查询条件 -->
      <div class="search-section">
        <el-form :model="testParamQueryParams" ref="testParamQueryForm" size="small" :inline="true" label-width="80px" class="search-form">
          <el-form-item label="参数名称" prop="paramName">
            <el-autocomplete
              v-model="testParamQueryParams.paramName"
              :fetch-suggestions="queryParamNameSuggestions"
              placeholder="请输入参数名称"
              clearable
              style="width: 220px;"
              @focus="handleParamNameFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-data-line"
            />
          </el-form-item>
          <el-form-item label="参数单位" prop="unit">
            <el-autocomplete
              v-model="testParamQueryParams.unit"
              :fetch-suggestions="queryUnitSuggestions"
              placeholder="请输入参数单位"
              clearable
              style="width: 220px;"
              @focus="handleUnitFocus"
              :trigger-on-focus="true"
              prefix-icon="el-icon-price-tag"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleTestParamQuery" class="search-btn">
              <span>搜索</span>
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetTestParamQuery" class="reset-btn">
              <span>重置</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 测试参数明细表格 -->
      <div class="table-container">
        <el-table
          v-loading="testParamLoading"
          :data="testParamList"
          @selection-change="handleTestParamSelectionChange"
          @row-click="handleTestParamRowClick"
          highlight-current-row
          style="width: 100%"
          :row-class-name="getTestParamRowClassName"
          ref="testParamTable"
          class="enhanced-table"
          element-loading-text="正在加载测试参数数据..."
          element-loading-spinner="el-icon-loading"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column type="index" label="序号" width="60" align="center">
            <template slot-scope="scope">
              <span class="index-number">{{ scope.$index + 1 + (testParamQueryParams.pageNum - 1) * testParamQueryParams.pageSize }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="paramName" label="参数名称" min-width="150" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="test-param-name-cell">
                <i class="el-icon-data-line test-param-icon"></i>
                <span class="test-param-name">{{ scope.row.paramName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="paramValue" label="参数数值" width="120" align="right">
            <template slot-scope="scope">
              <span class="test-param-value" v-if="scope.row.paramValue !== null">{{ scope.row.paramValue }}</span>
              <span v-else class="empty-data">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位" width="80">
            <template slot-scope="scope">
              <el-tag size="small" type="success" v-if="scope.row.unit">{{ scope.row.unit }}</el-tag>
              <span v-else class="empty-data">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="创建人" width="100">
            <template slot-scope="scope">
              <div class="user-info">
                <i class="el-icon-user"></i>
                <span>{{ scope.row.createBy || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template slot-scope="scope">
              <div class="time-info">
                <i class="el-icon-time"></i>
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updateBy" label="更新人" width="100">
            <template slot-scope="scope">
              <div class="user-info">
                <i class="el-icon-user"></i>
                <span>{{ scope.row.updateBy || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="160">
            <template slot-scope="scope">
              <div class="time-info">
                <i class="el-icon-time"></i>
                <span>{{ parseTime(scope.row.updateTime) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="附件" width="80" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.attachments && scope.row.attachments.trim()"
                size="mini"
                type="text"
                @click.stop="handleViewTestParamAttachments(scope.row.attachments)"
                class="attachment-btn"
              >
                <i class="el-icon-paperclip"></i>
                查看
              </el-button>
              <span v-else class="empty-data">-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" align="center" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button size="mini" type="text" @click.stop="handleEditTestParam(scope.row)" class="edit-btn">
                  <i class="el-icon-edit"></i>
                  编辑
                </el-button>
                <el-button size="mini" type="text" @click.stop="handleDeleteTestParam(scope.row)" class="delete-btn">
                  <i class="el-icon-delete"></i>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        v-show="testParamTotal > 0"
        :total="testParamTotal"
        :page.sync="testParamQueryParams.pageNum"
        :limit.sync="testParamQueryParams.pageSize"
        @pagination="getTestParamList"
        style="margin-top: 15px;"
      />
    </el-card>

    <!-- 测试方案组对话框 -->
    <el-dialog :title="planGroupTitle" :visible.sync="planGroupOpen" width="800px" append-to-body v-dialogDrag>
      <el-form ref="planGroupForm" :model="planGroupForm" :rules="planGroupRules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="方案编号" prop="planCode">
              <el-input v-model="planGroupForm.planCode" placeholder="请输入方案编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性能类型" prop="performanceType">
              <el-input v-model="planGroupForm.performanceType" placeholder="请输入性能类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="性能名称" prop="performanceName">
              <el-input v-model="planGroupForm.performanceName" placeholder="请输入性能名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测试设备" prop="testEquipment">
              <el-input v-model="planGroupForm.testEquipment" placeholder="请输入测试设备" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="附件上传">
          <el-upload
            ref="planGroupUpload"
            :limit="5"
            accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx"
            :action="uploadFileUrl"
            :headers="uploadHeaders"
            :file-list="planGroupFileList"
            :on-success="handlePlanGroupUploadSuccess"
            :on-remove="handlePlanGroupUploadRemove"
            :before-upload="beforePlanGroupUpload"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png/gif/pdf/doc/xls文件，且不超过10MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="planGroupForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPlanGroupForm">确 定</el-button>
        <el-button @click="cancelPlanGroup">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 测试参数明细对话框 -->
    <el-dialog :title="testParamTitle" :visible.sync="testParamOpen" width="600px" append-to-body v-dialogDrag>
      <el-form ref="testParamForm" :model="testParamForm" :rules="testParamRules" label-width="100px">
        <el-form-item label="参数名称" prop="paramName">
          <el-input v-model="testParamForm.paramName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="参数数值" prop="paramValue">
              <el-input v-model="testParamForm.paramValue" placeholder="请输入参数数值（支持文本格式）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数单位" prop="unit">
              <el-input v-model="testParamForm.unit" placeholder="请输入参数单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="附件上传">
          <el-upload
            ref="testParamUpload"
            :limit="5"
            accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx"
            :action="uploadFileUrl"
            :headers="uploadHeaders"
            :file-list="testParamFileList"
            :on-success="handleTestParamUploadSuccess"
            :on-remove="handleTestParamUploadRemove"
            :before-upload="beforeTestParamUpload"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png/gif/pdf/doc/xls文件，且不超过10MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="testParamForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTestParamForm">确 定</el-button>
        <el-button @click="cancelTestParam">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 附件查看对话框 -->
    <el-dialog title="附件列表" :visible.sync="attachmentDialogVisible" width="600px" append-to-body>
      <el-table :data="attachmentList" style="width: 100%">
        <el-table-column prop="name" label="文件名" show-overflow-tooltip />
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="downloadAttachment(scope.row.url, scope.row.name)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  listTestPlanGroup, getTestPlanGroup, delTestPlanGroup, addTestPlanGroup, updateTestPlanGroup,
  exportTestPlanGroup, getTestPlanGroupOptions
} from "@/api/material/testPlanGroup";
import {
  listTestParamItem, getTestParamItem, delTestParamItem, addTestParamItem, updateTestParamItem,
  exportTestParamItem, getTestParamItemOptions, listByPlanGroupId
} from "@/api/material/testParamItem";
import { getToken } from "@/utils/auth";

export default {
  name: "TestPlan",
  data() {
    return {
      // 测试方案组相关
      planGroupLoading: false,
      planGroupList: [],
      planGroupTotal: 0,
      planGroupOpen: false,
      planGroupTitle: "",
      planGroupForm: {},
      planGroupFileList: [],
      planGroupIds: [],
      planGroupSingle: true,
      planGroupMultiple: true,
      planGroupQueryParams: {
        pageNum: 1,
        pageSize: 10,
        planCode: null,
        performanceType: null,
        testEquipment: null
      },
      planGroupRules: {
        planCode: [
          { required: true, message: "方案编号不能为空", trigger: "blur" }
        ],
        performanceType: [
          { required: true, message: "性能类型不能为空", trigger: "blur" }
        ]
      },

      // 测试参数明细相关
      testParamLoading: false,
      testParamList: [],
      testParamTotal: 0,
      testParamOpen: false,
      testParamTitle: "",
      testParamForm: {},
      testParamFileList: [],
      testParamIds: [],
      testParamSingle: true,
      testParamMultiple: true,
      testParamQueryParams: {
        pageNum: 1,
        pageSize: 10,
        planGroupId: null,
        paramName: null,
        unit: null
      },
      testParamRules: {
        paramName: [
          { required: true, message: "参数名称不能为空", trigger: "blur" }
        ]
      },

      // 当前选中的测试方案组
      currentPlanGroup: null,

      // 附件查看
      attachmentDialogVisible: false,
      attachmentList: [],

      // 搜索建议数据
      planCodeSuggestions: [],
      performanceTypeSuggestions: [],
      testEquipmentSuggestions: [],
      paramNameSuggestions: [],
      unitSuggestions: [],

      // 上传相关
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      }
    };
  },
  created() {
    this.getPlanGroupList();
    this.loadSuggestions();
  },
  methods: {
    /** 获取性能类型标签颜色 */
    getPerformanceTypeTag(type) {
      const typeMap = {
        '力学性能': 'success',
        '电学性能': 'primary',
        '热学性能': 'warning',
        '光学性能': 'info',
        '化学性能': 'danger',
        '物理性能': ''
      };
      return typeMap[type] || '';
    },

    /** 查询测试方案组列表 */
    getPlanGroupList() {
      this.planGroupLoading = true;
      listTestPlanGroup(this.planGroupQueryParams).then(response => {
        this.planGroupList = response.rows;
        this.planGroupTotal = response.total;
        this.planGroupLoading = false;
      });
    },

    /** 查询测试参数明细列表 */
    getTestParamList() {
      if (!this.currentPlanGroup) return;

      this.testParamLoading = true;
      this.testParamQueryParams.planGroupId = this.currentPlanGroup.planGroupId;
      listTestParamItem(this.testParamQueryParams).then(response => {
        this.testParamList = response.rows;
        this.testParamTotal = response.total;
        this.testParamLoading = false;

        // 更新参数筛选选项
        this.$nextTick(() => {
          this.updateParamFilterOptions();
        });
      });
    },

    /** 加载搜索建议数据 */
    loadSuggestions() {
      // 获取方案编号建议
      getTestPlanGroupOptions({ type: 'planCode' }).then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.planCodeSuggestions = response.data.map(item => ({ value: item }));
        }
      });

      // 获取性能类型建议
      getTestPlanGroupOptions({ type: 'performanceType' }).then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));
        }
      });

      // 获取测试设备建议
      getTestPlanGroupOptions({ type: 'testEquipment' }).then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));
        }
      });

      // 获取参数名称建议
      getTestParamItemOptions({ type: 'paramName' }).then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.paramNameSuggestions = response.data.map(item => ({ value: item }));
        }
      });

      // 获取参数单位建议
      getTestParamItemOptions({ type: 'unit' }).then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.unitSuggestions = response.data.map(item => ({ value: item }));
        }
      });
    },

    /** 搜索建议方法 */
    queryPlanCodeSuggestions(queryString, cb) {
      let suggestions = this.planCodeSuggestions;
      if (queryString) {
        suggestions = this.planCodeSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    queryPerformanceTypeSuggestions(queryString, cb) {
      let suggestions = this.performanceTypeSuggestions;
      if (queryString) {
        suggestions = this.performanceTypeSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    queryTestEquipmentSuggestions(queryString, cb) {
      let suggestions = this.testEquipmentSuggestions;
      if (queryString) {
        suggestions = this.testEquipmentSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    queryParamNameSuggestions(queryString, cb) {
      let suggestions = this.paramNameSuggestions;
      if (queryString) {
        suggestions = this.paramNameSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    queryUnitSuggestions(queryString, cb) {
      let suggestions = this.unitSuggestions;
      if (queryString) {
        suggestions = this.unitSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 焦点事件 */
    handlePlanCodeFocus() {
      getTestPlanGroupOptions({ type: 'planCode' }).then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.planCodeSuggestions = response.data.map(item => ({ value: item }));
        }
      });
    },

    handlePerformanceTypeFocus() {
      getTestPlanGroupOptions({ type: 'performanceType' }).then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));
        }
      });
    },

    handleTestEquipmentFocus() {
      getTestPlanGroupOptions({ type: 'testEquipment' }).then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));
        }
      });
    },

    handleParamNameFocus() {
      // 只获取当前选中测试方案组下的参数名称选项
      if (this.currentPlanGroup && this.currentPlanGroup.planGroupId) {
        // 从当前显示的测试参数列表中提取参数名称
        const paramNames = [...new Set(this.testParamList.map(item => item.paramName).filter(name => name))];
        this.paramNameSuggestions = paramNames.map(name => ({ value: name }));
      } else {
        // 如果没有选中方案组，获取所有参数名称
        getTestParamItemOptions({ type: 'paramName' }).then(response => {
          if (response.data && Array.isArray(response.data)) {
            this.paramNameSuggestions = response.data.map(item => ({ value: item }));
          }
        });
      }
    },

    handleUnitFocus() {
      // 只获取当前选中测试方案组下的参数单位选项
      if (this.currentPlanGroup && this.currentPlanGroup.planGroupId) {
        // 从当前显示的测试参数列表中提取参数单位
        const units = [...new Set(this.testParamList.map(item => item.unit).filter(unit => unit))];
        this.unitSuggestions = units.map(unit => ({ value: unit }));
      } else {
        // 如果没有选中方案组，获取所有参数单位
        getTestParamItemOptions({ type: 'unit' }).then(response => {
          if (response.data && Array.isArray(response.data)) {
            this.unitSuggestions = response.data.map(item => ({ value: item }));
          }
        });
      }
    },

    /** 测试方案组相关方法 */
    handlePlanGroupSelectionChange(selection) {
      this.planGroupIds = selection.map(item => item.planGroupId);
      this.planGroupSingle = selection.length !== 1;
      this.planGroupMultiple = !selection.length;
    },

    handlePlanGroupRowClick(row) {
      this.currentPlanGroup = row;
      this.$refs.planGroupTable.toggleRowSelection(row);

      // 清空测试参数筛选条件
      this.testParamQueryParams.paramName = null;
      this.testParamQueryParams.unit = null;
      this.resetForm("testParamQueryForm");

      // 加载测试参数列表
      this.getTestParamList();

      // 延迟更新筛选选项，确保testParamList已加载
      this.$nextTick(() => {
        this.updateParamFilterOptions();
      });
    },

    getPlanGroupRowClassName({row, rowIndex}) {
      if (this.currentPlanGroup && row.planGroupId === this.currentPlanGroup.planGroupId) {
        return 'current-row';
      }
      return '';
    },

    handlePlanGroupQuery() {
      this.planGroupQueryParams.pageNum = 1;
      this.getPlanGroupList();
    },

    resetPlanGroupQuery() {
      this.resetForm("planGroupQueryForm");
      this.handlePlanGroupQuery();
    },

    handleAddPlanGroup() {
      this.resetPlanGroupForm();
      this.planGroupOpen = true;
      this.planGroupTitle = "添加测试方案组";
    },

    handleEditPlanGroup(row) {
      this.resetPlanGroupForm();
      const planGroupId = row.planGroupId || this.planGroupIds;
      getTestPlanGroup(planGroupId).then(response => {
        this.planGroupForm = response.data;
        this.parsePlanGroupAttachments();
        this.planGroupOpen = true;
        this.planGroupTitle = "修改测试方案组";
      });
    },

    handleCopyPlanGroup(row) {
      this.resetPlanGroupForm();
      const planGroupId = row.planGroupId;
      getTestPlanGroup(planGroupId).then(response => {
        this.planGroupForm = response.data;
        this.planGroupForm.planGroupId = null;
        this.planGroupForm.planCode = this.planGroupForm.planCode + "_copy";
        this.parsePlanGroupAttachments();
        this.planGroupOpen = true;
        this.planGroupTitle = "复制测试方案组";
      });
    },

    handleDeletePlanGroup(row) {
      const planGroupIds = row.planGroupId || this.planGroupIds;
      this.$modal.confirm('是否确认删除测试方案组编号为"' + row.planCode + '"的数据项？').then(function() {
        return delTestPlanGroup(planGroupIds);
      }).then(() => {
        this.getPlanGroupList();
        this.$modal.msgSuccess("删除成功");
        if (this.currentPlanGroup && this.currentPlanGroup.planGroupId === row.planGroupId) {
          this.currentPlanGroup = null;
          this.testParamList = [];
        }
      }).catch(() => {});
    },

    handleBatchDeletePlanGroup() {
      const planGroupIds = this.planGroupIds;
      this.$modal.confirm('是否确认删除选中的' + planGroupIds.length + '条数据？').then(function() {
        return delTestPlanGroup(planGroupIds);
      }).then(() => {
        this.getPlanGroupList();
        this.$modal.msgSuccess("删除成功");
        this.currentPlanGroup = null;
        this.testParamList = [];
      }).catch(() => {});
    },

    handleExportPlanGroup() {
      this.download('material/testPlanGroup/export', {
        ...this.planGroupQueryParams
      }, `test_plan_group_${new Date().getTime()}.xlsx`)
    },

    /** 更新参数筛选选项 */
    updateParamFilterOptions() {
      if (this.currentPlanGroup && this.testParamList.length > 0) {
        // 从当前测试参数列表中提取唯一的参数名称和单位
        const paramNames = [...new Set(this.testParamList.map(item => item.paramName).filter(name => name))];
        const units = [...new Set(this.testParamList.map(item => item.unit).filter(unit => unit))];

        this.paramNameSuggestions = paramNames.map(name => ({ value: name }));
        this.unitSuggestions = units.map(unit => ({ value: unit }));
      }
    },

    /** 测试参数明细相关方法 */
    handleTestParamSelectionChange(selection) {
      this.testParamIds = selection.map(item => item.testParamId);
      this.testParamSingle = selection.length !== 1;
      this.testParamMultiple = !selection.length;
    },

    handleTestParamRowClick(row) {
      this.$refs.testParamTable.toggleRowSelection(row);
    },

    handleTestParamQuery() {
      this.testParamQueryParams.pageNum = 1;
      this.getTestParamList();
    },

    resetTestParamQuery() {
      this.resetForm("testParamQueryForm");
      this.handleTestParamQuery();
    },

    handleAddTestParam() {
      this.resetTestParamForm();
      this.testParamOpen = true;
      this.testParamTitle = "添加测试参数明细";
    },

    handleEditTestParam(row) {
      this.resetTestParamForm();
      const testParamId = row.testParamId || this.testParamIds;
      getTestParamItem(testParamId).then(response => {
        this.testParamForm = response.data;
        this.parseTestParamAttachments();
        this.testParamOpen = true;
        this.testParamTitle = "修改测试参数明细";
      });
    },

    handleDeleteTestParam(row) {
      const testParamIds = row.testParamId || this.testParamIds;
      this.$modal.confirm('是否确认删除参数名称为"' + row.paramName + '"的数据项？').then(function() {
        return delTestParamItem(testParamIds);
      }).then(() => {
        this.getTestParamList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    handleBatchDeleteTestParam() {
      const testParamIds = this.testParamIds;
      this.$modal.confirm('是否确认删除选中的' + testParamIds.length + '条数据？').then(function() {
        return delTestParamItem(testParamIds);
      }).then(() => {
        this.getTestParamList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    handleExportTestParam() {
      this.download('material/testParamItem/export', {
        ...this.testParamQueryParams
      }, `test_param_item_${new Date().getTime()}.xlsx`)
    },

    /** 表单相关方法 */
    resetPlanGroupForm() {
      this.planGroupForm = {
        planGroupId: null,
        planCode: null,
        performanceType: null,
        performanceName: null,
        testEquipment: null,
        attachments: null,
        remark: null
      };
      this.planGroupFileList = [];
      this.resetForm("planGroupForm");
    },

    resetTestParamForm() {
      this.testParamForm = {
        testParamId: null,
        planGroupId: this.currentPlanGroup ? this.currentPlanGroup.planGroupId : null,
        paramName: null,
        paramValue: null,
        unit: null,
        attachments: null,
        remark: null
      };
      this.testParamFileList = [];
      this.resetForm("testParamForm");
    },

    submitPlanGroupForm() {
      this.$refs["planGroupForm"].validate(valid => {
        if (valid) {
          this.planGroupForm.attachments = this.planGroupFileList.map(file => file.url).join(',');
          if (this.planGroupForm.planGroupId != null) {
            updateTestPlanGroup(this.planGroupForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.planGroupOpen = false;
              this.getPlanGroupList();
            });
          } else {
            addTestPlanGroup(this.planGroupForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.planGroupOpen = false;
              this.getPlanGroupList();
            });
          }
        }
      });
    },

    submitTestParamForm() {
      this.$refs["testParamForm"].validate(valid => {
        if (valid) {
          this.testParamForm.attachments = this.testParamFileList.map(file => file.url).join(',');
          if (this.testParamForm.testParamId != null) {
            updateTestParamItem(this.testParamForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.testParamOpen = false;
              this.getTestParamList();
            });
          } else {
            addTestParamItem(this.testParamForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.testParamOpen = false;
              this.getTestParamList();
            });
          }
        }
      });
    },

    cancelPlanGroup() {
      this.planGroupOpen = false;
      this.resetPlanGroupForm();
    },

    cancelTestParam() {
      this.testParamOpen = false;
      this.resetTestParamForm();
    },

    /** 附件相关方法 */
    parsePlanGroupAttachments() {
      if (this.planGroupForm.attachments) {
        const urls = this.planGroupForm.attachments.split(',');
        this.planGroupFileList = urls.map((url, index) => ({
          name: url.substring(url.lastIndexOf('/') + 1),
          url: url,
          uid: index
        }));
      }
    },

    parseTestParamAttachments() {
      if (this.testParamForm.attachments) {
        const urls = this.testParamForm.attachments.split(',');
        this.testParamFileList = urls.map((url, index) => ({
          name: url.substring(url.lastIndexOf('/') + 1),
          url: url,
          uid: index
        }));
      }
    },

    handlePlanGroupUploadSuccess(response, file) {
      this.planGroupFileList.push({
        name: file.name,
        url: response.url,
        uid: file.uid
      });
    },

    handleTestParamUploadSuccess(response, file) {
      this.testParamFileList.push({
        name: file.name,
        url: response.url,
        uid: file.uid
      });
    },

    handlePlanGroupUploadRemove(file) {
      const index = this.planGroupFileList.findIndex(item => item.uid === file.uid);
      if (index > -1) {
        this.planGroupFileList.splice(index, 1);
      }
    },

    handleTestParamUploadRemove(file) {
      const index = this.testParamFileList.findIndex(item => item.uid === file.uid);
      if (index > -1) {
        this.testParamFileList.splice(index, 1);
      }
    },

    beforePlanGroupUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
      }
      return isLt10M;
    },

    beforeTestParamUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
      }
      return isLt10M;
    },

    handleViewPlanGroupAttachments(attachments) {
      this.viewAttachments(attachments);
    },

    handleViewTestParamAttachments(attachments) {
      this.viewAttachments(attachments);
    },

    viewAttachments(attachments) {
      if (!attachments) return;
      const urls = attachments.split(',');
      this.attachmentList = urls.map(url => ({
        name: url.substring(url.lastIndexOf('/') + 1),
        url: url
      }));
      this.attachmentDialogVisible = true;
    },

    downloadAttachment(url, name) {
      const link = document.createElement('a');
      link.href = url;
      link.download = name;
      link.click();
    },

    handlePlanCodeSelect(item) {
      this.planGroupQueryParams.planCode = item.value;
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-title i {
  margin-right: 10px;
  color: #409EFF;
  font-size: 28px;
}

.page-description p {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

/* 增强卡片样式 */
.enhanced-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  background: white;
}

.enhanced-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left i {
  color: #409EFF;
  font-size: 18px;
}

.header-title {
  font-weight: bold;
  font-size: 16px;
  color: #2c3e50;
}

.item-count-badge {
  margin-left: 8px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.header-right .el-button {
  border-radius: 6px;
  font-weight: 500;
}

/* 搜索区域样式 */
.search-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  border: 1px solid #e9ecef;
}

.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 10px;
}

.search-form .el-autocomplete {
  border-radius: 6px;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;
}

.reset-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 表格容器样式 */
.table-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

/* 增强表格样式 */
.enhanced-table {
  border-radius: 8px;
}

.enhanced-table .el-table__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.enhanced-table .el-table__header th {
  background: transparent;
  color: white;
  font-weight: 600;
  border-bottom: none;
}

.enhanced-table .el-table__body tr:hover > td {
  background-color: #f0f9ff !important;
}

.enhanced-table .current-row {
  background-color: #e6f7ff !important;
}

.enhanced-table .current-row:hover > td {
  background-color: #e6f7ff !important;
}

/* 表格单元格样式 */
.index-number {
  font-weight: bold;
  color: #409EFF;
}

.plan-code-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plan-icon {
  color: #409EFF;
}

.plan-code {
  font-weight: 600;
  color: #2c3e50;
}

.performance-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.performance-icon {
  color: #E6A23C;
}

.equipment-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.equipment-icon {
  color: #67C23A;
}

.user-info, .time-info {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.user-info i, .time-info i {
  color: #909399;
}

.attachment-btn {
  color: #409EFF;
  font-weight: 500;
}

.attachment-btn:hover {
  color: #66b1ff;
}

.empty-data {
  color: #C0C4CC;
  font-style: italic;
}

/* 测试参数明细样式 */
.test-param-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.test-param-icon {
  color: #E6A23C;
}

.test-param-name {
  font-weight: 600;
  color: #2c3e50;
}

.test-param-value {
  font-weight: 600;
  color: #67C23A;
}

/* 指示器样式 */
.plan-group-indicator {
  margin-left: 10px;
}

.plan-group-indicator .el-tag {
  border-radius: 12px;
  padding: 0 8px;
  font-size: 12px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.edit-btn {
  color: #409EFF;
  font-weight: 500;
}

.edit-btn:hover {
  color: #66b1ff;
  background-color: #ecf5ff;
}

.copy-btn {
  color: #67C23A;
  font-weight: 500;
}

.copy-btn:hover {
  color: #85ce61;
  background-color: #f0f9ff;
}

.delete-btn {
  color: #F56C6C;
  font-weight: 500;
}

.delete-btn:hover {
  color: #f78989;
  background-color: #fef0f0;
}

.dialog-footer {
  text-align: center;
}

.el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .page-header {
    padding: 15px;
  }

  .header-right {
    flex-wrap: wrap;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
}

/* 统一按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}

.el-button--success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
  border: none !important;
}

.el-button--info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  border: none !important;
}

.el-button--warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  border: none !important;
}

.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
  border: none !important;
}
</style>
