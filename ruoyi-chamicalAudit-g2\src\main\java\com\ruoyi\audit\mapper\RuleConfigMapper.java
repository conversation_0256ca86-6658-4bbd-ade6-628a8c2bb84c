package com.ruoyi.audit.mapper;

import java.util.List;
import com.ruoyi.audit.domain.RuleConfig;

/**
 * 数据刷新规则配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface RuleConfigMapper 
{
    /**
     * 查询数据刷新规则配置
     * 
     * @param id 数据刷新规则配置主键
     * @return 数据刷新规则配置
     */
    public RuleConfig selectRuleConfigById(Long id);

    /**
     * 查询数据刷新规则配置列表
     * 
     * @param ruleConfig 数据刷新规则配置
     * @return 数据刷新规则配置集合
     */
    public List<RuleConfig> selectRuleConfigList(RuleConfig ruleConfig);

    /**
     * 根据产品、过程、测试名称查询规则配置
     * 
     * @param productName 产品名称
     * @param processName 过程名称
     * @param testName 测试名称
     * @return 数据刷新规则配置
     */
    public RuleConfig selectRuleConfigByNames(String productName, String processName, String testName);

    /**
     * 新增数据刷新规则配置
     * 
     * @param ruleConfig 数据刷新规则配置
     * @return 结果
     */
    public int insertRuleConfig(RuleConfig ruleConfig);

    /**
     * 修改数据刷新规则配置
     * 
     * @param ruleConfig 数据刷新规则配置
     * @return 结果
     */
    public int updateRuleConfig(RuleConfig ruleConfig);

    /**
     * 删除数据刷新规则配置
     * 
     * @param id 数据刷新规则配置主键
     * @return 结果
     */
    public int deleteRuleConfigById(Long id);

    /**
     * 批量删除数据刷新规则配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRuleConfigByIds(Long[] ids);
}
