package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.domain.ProcessParamItem;
import com.ruoyi.system.mapper.ProcessParamGroupMapper;
import com.ruoyi.system.domain.ProcessParamGroup;
import com.ruoyi.system.service.IProcessParamGroupService;

/**
 * 工艺参数组Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ProcessParamGroupServiceImpl implements IProcessParamGroupService 
{
    @Autowired
    private ProcessParamGroupMapper processParamGroupMapper;

    /**
     * 查询工艺参数组
     * 
     * @param groupId 工艺参数组主键
     * @return 工艺参数组
     */
    @Override
    public ProcessParamGroup selectProcessParamGroupByGroupId(Long groupId)
    {
        return processParamGroupMapper.selectProcessParamGroupByGroupId(groupId);
    }

    /**
     * 查询工艺参数组列表
     * 
     * @param processParamGroup 工艺参数组
     * @return 工艺参数组
     */
    @Override
    public List<ProcessParamGroup> selectProcessParamGroupList(ProcessParamGroup processParamGroup)
    {
        return processParamGroupMapper.selectProcessParamGroupList(processParamGroup);
    }

    /**
     * 根据材料ID查询工艺参数组列表
     * 
     * @param materialId 材料ID
     * @return 工艺参数组集合
     */
    @Override
    public List<ProcessParamGroup> selectProcessParamGroupByMaterialId(Long materialId)
    {
        return processParamGroupMapper.selectProcessParamGroupByMaterialId(materialId);
    }

    /**
     * 新增工艺参数组
     * 
     * @param processParamGroup 工艺参数组
     * @return 结果
     */
    @Transactional
    @Override
    public int insertProcessParamGroup(ProcessParamGroup processParamGroup)
    {
        processParamGroup.setCreateTime(DateUtils.getNowDate());
        int rows = processParamGroupMapper.insertProcessParamGroup(processParamGroup);
        insertProcessParamItem(processParamGroup);
        return rows;
    }

    /**
     * 修改工艺参数组
     * 
     * @param processParamGroup 工艺参数组
     * @return 结果
     */
    @Transactional
    @Override
    public int updateProcessParamGroup(ProcessParamGroup processParamGroup)
    {
        processParamGroup.setUpdateTime(DateUtils.getNowDate());
        processParamGroupMapper.deleteProcessParamItemByGroupId(processParamGroup.getGroupId());
        insertProcessParamItem(processParamGroup);
        return processParamGroupMapper.updateProcessParamGroup(processParamGroup);
    }

    /**
    /**
     * 批量删除工艺参数组
     *
     * @param groupIds 需要删除的工艺参数组主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteProcessParamGroupByGroupIds(Long[] groupIds)
    {
        processParamGroupMapper.deleteProcessParamItemByGroupIds(groupIds);
        return processParamGroupMapper.deleteProcessParamGroupByGroupIds(groupIds);
    }

    /**
     * 获取工艺参数组选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    @Override
    public List<String> selectProcessParamGroupOptions(String type)
    {
        if ("processType".equals(type)) {
            return processParamGroupMapper.selectProcessTypeOptions();
        } else if ("paramNumber".equals(type)) {
            return processParamGroupMapper.selectParamNumberOptions();
        }
        return new ArrayList<>();
    }

    /**
     * 删除工艺参数组信息
     * 
     * @param groupId 工艺参数组主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteProcessParamGroupByGroupId(Long groupId)
    {
        processParamGroupMapper.deleteProcessParamItemByGroupId(groupId);
        return processParamGroupMapper.deleteProcessParamGroupByGroupId(groupId);
    }

    /**
     * 新增工艺参数明细信息
     * 
     * @param processParamGroup 工艺参数组对象
     */
    public void insertProcessParamItem(ProcessParamGroup processParamGroup)
    {
        List<ProcessParamItem> processParamItemList = processParamGroup.getProcessParamItemList();
        Long groupId = processParamGroup.getGroupId();
        if (StringUtils.isNotNull(processParamItemList))
        {
            List<ProcessParamItem> list = new ArrayList<ProcessParamItem>();
            for (ProcessParamItem processParamItem : processParamItemList)
            {
                processParamItem.setGroupId(groupId);
                list.add(processParamItem);
            }
            if (list.size() > 0)
            {
                processParamGroupMapper.batchProcessParamItem(list);
            }
        }
    }
    
    /**
     * 获取工艺类型选项
     *
     * @return 工艺类型列表
     */
    @Override
    public List<String> selectProcessTypeOptions()
    {
        return processParamGroupMapper.selectProcessTypeOptions();
    }
    
    /**
     * 根据材料ID获取完整数据用于导出
     *
     * @param materialId 材料ID
     * @return 完整数据列表
     */
    @Override
    public List<Map<String, Object>> selectCompleteDataByMaterialId(Long materialId)
    {
        return processParamGroupMapper.selectCompleteDataByMaterialId(materialId);
    }
}
