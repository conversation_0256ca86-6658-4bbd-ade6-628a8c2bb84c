-- 材料管理菜单配置
-- 主菜单：材料管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('材料管理', 0, 5, 'material', NULL, '', 1, 0, 'M', '0', '0', '', 'build', 'admin', sysdate(), '', NULL, '材料管理目录');

-- 获取刚插入的材料管理菜单ID
SET @material_menu_id = LAST_INSERT_ID();

-- 子菜单：材料及参数配置
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('材料及参数配置', @material_menu_id, 1, 'config', 'material/config/index', '', 1, 0, 'C', '0', '0', 'material:material:list', 'tree', 'admin', sysdate(), '', NULL, '材料及参数配置菜单');

-- 子菜单：测试方案管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('测试方案管理', @material_menu_id, 2, 'testPlan', 'material/testPlan/index', '', 1, 0, 'C', '0', '0', 'material:testPlan:list', 'form', 'admin', sysdate(), '', NULL, '测试方案管理菜单');

-- 子菜单：结果录入
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('结果录入', @material_menu_id, 3, 'testResult', 'material/testResult/index', '', 1, 0, 'C', '0', '0', 'material:testResult:list', 'edit', 'admin', sysdate(), '', NULL, '结果录入菜单');

-- 子菜单：对比分析
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('对比分析', @material_menu_id, 4, 'trend', 'material/trend/index', '', 1, 0, 'C', '0', '0', 'material:trend:list', 'chart', 'admin', sysdate(), '', NULL, '对比分析菜单');

-- 获取材料及参数配置菜单ID
SET @config_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '材料及参数配置' AND parent_id = @material_menu_id);

-- 材料及参数配置权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('材料查询', @config_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'material:material:query', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('材料新增', @config_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'material:material:add', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('材料修改', @config_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'material:material:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('材料删除', @config_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'material:material:remove', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('材料导出', @config_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'material:material:export', '#', 'admin', sysdate(), '', NULL, '');

-- 工艺参数组权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数组查询', @config_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamGroup:query', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数组新增', @config_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamGroup:add', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数组修改', @config_menu_id, 8, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamGroup:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数组删除', @config_menu_id, 9, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamGroup:remove', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数组导出', @config_menu_id, 10, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamGroup:export', '#', 'admin', sysdate(), '', NULL, '');

-- 工艺参数明细权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数明细查询', @config_menu_id, 11, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamItem:query', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数明细新增', @config_menu_id, 12, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamItem:add', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数明细修改', @config_menu_id, 13, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamItem:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数明细删除', @config_menu_id, 14, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamItem:remove', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('参数明细导出', @config_menu_id, 15, '', '', '', 1, 0, 'F', '0', '0', 'material:processParamItem:export', '#', 'admin', sysdate(), '', NULL, '');

-- 获取测试方案管理菜单ID
SET @testplan_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '测试方案管理' AND parent_id = @material_menu_id);

-- 测试方案管理权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('方案查询', @testplan_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'material:testPlan:query', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('方案新增', @testplan_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'material:testPlan:add', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('方案修改', @testplan_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'material:testPlan:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('方案删除', @testplan_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'material:testPlan:remove', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('方案导出', @testplan_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'material:testPlan:export', '#', 'admin', sysdate(), '', NULL, '');

-- 获取结果录入菜单ID
SET @testresult_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '结果录入' AND parent_id = @material_menu_id);

-- 结果录入权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('结果查询', @testresult_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'material:testResult:query', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('结果新增', @testresult_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'material:testResult:add', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('结果修改', @testresult_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'material:testResult:edit', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('结果删除', @testresult_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'material:testResult:remove', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('结果导出', @testresult_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'material:testResult:export', '#', 'admin', sysdate(), '', NULL, '');

-- 获取对比分析菜单ID
SET @trend_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '对比分析' AND parent_id = @material_menu_id);

-- 对比分析权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('分析查询', @trend_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'material:trend:query', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('分析列表', @trend_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'material:trend:list', '#', 'admin', sysdate(), '', NULL, '');

-- 给admin角色分配所有材料管理权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE menu_name LIKE '%材料%' OR menu_name LIKE '%参数%' OR menu_name LIKE '%方案%' OR menu_name LIKE '%结果%' OR menu_name LIKE '%分析%';