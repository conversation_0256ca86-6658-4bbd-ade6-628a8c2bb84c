<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('数据刷新规则配置列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>产品名称：</label>
                                <input type="text" name="productName"/>
                            </li>
                            <li>
                                <label>过程名称：</label>
                                <input type="text" name="processName"/>
                            </li>
                            <li>
                                <label>测试名称：</label>
                                <input type="text" name="testName"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="audit:ruleConfig:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="audit:ruleConfig:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="audit:ruleConfig:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="audit:ruleConfig:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="batchSet()" shiro:hasPermission="audit:ruleConfig:edit">
                    <i class="fa fa-cogs"></i> 批量设置
                </a>
                <a class="btn btn-secondary" onclick="getDefaultConfig()">
                    <i class="fa fa-file-o"></i> 默认配置
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('audit:ruleConfig:edit')}]];
        var removeFlag = [[${@permission.hasPermi('audit:ruleConfig:remove')}]];
        var prefix = ctx + "audit/ruleConfig";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "数据刷新规则配置",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'productName',
                    title: '产品名称'
                },
                {
                    field: 'processName',
                    title: '过程名称'
                },
                {
                    field: 'testName',
                    title: '测试名称'
                },
                {
                    field: 'enableControlLimitAdjustment',
                    title: '控制线调整',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-success">启用</span>' : '<span class="label label-danger">禁用</span>';
                    }
                },
                {
                    field: 'enableMovingRangeAdjustment',
                    title: '移动极差调整',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-success">启用</span>' : '<span class="label label-danger">禁用</span>';
                    }
                },
                {
                    field: 'enableNinePointSameSideCheck',
                    title: '9点同侧检查',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-success">启用</span>' : '<span class="label label-danger">禁用</span>';
                    }
                },
                {
                    field: 'enableSixPointTrendCheck',
                    title: '6点递变检查',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-success">启用</span>' : '<span class="label label-danger">禁用</span>';
                    }
                },
                {
                    field: 'enableCpkAdjustment',
                    title: 'CPK调整',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-success">启用</span>' : '<span class="label label-danger">禁用</span>';
                    }
                },
                {
                    field: 'cpkTarget',
                    title: 'CPK目标值'
                },
                {
                    field: 'isRefreshMode',
                    title: '刷新模式',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-primary">刷新模式</span>' : '<span class="label label-info">CPK模式</span>';
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'ruleDescription',
                    title: '规则描述'
                },
                {
                    field: 'createdTime',
                    title: '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="copyConfig(\'' + row.id + '\')"><i class="fa fa-copy"></i>复制</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        var statusDatas = [[${@dict.getType('sys_normal_disable')}]];

        // 批量设置
        function batchSet() {
            var layer_index = layer.open({
                type: 2,
                title: '批量设置规则配置',
                shadeClose: true,
                shade: false,
                maxmin: true,
                area: ['800px', '600px'],
                content: prefix + '/batchSet'
            });
        }

        // 获取默认配置
        function getDefaultConfig() {
            $.get(prefix + "/default", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var config = result.data;
                    var content = "默认规则配置：<br/>";
                    content += "控制线调整: " + (config.enableControlLimitAdjustment ? "启用" : "禁用") + "<br/>";
                    content += "移动极差调整: " + (config.enableMovingRangeAdjustment ? "启用" : "禁用") + "<br/>";
                    content += "9点同侧检查: " + (config.enableNinePointSameSideCheck ? "启用" : "禁用") + "<br/>";
                    content += "6点递变检查: " + (config.enableSixPointTrendCheck ? "启用" : "禁用") + "<br/>";
                    content += "CPK调整: " + (config.enableCpkAdjustment ? "启用" : "禁用") + "<br/>";
                    content += "CPK目标值: " + config.cpkTarget + "<br/>";
                    content += "刷新模式: " + (config.isRefreshMode ? "启用" : "禁用");
                    
                    layer.alert(content, {
                        title: '默认规则配置',
                        icon: 1
                    });
                } else {
                    $.modal.msgError(result.msg);
                }
            });
        }

        // 复制配置
        function copyConfig(id) {
            var layer_index = layer.open({
                type: 2,
                title: '复制规则配置',
                shadeClose: true,
                shade: false,
                maxmin: true,
                area: ['600px', '400px'],
                content: prefix + '/copy/' + id
            });
        }
    </script>
</body>
</html>
