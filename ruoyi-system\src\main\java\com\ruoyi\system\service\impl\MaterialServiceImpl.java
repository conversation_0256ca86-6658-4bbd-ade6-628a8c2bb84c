package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.CompleteExportData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.domain.ProcessParamItem;
import com.ruoyi.system.mapper.MaterialMapper;
import com.ruoyi.system.domain.Material;
import com.ruoyi.system.domain.ProcessParamGroup;
import com.ruoyi.system.service.IMaterialService;

/**
 * 材料信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MaterialServiceImpl implements IMaterialService 
{
    @Autowired
    private MaterialMapper materialMapper;

    /**
     * 查询材料信息
     * 
     * @param materialId 材料信息主键
     * @return 材料信息
     */
    @Override
    public Material selectMaterialByMaterialId(Long materialId)
    {
        return materialMapper.selectMaterialByMaterialId(materialId);
    }

    /**
     * 查询材料信息列表
     * 
     * @param material 材料信息
     * @return 材料信息
     */
    @Override
    public List<Material> selectMaterialList(Material material)
    {
        return materialMapper.selectMaterialList(material);
    }

    /**
     * 新增材料信息
     * 
     * @param material 材料信息
     * @return 结果
     */
    @Transactional
    @Override
    public int insertMaterial(Material material)
    {
        material.setCreateTime(DateUtils.getNowDate());
        int rows = materialMapper.insertMaterial(material);
        insertProcessParamGroup(material);
        return rows;
    }

    /**
     * 修改材料信息
     * 
     * @param material 材料信息
     * @return 结果
     */
    @Transactional
    @Override
    public int updateMaterial(Material material)
    {
        material.setUpdateTime(DateUtils.getNowDate());
        materialMapper.deleteProcessParamGroupByMaterialId(material.getMaterialId());
        insertProcessParamGroup(material);
        return materialMapper.updateMaterial(material);
    }

    /**
     * 批量删除材料信息
     * 
     * @param materialIds 需要删除的材料信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteMaterialByMaterialIds(Long[] materialIds)
    {
        materialMapper.deleteProcessParamGroupByMaterialIds(materialIds);
        return materialMapper.deleteMaterialByMaterialIds(materialIds);
    }

    /**
     * 删除材料信息信息
     * 
     * @param materialId 材料信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteMaterialByMaterialId(Long materialId)
    {
        materialMapper.deleteProcessParamGroupByMaterialId(materialId);
        return materialMapper.deleteMaterialByMaterialId(materialId);
    }

    /**
     * 新增工艺参数组信息
     * 
     * @param material 材料信息对象
     */
    public void insertProcessParamGroup(Material material)
    {
        List<ProcessParamGroup> processParamGroupList = material.getProcessParamGroupList();
        Long materialId = material.getMaterialId();
        if (StringUtils.isNotNull(processParamGroupList))
        {
            List<ProcessParamGroup> list = new ArrayList<ProcessParamGroup>();
            for (ProcessParamGroup processParamGroup : processParamGroupList)
            {
                processParamGroup.setMaterialId(materialId);
                list.add(processParamGroup);
            }
            if (list.size() > 0)
            {
                materialMapper.batchProcessParamGroup(list);
            }
        }
    }
    
    /**
     * 获取材料名称选项
     *
     * @return 材料名称列表
     */
    @Override
    public List<String> selectMaterialNameOptions()
    {
        return materialMapper.selectMaterialNameOptions();
    }
    
    /**
     * 获取供应商名称选项
     *
     * @return 供应商名称列表
     */
    @Override
    public List<String> selectSupplierNameOptions()
    {
        return materialMapper.selectSupplierNameOptions();
    }

    /**
     * 获取材料选项数据
     * 
     * @param material 材料
     * @return 选项列表
     */
    @Override
    public List<String> selectMaterialOptions(Material material)
    {
        return materialMapper.selectMaterialOptions(material);
    }

    /**
    /**
     * 获取材料选项数据
     * 
     * @param type 选项类型
     * @return 选项列表
     */
    @Override
    public List<String> selectMaterialOptions(String type)
    {
        if ("materialName".equals(type)) {
            return materialMapper.selectMaterialNameOptions();
        } else if ("supplierName".equals(type)) {
            return materialMapper.selectSupplierNameOptions();
        } else if ("materialModel".equals(type)) {
            return materialMapper.selectMaterialModelOptions();
        }
        return new ArrayList<>();
    }

    /**
     * 获取完整导出数据（材料+参数组+参数明细）
     *
     * @param material 材料查询条件
     * @return 完整数据列表
     */
    @Override
    public List<Map<String, Object>> selectCompleteExportData(Material material)
    {
        return materialMapper.selectCompleteExportData(material);
    }

    /**
     * 整体导出数据
     *
     * @param response HTTP响应
     * @param material 材料查询条件
     */
    @Override
    public void exportCompleteData(javax.servlet.http.HttpServletResponse response, Material material)
    {
        List<Map<String, Object>> list = this.selectCompleteExportData(material);

        // 创建完整导出数据类
        List<CompleteExportData> exportList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            CompleteExportData data = new CompleteExportData();
            data.setMaterialName(String.valueOf(map.get("materialName")));
            data.setSupplierName(String.valueOf(map.get("supplierName")));
            data.setMaterialModel(String.valueOf(map.get("materialModel")));
            data.setMaterialDescription(String.valueOf(map.get("materialDescription")));
            data.setProcessType(String.valueOf(map.get("processType")));
            data.setParamNumber(String.valueOf(map.get("paramNumber")));
            data.setParamName(String.valueOf(map.get("paramName")));
            data.setParamValue(map.get("paramValue") != null ? String.valueOf(map.get("paramValue")) : "");
            data.setUnit(String.valueOf(map.get("unit")));
            exportList.add(data);
        }

        com.ruoyi.common.utils.poi.ExcelUtil<CompleteExportData> util = new com.ruoyi.common.utils.poi.ExcelUtil<>(CompleteExportData.class);
        util.exportExcel(response, exportList, "完整材料数据");
    }
}
