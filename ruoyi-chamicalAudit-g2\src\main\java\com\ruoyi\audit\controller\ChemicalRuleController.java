package com.ruoyi.audit.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.audit.domain.ChemicalRule;
import com.ruoyi.audit.service.IChemicalRuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 化学处理规则Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/chemical/rule")
public class ChemicalRuleController extends BaseController
{
    @Autowired
    private IChemicalRuleService chemicalRuleService;

    /**
     * 查询化学处理规则列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:view')")
    @GetMapping("/list")
    public TableDataInfo list(ChemicalRule chemicalRule)
    {
        startPage();
        List<ChemicalRule> list = chemicalRuleService.selectChemicalRuleList(chemicalRule);
        return getDataTable(list);
    }

    /**
     * 导出化学处理规则列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:export')")
    @Log(title = "化学处理规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChemicalRule chemicalRule)
    {
        List<ChemicalRule> list = chemicalRuleService.selectChemicalRuleList(chemicalRule);
        ExcelUtil<ChemicalRule> util = new ExcelUtil<ChemicalRule>(ChemicalRule.class);
        util.exportExcel(response, list, "化学处理规则");
    }

    /**
     * 获取化学处理规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:query')")
    @GetMapping(value = "/{ruleId}")
    public AjaxResult getInfo(@PathVariable("ruleId") Long ruleId)
    {
        return success(chemicalRuleService.selectChemicalRuleByRuleId(ruleId));
    }

    /**
     * 新增化学处理规则
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:add')")
    @Log(title = "化学处理规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ChemicalRule chemicalRule)
    {
        return toAjax(chemicalRuleService.insertChemicalRule(chemicalRule));
    }

    /**
     * 修改化学处理规则
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:edit')")
    @Log(title = "化学处理规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ChemicalRule chemicalRule)
    {
        return toAjax(chemicalRuleService.updateChemicalRule(chemicalRule));
    }

    /**
     * 删除化学处理规则
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:remove')")
    @Log(title = "化学处理规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ruleIds}")
    public AjaxResult remove(@PathVariable Long[] ruleIds)
    {
        return toAjax(chemicalRuleService.deleteChemicalRuleByRuleIds(ruleIds));
    }

    /**
     * 根据产品、工艺、测试名称查询规则
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:query')")
    @GetMapping("/findByNames")
    public AjaxResult findRuleByNames(String productName, String processName, String testName)
    {
        ChemicalRule rule = chemicalRuleService.selectRuleByNames(productName, processName, testName);
        return success(rule);
    }

    /**
     * 检查规则是否存在
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:query')")
    @GetMapping("/exists")
    public AjaxResult checkRuleExists(String productName, String processName, String testName)
    {
        boolean exists = chemicalRuleService.existsRule(productName, processName, testName);
        return AjaxResult.success("规则检查完成").put("exists", exists);
    }

    /**
     * 查询启用的规则列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:view')")
    @GetMapping("/enabled")
    public AjaxResult getEnabledRules()
    {
        List<ChemicalRule> list = chemicalRuleService.selectEnabledRuleList();
        return success(list);
    }

    /**
     * 查询刷新规则列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:view')")
    @GetMapping("/refresh")
    public AjaxResult getRefreshRules()
    {
        List<ChemicalRule> list = chemicalRuleService.selectRefreshRuleList();
        return success(list);
    }

    /**
     * 查询特殊规则列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:view')")
    @GetMapping("/special")
    public AjaxResult getSpecialRules()
    {
        List<ChemicalRule> list = chemicalRuleService.selectSpecialRuleList();
        return success(list);
    }

    /**
     * 批量启用规则
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:edit')")
    @Log(title = "化学处理规则", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public AjaxResult enableRules(@RequestBody Long[] ruleIds)
    {
        int result = chemicalRuleService.enableRules(ruleIds);
        return toAjax(result);
    }

    /**
     * 批量禁用规则
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:edit')")
    @Log(title = "化学处理规则", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public AjaxResult disableRules(@RequestBody Long[] ruleIds)
    {
        int result = chemicalRuleService.disableRules(ruleIds);
        return toAjax(result);
    }

    /**
     * 复制规则
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:add')")
    @Log(title = "化学处理规则", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{ruleId}")
    public AjaxResult copyRule(@PathVariable Long ruleId)
    {
        int result = chemicalRuleService.copyRule(ruleId);
        return toAjax(result);
    }

    /**
     * 导入规则
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:add')")
    @Log(title = "化学处理规则", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importRules(@RequestBody List<ChemicalRule> rules)
    {
        int result = chemicalRuleService.importRules(rules);
        return result > 0 ? success("导入成功，导入数量：" + result) : error("导入失败");
    }

    /**
     * 获取规则统计信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:rule:view')")
    @GetMapping("/statistics")
    public AjaxResult getRuleStatistics()
    {
        return success(chemicalRuleService.getRuleStatistics());
    }
}
