# 化学审计系统整合完成文档

## 项目概述

本项目成功将原有的JavaFX化学审计系统（chemical和chemical-refresh模块）整合到若依框架的Web系统中，实现了完整的化学数据审计功能。

## 整合内容

### 1. 核心功能整合

#### 原chemical模块功能
- ✅ Pulsar消息队列数据接收
- ✅ 化学检测数据处理和验证
- ✅ 实时任务控制（启动、暂停、停止）
- ✅ 系统监控和日志记录

#### 原chemical-refresh模块功能
- ✅ 数据刷新算法实现
- ✅ 本地和云端数据库对比
- ✅ 批量数据调整和更新
- ✅ CSV/Excel数据导出

### 2. 新增Web功能
- ✅ 响应式Web界面
- ✅ 数据可视化展示
- ✅ 批量操作支持
- ✅ 任务进度监控
- ✅ 历史记录管理

## 技术架构

### 后端架构
```
ruoyi-chamicalAudit-g2/
├── src/main/java/com/ruoyi/audit/
│   ├── controller/          # 控制器层
│   │   ├── ChemicalController.java
│   │   ├── ChemicalTaskController.java
│   │   ├── ChemicalRefreshController.java
│   │   ├── ChemicalExportController.java
│   │   └── ChemicalMonitorController.java
│   ├── service/             # 服务层
│   │   ├── IChemicalService.java
│   │   ├── IChemicalTaskService.java
│   │   ├── IChemicalRefreshService.java
│   │   └── IChemicalExportService.java
│   ├── service/impl/        # 服务实现层
│   │   ├── ChemicalServiceImpl.java
│   │   ├── ChemicalRefreshServiceImpl.java
│   │   └── ChemicalExportServiceImpl.java
│   ├── domain/              # 实体类
│   │   ├── Chemical.java
│   │   ├── ChemicalYs.java
│   │   ├── ChemicalRefreshTask.java
│   │   └── ChemicalRule.java
│   └── mapper/              # 数据访问层
│       ├── ChemicalMapper.java
│       ├── ChemicalRefreshTaskMapper.java
│       └── ChemicalYsMapper.java
```

### 前端架构
```
ruoyi-ui/src/views/chemical/
├── data/                    # 数据管理
│   └── chemical/index.vue   # 化学数据列表页面
├── monitor/                 # 监控页面
│   └── index.vue           # 系统监控页面
├── task/                   # 任务管理
│   └── index.vue           # 任务控制页面
└── refresh/                # 数据刷新
    └── index.vue           # 数据刷新管理页面
```

## 数据库设计

### 核心表结构

#### 1. chemical - 化学数据主表
```sql
CREATE TABLE chemical (
    id VARCHAR(50) PRIMARY KEY,
    organization_id BIGINT,
    attribute_id BIGINT,
    examine_date DATETIME,
    shift VARCHAR(20),
    staff VARCHAR(100),
    department_code VARCHAR(50),
    process_name VARCHAR(100),
    product_name VARCHAR(100),
    test_name VARCHAR(100),
    layer_number VARCHAR(50),
    upper_limit VARCHAR(20),
    median_specification VARCHAR(20),
    down_limit VARCHAR(20),
    examine1 VARCHAR(20),
    examine1_ys VARCHAR(20),
    examine2 VARCHAR(20),
    examine1_modified BIT DEFAULT 0,
    original_examine1 VARCHAR(20),
    -- 其他字段...
    create_time DATETIME DEFAULT GETDATE(),
    update_time DATETIME DEFAULT GETDATE()
);
```

#### 2. chemical_refresh_task - 数据刷新任务表
```sql
CREATE TABLE chemical_refresh_task (
    refresh_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL,
    start_time DATETIME,
    end_time DATETIME,
    total_groups INT DEFAULT 0,
    modified_records INT DEFAULT 0,
    duration_ms BIGINT DEFAULT 0,
    task_status VARCHAR(20) DEFAULT 'RUNNING',
    error_message TEXT,
    export_file_path VARCHAR(500),
    start_date DATETIME,
    end_date DATETIME,
    layer_numbers VARCHAR(200),
    create_time DATETIME DEFAULT GETDATE()
);
```

## API接口文档

### 1. 化学数据管理接口

#### 查询化学数据列表
- **URL**: `GET /chemical/data/list`
- **参数**: 
  - `pageNum`: 页码
  - `pageSize`: 页大小
  - `examineDate`: 检测日期
  - `processName`: 工艺名称
  - `productName`: 产品名称
  - `layerNumber`: 层号
- **返回**: 分页数据列表

#### 批量操作
- **URL**: `POST /chemical/data/batch`
- **参数**: 
  - `ids`: ID数组
  - `operation`: 操作类型（markExported/markNotProcess/resetStatus）

### 2. 数据刷新接口

#### 执行数据刷新
- **URL**: `POST /chemical/refresh/execute`
- **参数**:
  - `startDate`: 开始时间
  - `endDate`: 结束时间
  - `layerNumbers`: 层号数组
  - `exportPath`: 导出路径
- **返回**: 任务执行结果

#### 获取刷新状态
- **URL**: `GET /chemical/refresh/status`
- **返回**: 当前刷新任务状态

### 3. 任务控制接口

#### 启动数据读取任务
- **URL**: `POST /chemical/task/start`
- **返回**: 任务启动结果

#### 获取任务状态
- **URL**: `GET /chemical/task/status`
- **返回**: 当前任务状态信息

### 4. 数据导出接口

#### 导出CSV文件
- **URL**: `POST /chemical/export/csv`
- **参数**:
  - `startDate`: 开始时间
  - `endDate`: 结束时间
  - `layerNumbers`: 层号数组
- **返回**: 导出文件信息

#### 导出Excel文件
- **URL**: `POST /chemical/export/excel`
- **参数**: 同CSV导出
- **返回**: 导出文件信息

## 配置说明

### 1. 数据库配置
```yaml
# application.yml
chemical:
  database:
    local:
      url: *****************************************************************
      username: sa
      password: your_password
    cloud:
      url: ******************************************************************************************
      username: sa
      password: your_password
```

### 2. 导出配置
```yaml
chemical:
  export:
    default:
      path: E:\测试数据\应审抛转数据\G2
    max:
      records: 50000
```

### 3. Pulsar配置
```yaml
pulsar:
  service:
    url: pulsar://localhost:6650
  consumer:
    subscription: chemical-audit-subscription
    topic: chemical-data-topic
```

## 部署指南

### 1. 环境要求
- JDK 8+
- MySQL 5.7+ 或 SQL Server 2016+
- Node.js 14+
- Apache Pulsar 2.8+

### 2. 部署步骤

#### 后端部署
1. 编译项目
```bash
mvn clean package -DskipTests
```

2. 创建数据库表
```bash
# 执行SQL脚本
mysql -u root -p < sql/chemical_audit_tables.sql
```

3. 启动应用
```bash
java -jar ruoyi-admin.jar
```

#### 前端部署
1. 安装依赖
```bash
npm install
```

2. 构建生产版本
```bash
npm run build:prod
```

3. 部署到Web服务器
```bash
# 将dist目录内容部署到nginx或apache
```

### 3. 配置检查清单

- [ ] 数据库连接配置正确
- [ ] Pulsar服务正常运行
- [ ] 导出目录权限设置正确
- [ ] 防火墙端口开放
- [ ] 日志目录可写
- [ ] 系统时区设置正确

## 功能验证

### 1. 数据接收验证
1. 启动Pulsar消费者
2. 发送测试消息
3. 检查数据库记录
4. 验证日志输出

### 2. 数据刷新验证
1. 准备测试数据
2. 执行数据刷新任务
3. 检查数据调整结果
4. 验证导出文件

### 3. Web界面验证
1. 登录系统
2. 访问化学审计模块
3. 测试各项功能
4. 检查响应性能

## 注意事项

### 1. 性能优化
- 大数据量查询使用分页
- 导出操作异步处理
- 定期清理历史数据
- 数据库索引优化

### 2. 安全考虑
- API接口权限控制
- 数据库连接加密
- 文件路径验证
- 输入参数校验

### 3. 监控告警
- 任务执行状态监控
- 数据库连接监控
- 磁盘空间监控
- 错误日志告警

## 故障排除

### 常见问题

1. **Pulsar连接失败**
   - 检查Pulsar服务状态
   - 验证网络连接
   - 确认配置参数

2. **数据库连接超时**
   - 检查数据库服务
   - 验证连接字符串
   - 调整超时参数

3. **文件导出失败**
   - 检查目录权限
   - 验证磁盘空间
   - 确认路径存在

4. **前端页面空白**
   - 检查API接口
   - 验证权限配置
   - 查看浏览器控制台

## 联系信息

- 开发团队：化学审计系统开发组
- 技术支持：请提交Issue到项目仓库
- 文档更新：2024年1月1日
