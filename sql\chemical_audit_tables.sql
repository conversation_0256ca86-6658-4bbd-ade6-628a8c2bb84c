-- =============================================
-- 化学审计系统数据库表结构设计
-- 基于若依框架标准，整合chemical和chemical-refresh功能
-- =============================================

-- 1. 化学数据主表 (chemical)
-- 存储从Pulsar消息队列接收的原始化学检测数据
DROP TABLE IF EXISTS chemical;
CREATE TABLE chemical (
    id VARCHAR(50) PRIMARY KEY,                    -- 主键ID
    organization_id BIGINT,                        -- 组织ID
    attribute_id BIGINT,                           -- 属性ID
    examine_date DATETIME,                         -- 检测日期
    shift VARCHAR(20),                             -- 班次
    staff VARCHAR(100),                            -- 操作员工
    department_code VARCHAR(50),                   -- 部门代码
    process_set_name VARCHAR(100),                 -- 工艺集名称
    process_name VARCHAR(100),                     -- 工艺名称
    product_set_name VARCHAR(100),                 -- 产品集名称
    product_name VARCHAR(100),                     -- 产品名称
    test_set_name VARCHAR(100),                    -- 测试集名称
    test_name VARCHAR(100),                        -- 测试名称
    sample_size VARCHAR(20),                       -- 样本大小
    layer_number VARCHAR(50),                      -- 层号
    upper_limit VARCHAR(20),                       -- 上限
    median_specification VARCHAR(20),              -- 中位规格
    down_limit VARCHAR(20),                        -- 下限
    examine1 VARCHAR(20),                          -- 检测值1
    examine1_ys VARCHAR(20),                       -- 检测值1应审
    examine2 VARCHAR(20),                          -- 检测值2
    created_by VARCHAR(100),                       -- 创建人
    last_updated_by VARCHAR(100),                  -- 最后更新人
    creation_date DATETIME,                        -- 创建时间
    last_update_date DATETIME,                     -- 最后更新时间
    status BIGINT DEFAULT 1,                       -- 状态 (1:正常 0:停用)
    frequency VARCHAR(50),                         -- 频率
    frequency_unit VARCHAR(20),                    -- 频率单位
    slot_body_name VARCHAR(100),                   -- 槽体名称
    project_team_code VARCHAR(50),                 -- 项目组代码
    project_team_name VARCHAR(100),                -- 项目组名称
    test_code VARCHAR(50),                         -- 测试代码
    adjustment_upper_limit VARCHAR(20),            -- 调整上限
    adjustment_mid VARCHAR(20),                    -- 调整中值
    adjustment_lower_limit VARCHAR(20),            -- 调整下限
    project_unit VARCHAR(20),                      -- 项目单位
    insertion_time DATETIME DEFAULT GETDATE(),     -- 插入时间
    is_exported BIT DEFAULT 0,                     -- 是否已导出
    not_process BIT DEFAULT 0,                     -- 是否不处理
    warning_upper_limit VARCHAR(20),               -- 警告上限
    warning_mid VARCHAR(20),                       -- 警告中值
    warning_lower_limit VARCHAR(20),               -- 警告下限
    attribute1 VARCHAR(200),                       -- 扩展属性1
    attribute2 VARCHAR(200),                       -- 扩展属性2
    attribute3 VARCHAR(200),                       -- 扩展属性3
    examine1_modified BIT DEFAULT 0,               -- 检测值1是否被修改
    original_examine1 VARCHAR(20),                 -- 原始检测值1
    remark VARCHAR(500),                           -- 备注
    create_by VARCHAR(100),                        -- 创建人(若依标准)
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间(若依标准)
    update_by VARCHAR(100),                        -- 更新人(若依标准)
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间(若依标准)
);

-- 创建索引
CREATE INDEX idx_chemical_examine_date ON chemical(examine_date);
CREATE INDEX idx_chemical_department_code ON chemical(department_code);
CREATE INDEX idx_chemical_product_name ON chemical(product_name);
CREATE INDEX idx_chemical_test_name ON chemical(test_name);
CREATE INDEX idx_chemical_is_exported ON chemical(is_exported);
CREATE INDEX idx_chemical_not_process ON chemical(not_process);

-- 2. 化学应审数据表 (chemical_ys)
-- 存储经过处理和调整后的化学检测数据
DROP TABLE IF EXISTS chemical_ys;
CREATE TABLE chemical_ys (
    id VARCHAR(50) PRIMARY KEY,                    -- 主键ID (关联chemical表)
    organization_id BIGINT,                        -- 组织ID
    attribute_id BIGINT,                           -- 属性ID
    examine_date DATETIME,                         -- 检测日期
    shift VARCHAR(20),                             -- 班次
    staff VARCHAR(100),                            -- 操作员工
    process_set_name VARCHAR(50),                  -- 工艺集名称
    process_name VARCHAR(100),                     -- 工艺名称
    product_set_name VARCHAR(100),                 -- 产品集名称
    product_name VARCHAR(100),                     -- 产品名称
    test_set_name VARCHAR(100),                    -- 测试集名称
    test_name VARCHAR(100),                        -- 测试名称
    sample_size VARCHAR(20),                       -- 样本大小
    layer_number VARCHAR(50),                      -- 层号
    upper_limit VARCHAR(20),                       -- 上限
    median_specification VARCHAR(20),              -- 中位规格
    down_limit VARCHAR(20),                        -- 下限
    examine1 VARCHAR(20),                          -- 调整后检测值1
    examine2 VARCHAR(20),                          -- 调整后检测值2
    examine1_zs VARCHAR(20),                       -- 检测值1真实值
    created_by VARCHAR(100),                       -- 创建人
    last_updated_by VARCHAR(100),                  -- 最后更新人
    creation_date DATETIME,                        -- 创建时间
    last_update_date DATETIME,                     -- 最后更新时间
    status BIGINT DEFAULT 1,                       -- 状态
    frequency VARCHAR(50),                         -- 频率
    frequency_unit VARCHAR(20),                    -- 频率单位
    slot_body_name VARCHAR(100),                   -- 槽体名称
    project_team_code VARCHAR(50),                 -- 项目组代码
    project_team_name VARCHAR(100),                -- 项目组名称
    test_code VARCHAR(50),                         -- 测试代码
    adjustment_upper_limit VARCHAR(20),            -- 调整上限
    adjustment_mid VARCHAR(20),                    -- 调整中值
    adjustment_lower_limit VARCHAR(20),            -- 调整下限
    project_unit VARCHAR(20),                      -- 项目单位
    insertion_time DATETIME DEFAULT GETDATE(),     -- 插入时间
    warning_upper_limit VARCHAR(20),               -- 警告上限
    warning_mid VARCHAR(20),                       -- 警告中值
    warning_lower_limit VARCHAR(20),               -- 警告下限
    is_modified BIT DEFAULT 0,                     -- 是否被修改过
    original_examine1 VARCHAR(20),                 -- 原始检测值1
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间(若依标准)
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间(若依标准)
);

-- 创建索引
CREATE INDEX idx_chemical_ys_examine_date ON chemical_ys(examine_date);
CREATE INDEX idx_chemical_ys_process_name ON chemical_ys(process_name);
CREATE INDEX idx_chemical_ys_product_name ON chemical_ys(product_name);
CREATE INDEX idx_chemical_ys_test_name ON chemical_ys(test_name);
CREATE INDEX idx_chemical_ys_layer_number ON chemical_ys(layer_number);

-- 3. 化学数据日志表 (chemical_log)
-- 记录数据变更日志，用于追踪数据处理过程
DROP TABLE IF EXISTS chemical_log;
CREATE TABLE chemical_log (
    log_id BIGINT IDENTITY(1,1) PRIMARY KEY,       -- 日志ID
    chemical_id VARCHAR(50) NOT NULL,              -- 关联chemical表ID
    operation_type VARCHAR(20) NOT NULL,           -- 操作类型 (INSERT/UPDATE/DELETE/PROCESS)
    operation_desc VARCHAR(200),                   -- 操作描述
    old_value TEXT,                                -- 变更前值
    new_value TEXT,                                -- 变更后值
    processed BIT DEFAULT 0,                       -- 是否已处理
    error_message VARCHAR(500),                    -- 错误信息
    create_by VARCHAR(100),                        -- 操作人
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间
);

-- 创建索引
CREATE INDEX idx_chemical_log_chemical_id ON chemical_log(chemical_id);
CREATE INDEX idx_chemical_log_processed ON chemical_log(processed);
CREATE INDEX idx_chemical_log_create_time ON chemical_log(create_time);

-- 4. 处理规则表 (chemical_rule)
-- 存储特殊的数据处理规则
DROP TABLE IF EXISTS chemical_rule;
CREATE TABLE chemical_rule (
    rule_id BIGINT IDENTITY(1,1) PRIMARY KEY,      -- 规则ID
    product_name VARCHAR(100) NOT NULL,            -- 产品名称
    process_name VARCHAR(100) NOT NULL,            -- 工艺名称
    test_name VARCHAR(100) NOT NULL,               -- 测试名称
    is_refresh BIT DEFAULT 0,                      -- 是否刷新
    rule_type VARCHAR(20) DEFAULT 'NORMAL',        -- 规则类型 (NORMAL/SPECIAL)
    rule_desc VARCHAR(200),                        -- 规则描述
    rule_config TEXT,                              -- 规则配置(JSON格式)
    status CHAR(1) DEFAULT '0',                    -- 状态 (0:正常 1:停用)
    create_by VARCHAR(100),                        -- 创建人
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_by VARCHAR(100),                        -- 更新人
    update_time DATETIME DEFAULT GETDATE(),        -- 更新时间
    remark VARCHAR(500)                            -- 备注
);

-- 创建唯一索引
CREATE UNIQUE INDEX uk_chemical_rule ON chemical_rule(product_name, process_name, test_name);
CREATE INDEX idx_chemical_rule_status ON chemical_rule(status);

-- 5. 部门信息表 (chemical_department)
-- 存储部门信息，关联department_code
DROP TABLE IF EXISTS chemical_department;
CREATE TABLE chemical_department (
    dept_id BIGINT IDENTITY(1,1) PRIMARY KEY,      -- 部门ID
    department_code VARCHAR(50) NOT NULL UNIQUE,   -- 部门代码
    department_name VARCHAR(100) NOT NULL,         -- 部门名称
    parent_id BIGINT DEFAULT 0,                    -- 父部门ID
    ancestors VARCHAR(500) DEFAULT '',             -- 祖级列表
    order_num INT DEFAULT 0,                       -- 显示顺序
    leader VARCHAR(100),                           -- 负责人
    phone VARCHAR(20),                             -- 联系电话
    email VARCHAR(100),                            -- 邮箱
    status CHAR(1) DEFAULT '0',                    -- 状态 (0:正常 1:停用)
    del_flag CHAR(1) DEFAULT '0',                  -- 删除标志 (0:存在 2:删除)
    create_by VARCHAR(100),                        -- 创建人
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_by VARCHAR(100),                        -- 更新人
    update_time DATETIME DEFAULT GETDATE(),        -- 更新时间
    remark VARCHAR(500)                            -- 备注
);

-- 创建索引
CREATE INDEX idx_chemical_dept_code ON chemical_department(department_code);
CREATE INDEX idx_chemical_dept_status ON chemical_department(status);

-- 6. 任务监控表 (chemical_task_monitor)
-- 记录任务执行状态和监控信息
DROP TABLE IF EXISTS chemical_task_monitor;
CREATE TABLE chemical_task_monitor (
    task_id BIGINT IDENTITY(1,1) PRIMARY KEY,      -- 任务ID
    task_name VARCHAR(100) NOT NULL,               -- 任务名称
    task_type VARCHAR(20) NOT NULL,                -- 任务类型 (DATA_READ/LOG_READ/DATA_PROCESS/DATA_EXPORT/DATA_REFRESH)
    task_status VARCHAR(20) DEFAULT 'WAITING',     -- 任务状态 (WAITING/RUNNING/PAUSED/STOPPED/COMPLETED/ERROR)
    start_time DATETIME,                           -- 开始时间
    end_time DATETIME,                             -- 结束时间
    total_records INT DEFAULT 0,                   -- 总记录数
    processed_records INT DEFAULT 0,               -- 已处理记录数
    success_records INT DEFAULT 0,                 -- 成功记录数
    error_records INT DEFAULT 0,                   -- 错误记录数
    progress_percent DECIMAL(5,2) DEFAULT 0,       -- 进度百分比
    error_message TEXT,                            -- 错误信息
    task_config TEXT,                              -- 任务配置(JSON格式)
    create_by VARCHAR(100),                        -- 创建人
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间
);

-- 创建索引
CREATE INDEX idx_task_monitor_status ON chemical_task_monitor(task_status);
CREATE INDEX idx_task_monitor_type ON chemical_task_monitor(task_type);
CREATE INDEX idx_task_monitor_create_time ON chemical_task_monitor(create_time);

-- 7. 系统配置表 (chemical_config)
-- 存储系统配置参数
DROP TABLE IF EXISTS chemical_config;
CREATE TABLE chemical_config (
    config_id BIGINT IDENTITY(1,1) PRIMARY KEY,    -- 配置ID
    config_name VARCHAR(100) NOT NULL UNIQUE,      -- 配置名称
    config_key VARCHAR(100) NOT NULL UNIQUE,       -- 配置键
    config_value TEXT,                             -- 配置值
    config_type VARCHAR(20) DEFAULT 'STRING',      -- 配置类型 (STRING/NUMBER/BOOLEAN/JSON)
    config_desc VARCHAR(200),                      -- 配置描述
    is_system CHAR(1) DEFAULT 'N',                 -- 是否系统内置 (Y:是 N:否)
    status CHAR(1) DEFAULT '0',                    -- 状态 (0:正常 1:停用)
    create_by VARCHAR(100),                        -- 创建人
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_by VARCHAR(100),                        -- 更新人
    update_time DATETIME DEFAULT GETDATE(),        -- 更新时间
    remark VARCHAR(500)                            -- 备注
);

-- 创建索引
CREATE INDEX idx_chemical_config_key ON chemical_config(config_key);
CREATE INDEX idx_chemical_config_status ON chemical_config(status);

-- 8. 数据导出记录表 (chemical_export_record)
-- 记录数据导出历史
DROP TABLE IF EXISTS chemical_export_record;
CREATE TABLE chemical_export_record (
    export_id BIGINT IDENTITY(1,1) PRIMARY KEY,    -- 导出ID
    export_name VARCHAR(200),                      -- 导出名称
    export_type VARCHAR(20) NOT NULL,              -- 导出类型 (CSV/EXCEL)
    file_name VARCHAR(200),                        -- 文件名
    file_path VARCHAR(500),                        -- 文件路径
    file_size BIGINT DEFAULT 0,                    -- 文件大小(字节)
    export_condition TEXT,                         -- 导出条件(JSON格式)
    start_date DATETIME,                           -- 数据开始时间
    end_date DATETIME,                             -- 数据结束时间
    total_records INT DEFAULT 0,                   -- 导出记录数
    export_status VARCHAR(20) DEFAULT 'PROCESSING', -- 导出状态 (PROCESSING/SUCCESS/FAILED)
    error_message VARCHAR(500),                    -- 错误信息
    create_by VARCHAR(100),                        -- 创建人
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间
);

-- 创建索引
CREATE INDEX idx_export_record_status ON chemical_export_record(export_status);
CREATE INDEX idx_export_record_type ON chemical_export_record(export_type);
CREATE INDEX idx_export_record_create_time ON chemical_export_record(create_time);

-- 9. 初始化基础数据
-- 插入默认配置数据
INSERT INTO chemical_config (config_name, config_key, config_value, config_type, config_desc, is_system) VALUES
('Pulsar服务地址', 'pulsar.broker.url', 'pulsar://pulsar.scc.com:6650', 'STRING', 'Pulsar消息队列服务地址', 'Y'),
('Pulsar认证Token', 'pulsar.auth.token', 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzcGMtMzMxIn0.U7QrwJTShmbcucMtIqqzC5gfK8mbgsSAF20yyed1W6A', 'STRING', 'Pulsar认证令牌', 'Y'),
('Pulsar主题名称', 'pulsar.topic.medicine', 'persistent://spc/331-interface/labMedicineData', 'STRING', '药水数据主题', 'Y'),
('Pulsar订阅名称', 'pulsar.subscription.name', 'G2-Chemical-Web', 'STRING', '订阅名称', 'Y'),
('本地数据库连接', 'database.local.url', '*****************************************************************', 'STRING', '本地数据库连接字符串', 'Y'),
('云数据库连接', 'database.cloud.url', '************************************************************************************************************', 'STRING', '云数据库连接字符串', 'Y'),
('数据导出路径', 'export.file.path', 'E:\\测试数据\\应审抛转数据\\G2', 'STRING', '数据导出文件路径', 'N'),
('数据备份路径', 'export.backup.path', '', 'STRING', '数据备份文件路径', 'N'),
('任务执行间隔', 'task.schedule.interval', '30', 'NUMBER', '定时任务执行间隔(分钟)', 'N'),
('数据处理批次大小', 'data.process.batch.size', '100', 'NUMBER', '数据处理批次大小', 'N');

-- 插入默认部门数据
INSERT INTO chemical_department (department_code, department_name, order_num, status, create_by) VALUES
('G2-001', 'G2生产部', 1, '0', 'admin'),
('G2-002', 'G2质检部', 2, '0', 'admin'),
('G2-003', 'G2工艺部', 3, '0', 'admin'),
('G2-004', 'G2设备部', 4, '0', 'admin');

-- 9. 数据刷新任务表 (chemical_refresh_task)
-- 记录数据刷新任务的执行情况
DROP TABLE IF EXISTS chemical_refresh_task;
CREATE TABLE chemical_refresh_task (
    refresh_id BIGINT IDENTITY(1,1) PRIMARY KEY,   -- 刷新任务ID
    task_name VARCHAR(100) NOT NULL,               -- 任务名称
    start_time DATETIME,                           -- 开始时间
    end_time DATETIME,                             -- 结束时间
    total_groups INT DEFAULT 0,                    -- 处理项目组数
    modified_records INT DEFAULT 0,                -- 修改记录数
    duration_ms BIGINT DEFAULT 0,                  -- 耗时(毫秒)
    task_status VARCHAR(20) DEFAULT 'RUNNING',     -- 任务状态 (RUNNING/COMPLETED/FAILED)
    error_message TEXT,                            -- 错误信息
    export_file_path VARCHAR(500),                 -- 导出文件路径
    export_backup_path VARCHAR(500),               -- 备份文件路径
    start_date DATETIME,                           -- 数据开始时间
    end_date DATETIME,                             -- 数据结束时间
    layer_numbers VARCHAR(200),                    -- 层号列表(逗号分隔)
    create_by VARCHAR(100),                        -- 创建人
    create_time DATETIME DEFAULT GETDATE(),        -- 创建时间
    update_time DATETIME DEFAULT GETDATE()         -- 更新时间
);

-- 创建索引
CREATE INDEX idx_refresh_task_status ON chemical_refresh_task(task_status);
CREATE INDEX idx_refresh_task_create_time ON chemical_refresh_task(create_time);

-- 插入默认规则数据
INSERT INTO chemical_rule (product_name, process_name, test_name, is_refresh, rule_type, rule_desc, create_by) VALUES
('LV蓬松膨胀剂', '特殊工艺', 'LV蓬松膨胀剂E', 1, 'SPECIAL', 'LV蓬松膨胀剂特殊处理规则', 'admin'),
('PD全线', '微蚀', 'PD全线微蚀量', 0, 'NORMAL', 'PD全线微蚀量标准处理', 'admin'),
('TR微蚀', '微蚀', 'TR微蚀微蚀量', 0, 'NORMAL', 'TR微蚀标准处理', 'admin'),
('TROSPOSP', '膜厚', 'TROSPOSP膜厚', 0, 'NORMAL', 'TROSPOSP膜厚标准处理', 'admin');
