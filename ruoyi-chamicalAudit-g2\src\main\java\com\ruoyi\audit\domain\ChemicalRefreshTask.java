package com.ruoyi.audit.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 化学数据刷新任务对象 chemical_refresh_task
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ChemicalRefreshTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 刷新任务ID */
    private Long refreshId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 处理项目组数 */
    @Excel(name = "处理项目组数")
    private Integer totalGroups;

    /** 修改记录数 */
    @Excel(name = "修改记录数")
    private Integer modifiedRecords;

    /** 耗时(毫秒) */
    @Excel(name = "耗时(毫秒)")
    private Long durationMs;

    /** 任务状态 */
    @Excel(name = "任务状态", dictType = "chemical_task_status")
    private String taskStatus;

    /** 错误信息 */
    private String errorMessage;

    /** 导出文件路径 */
    @Excel(name = "导出文件路径")
    private String exportFilePath;

    /** 备份文件路径 */
    @Excel(name = "备份文件路径")
    private String exportBackupPath;

    /** 数据开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "数据开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 数据结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "数据结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /** 层号列表(逗号分隔) */
    @Excel(name = "层号列表")
    private String layerNumbers;

    public void setRefreshId(Long refreshId) 
    {
        this.refreshId = refreshId;
    }

    public Long getRefreshId() 
    {
        return refreshId;
    }

    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setTotalGroups(Integer totalGroups) 
    {
        this.totalGroups = totalGroups;
    }

    public Integer getTotalGroups() 
    {
        return totalGroups;
    }

    public void setModifiedRecords(Integer modifiedRecords) 
    {
        this.modifiedRecords = modifiedRecords;
    }

    public Integer getModifiedRecords() 
    {
        return modifiedRecords;
    }

    public void setDurationMs(Long durationMs) 
    {
        this.durationMs = durationMs;
    }

    public Long getDurationMs() 
    {
        return durationMs;
    }

    public void setTaskStatus(String taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public String getTaskStatus() 
    {
        return taskStatus;
    }

    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    public void setExportFilePath(String exportFilePath) 
    {
        this.exportFilePath = exportFilePath;
    }

    public String getExportFilePath() 
    {
        return exportFilePath;
    }

    public void setExportBackupPath(String exportBackupPath) 
    {
        this.exportBackupPath = exportBackupPath;
    }

    public String getExportBackupPath() 
    {
        return exportBackupPath;
    }

    public void setStartDate(Date startDate) 
    {
        this.startDate = startDate;
    }

    public Date getStartDate() 
    {
        return startDate;
    }

    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    public Date getEndDate() 
    {
        return endDate;
    }

    public void setLayerNumbers(String layerNumbers) 
    {
        this.layerNumbers = layerNumbers;
    }

    public String getLayerNumbers() 
    {
        return layerNumbers;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("refreshId", getRefreshId())
            .append("taskName", getTaskName())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("totalGroups", getTotalGroups())
            .append("modifiedRecords", getModifiedRecords())
            .append("durationMs", getDurationMs())
            .append("taskStatus", getTaskStatus())
            .append("errorMessage", getErrorMessage())
            .append("exportFilePath", getExportFilePath())
            .append("exportBackupPath", getExportBackupPath())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("layerNumbers", getLayerNumbers())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
