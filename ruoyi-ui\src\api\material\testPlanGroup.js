import request from '@/utils/request'

// 查询测试方案组列表
export function listTestPlanGroup(query) {
  return request({
    url: '/material/testPlanGroup/list',
    method: 'get',
    params: query
  })
}

// 查询测试方案组详细
export function getTestPlanGroup(planGroupId) {
  return request({
    url: '/material/testPlanGroup/' + planGroupId,
    method: 'get'
  })
}

// 新增测试方案组
export function addTestPlanGroup(data) {
  return request({
    url: '/material/testPlanGroup',
    method: 'post',
    data: data
  })
}

// 修改测试方案组
export function updateTestPlanGroup(data) {
  return request({
    url: '/material/testPlanGroup',
    method: 'put',
    data: data
  })
}

// 删除测试方案组
export function delTestPlanGroup(planGroupId) {
  return request({
    url: '/material/testPlanGroup/' + planGroupId,
    method: 'delete'
  })
}

// 导出测试方案组
export function exportTestPlanGroup(query) {
  return request({
    url: '/material/testPlanGroup/export',
    method: 'get',
    params: query
  })
}

// 获取测试方案组选项（用于下拉选择）
export function getTestPlanGroupOptions(query) {
  return request({
    url: '/material/testPlanGroup/options',
    method: 'get',
    params: query
  })
}
