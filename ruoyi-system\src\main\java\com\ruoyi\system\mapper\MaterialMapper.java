package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.Material;
import com.ruoyi.system.domain.ProcessParamGroup;

/**
 * 材料信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface MaterialMapper 
{
    /**
     * 查询材料信息
     * 
     * @param materialId 材料信息主键
     * @return 材料信息
     */
    public Material selectMaterialByMaterialId(Long materialId);

    /**
     * 查询材料信息列表
     * 
     * @param material 材料信息
     * @return 材料信息集合
     */
    public List<Material> selectMaterialList(Material material);

    /**
     * 新增材料信息
     * 
     * @param material 材料信息
     * @return 结果
     */
    public int insertMaterial(Material material);

    /**
     * 修改材料信息
     * 
     * @param material 材料信息
     * @return 结果
     */
    public int updateMaterial(Material material);

    /**
     * 删除材料信息信息
     * 
     * @param materialId 材料信息主键
     * @return 结果
     */
    public int deleteMaterialByMaterialId(Long materialId);

    
    /**
     * 获取材料名称选项
     *
     * @return 材料名称列表
     */
    public List<String> selectMaterialNameOptions();
    
    /**
    /**
     * 获取供应商名称选项
     *
     * @return 供应商名称列表
     */
    public List<String> selectSupplierNameOptions();

    /**
     * 获取材料型号选项
     *
     * @return 材料型号列表
     */
    public List<String> selectMaterialModelOptions();

    /**
     * 根据材料名称查询材料
     *
     * @param materialName 材料名称
     * @return 材料信息
     */
    public Material selectMaterialByMaterialName(String materialName);

    /**
     * 根据材料名称、供应商、材料型号查询材料信息
     *
     * @param materialName 材料名称
     * @param supplierName 供应商名称
     * @param materialModel 材料型号
     * @return 材料信息
     */
    public Material selectMaterialByNameSupplierModel(String materialName, String supplierName, String materialModel);

    /**
     * 批量删除材料信息
     * 
     * @param materialIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialByMaterialIds(Long[] materialIds);

    /**
     * 批量删除工艺参数组
     * 
     * @param materialIds 需要删除的材料主键集合
     * @return 结果
     */
    public int deleteProcessParamGroupByMaterialIds(Long[] materialIds);

    /**
     * 通过材料主键删除工艺参数组信息
     * 
     * @param materialId 材料ID
     * @return 结果
     */
    public int deleteProcessParamGroupByMaterialId(Long materialId);


    /**
     * 获取材料选项数据
     * 
     * @param material 材料
     * @return 选项列表
     */
    public List<String> selectMaterialOptions(Material material);

    /**
     * 获取完整导出数据（材料+参数组+参数明细）
     * 
     * @param material 材料查询条件
     * @return 完整数据列表
     */
    public List<Map<String, Object>> selectCompleteExportData(Material material);

    /**
     * 批量新增工艺参数组
     * 
     * @param list 工艺参数组列表
     * @return 结果
     */
    public int batchProcessParamGroup(List<ProcessParamGroup> list);
}


