package com.ruoyi.audit.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.audit.domain.Chemical;
import com.ruoyi.audit.service.IChemicalService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 化学检测数据Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/chemical/data")
public class ChemicalController extends BaseController
{
    @Autowired
    private IChemicalService chemicalService;

    /**
     * 查询化学检测数据列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:view')")
    @GetMapping("/list")
    public TableDataInfo list(Chemical chemical)
    {
        startPage();
        List<Chemical> list = chemicalService.selectChemicalList(chemical);
        return getDataTable(list);
    }

    /**
     * 导出化学检测数据列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:export')")
    @Log(title = "化学检测数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Chemical chemical)
    {
        List<Chemical> list = chemicalService.selectChemicalList(chemical);
        ExcelUtil<Chemical> util = new ExcelUtil<Chemical>(Chemical.class);
        util.exportExcel(response, list, "化学检测数据");
    }

    /**
     * 获取化学检测数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(chemicalService.selectChemicalById(id));
    }

    /**
     * 新增化学检测数据
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:add')")
    @Log(title = "化学检测数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Chemical chemical)
    {
        return toAjax(chemicalService.insertChemical(chemical));
    }

    /**
     * 修改化学检测数据
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:edit')")
    @Log(title = "化学检测数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Chemical chemical)
    {
        return toAjax(chemicalService.updateChemical(chemical));
    }

    /**
     * 删除化学检测数据
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:remove')")
    @Log(title = "化学检测数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(chemicalService.deleteChemicalByIds(ids));
    }

    /**
     * 查询未导出的化学检测数据列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:view')")
    @GetMapping("/unexported")
    public TableDataInfo unexportedList(Chemical chemical)
    {
        startPage();
        List<Chemical> list = chemicalService.selectUnexportedChemicalList(chemical);
        return getDataTable(list);
    }

    /**
     * 查询未处理的化学检测数据列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:view')")
    @GetMapping("/unprocessed")
    public TableDataInfo unprocessedList(Chemical chemical)
    {
        startPage();
        List<Chemical> list = chemicalService.selectUnprocessedChemicalList(chemical);
        return getDataTable(list);
    }

    /**
     * 批量更新导出状态
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:edit')")
    @Log(title = "化学检测数据", businessType = BusinessType.UPDATE)
    @PutMapping("/updateExportedStatus")
    public AjaxResult updateExportedStatus(@RequestBody String[] ids)
    {
        return toAjax(chemicalService.updateExportedStatusByIds(ids));
    }

    /**
     * 批量更新处理状态
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:edit')")
    @Log(title = "化学检测数据", businessType = BusinessType.UPDATE)
    @PutMapping("/updateProcessedStatus")
    public AjaxResult updateProcessedStatus(@RequestBody String[] ids)
    {
        return toAjax(chemicalService.updateProcessedStatusByIds(ids));
    }

    /**
     * 标记为不处理
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:edit')")
    @Log(title = "化学检测数据", businessType = BusinessType.UPDATE)
    @PutMapping("/markAsNotProcess/{id}")
    public AjaxResult markAsNotProcess(@PathVariable String id)
    {
        return toAjax(chemicalService.markAsNotProcess(id));
    }

    /**
     * 标记为已导出
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:edit')")
    @Log(title = "化学检测数据", businessType = BusinessType.UPDATE)
    @PutMapping("/markAsExported/{id}")
    public AjaxResult markAsExported(@PathVariable String id)
    {
        return toAjax(chemicalService.markAsExported(id));
    }

    /**
     * 重置处理状态
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:edit')")
    @Log(title = "化学检测数据", businessType = BusinessType.UPDATE)
    @PutMapping("/resetProcessStatus/{id}")
    public AjaxResult resetProcessStatus(@PathVariable String id)
    {
        return toAjax(chemicalService.resetProcessStatus(id));
    }

    /**
     * 重新处理数据
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:reprocess')")
    @Log(title = "化学检测数据", businessType = BusinessType.UPDATE)
    @PostMapping("/reprocess")
    public AjaxResult reprocessData(@RequestBody String[] ids)
    {
        int result = chemicalService.reprocessChemicalData(ids);
        return result > 0 ? success("重新处理成功，处理数量：" + result) : error("重新处理失败");
    }

    /**
     * 获取数据统计信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:view')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        return success(chemicalService.getDataStatistics());
    }

    /**
     * 处理化学数据
     */
    @PreAuthorize("@ss.hasPermi('chemical:data:edit')")
    @Log(title = "化学检测数据", businessType = BusinessType.UPDATE)
    @PostMapping("/process")
    public AjaxResult processData()
    {
        return success(chemicalService.processChemicalData());
    }

    /**
     * 处理Pulsar消息数据
     */
    @PostMapping("/processMessage")
    public AjaxResult processMessage(@RequestBody String messageData)
    {
        int result = chemicalService.processMessageData(messageData);
        return result > 0 ? success("消息处理成功") : error("消息处理失败");
    }
}
