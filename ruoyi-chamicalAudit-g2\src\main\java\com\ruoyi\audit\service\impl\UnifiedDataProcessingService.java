package com.ruoyi.audit.service.impl;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.ruoyi.audit.domain.Chemical;
import com.ruoyi.audit.domain.ChemicalYs;
import com.ruoyi.audit.domain.RuleConfig;
import com.ruoyi.audit.mapper.ChemicalMapper;
import com.ruoyi.audit.mapper.ChemicalYsMapper;
import com.ruoyi.audit.service.IRuleConfigService;
import com.ruoyi.audit.service.impl.DataRefreshRuleEngine;

/**
 * 统一数据处理服务
 * 整合原来分离的两个软件功能：
 * 1. 从云端读取数据并存储为原始数据
 * 2. 应用5大数据刷新规则
 * 3. 支持数据导出
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class UnifiedDataProcessingService 
{
    private static final Logger log = LoggerFactory.getLogger(UnifiedDataProcessingService.class);

    @Autowired
    private ChemicalMapper chemicalMapper;

    @Autowired
    private ChemicalYsMapper chemicalYsMapper;

    @Autowired
    private IRuleConfigService ruleConfigService;

    @Autowired
    private DataRefreshRuleEngine ruleEngine;

    // 云端数据库配置
    @Value("${chemical.database.cloud.url:************************************************************************************************************}")
    private String cloudDatabaseUrl;

    @Value("${chemical.database.cloud.username:hhh}")
    private String cloudDatabaseUsername;

    @Value("${chemical.database.cloud.password:root1234}")
    private String cloudDatabasePassword;

    // 统计信息
    private final AtomicLong totalReadRecords = new AtomicLong(0);
    private final AtomicLong totalProcessedRecords = new AtomicLong(0);
    private final AtomicLong totalModifiedRecords = new AtomicLong(0);
    private final AtomicInteger errorCount = new AtomicInteger(0);

    /**
     * 执行完整的数据处理流程
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param layerNumbers 层号列表
     * @return 处理结果
     */
    public Map<String, Object> executeCompleteDataProcessing(LocalDateTime startDate, LocalDateTime endDate, List<String> layerNumbers) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始执行完整数据处理流程: {} 至 {}", startDate, endDate);
            
            // 重置统计信息
            totalReadRecords.set(0);
            totalProcessedRecords.set(0);
            totalModifiedRecords.set(0);
            errorCount.set(0);
            
            // 第一步：从云端读取数据并存储为原始数据
            int readCount = readAndStoreRawDataFromCloud(startDate, endDate, layerNumbers);
            totalReadRecords.set(readCount);
            
            // 第二步：应用所有数据刷新规则
            int processedCount = applyDataRefreshRules(startDate, endDate, layerNumbers);
            totalProcessedRecords.set(processedCount);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            result.put("success", true);
            result.put("message", "数据处理完成");
            result.put("totalReadRecords", totalReadRecords.get());
            result.put("totalProcessedRecords", totalProcessedRecords.get());
            result.put("totalModifiedRecords", totalModifiedRecords.get());
            result.put("errorCount", errorCount.get());
            result.put("duration", duration);
            
            log.info("数据处理完成 - 读取: {}, 处理: {}, 修改: {}, 错误: {}, 耗时: {}ms", 
                    totalReadRecords.get(), totalProcessedRecords.get(), 
                    totalModifiedRecords.get(), errorCount.get(), duration);
            
        } catch (Exception e) {
            log.error("数据处理失败", e);
            result.put("success", false);
            result.put("message", "数据处理失败: " + e.getMessage());
            result.put("errorCount", errorCount.incrementAndGet());
        }
        
        return result;
    }

    /**
     * 第一步：从云端读取数据并存储为原始数据
     */
    private int readAndStoreRawDataFromCloud(LocalDateTime startDate, LocalDateTime endDate, List<String> layerNumbers) throws SQLException {
        log.info("开始从云端读取原始数据");
        
        int readCount = 0;
        
        try (Connection cloudConnection = getCloudConnection()) {
            // 构建查询SQL
            String sql = buildCloudDataQuerySql(startDate, endDate, layerNumbers);
            
            try (PreparedStatement stmt = cloudConnection.prepareStatement(sql)) {
                setQueryParameters(stmt, startDate, endDate, layerNumbers);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    List<Chemical> batchData = new ArrayList<>();
                    
                    while (rs.next()) {
                        Chemical chemical = mapResultSetToChemical(rs);
                        batchData.add(chemical);
                        readCount++;
                        
                        // 批量插入，每1000条提交一次
                        if (batchData.size() >= 1000) {
                            insertBatchChemicalData(batchData);
                            batchData.clear();
                        }
                    }
                    
                    // 插入剩余数据
                    if (!batchData.isEmpty()) {
                        insertBatchChemicalData(batchData);
                    }
                }
            }
        }
        
        log.info("从云端读取原始数据完成，共读取 {} 条记录", readCount);
        return readCount;
    }

    /**
     * 第二步：应用所有数据刷新规则
     */
    private int applyDataRefreshRules(LocalDateTime startDate, LocalDateTime endDate, List<String> layerNumbers) {
        log.info("开始应用数据刷新规则");
        
        int processedCount = 0;
        
        try {
            // 查询需要处理的数据
            List<ChemicalYs> dataToProcess = getDataForProcessing(startDate, endDate, layerNumbers);
            
            // 按产品-过程-测试分组
            Map<String, List<ChemicalYs>> groupedData = groupDataByProductProcessTest(dataToProcess);
            
            // 处理每个分组
            for (Map.Entry<String, List<ChemicalYs>> entry : groupedData.entrySet()) {
                String groupKey = entry.getKey();
                List<ChemicalYs> groupData = entry.getValue();
                
                try {
                    int modifiedCount = processDataGroup(groupKey, groupData);
                    totalModifiedRecords.addAndGet(modifiedCount);
                    processedCount += groupData.size();
                    
                    log.debug("处理分组 {}: {} 条记录，{} 条被修改", groupKey, groupData.size(), modifiedCount);
                    
                } catch (Exception e) {
                    log.error("处理分组 {} 失败", groupKey, e);
                    errorCount.incrementAndGet();
                }
            }
            
        } catch (Exception e) {
            log.error("应用数据刷新规则失败", e);
            errorCount.incrementAndGet();
        }
        
        log.info("数据刷新规则应用完成，共处理 {} 条记录", processedCount);
        return processedCount;
    }

    /**
     * 处理数据分组
     */
    private int processDataGroup(String groupKey, List<ChemicalYs> groupData) throws SQLException {
        if (groupData.isEmpty()) {
            return 0;
        }
        
        String[] parts = groupKey.split("~");
        String productName = parts[0];
        String processName = parts[1];
        String testName = parts[2];
        
        // 获取规则配置
        RuleConfig ruleConfig = ruleConfigService.selectRuleConfigByNames(productName, processName, testName);
        if (ruleConfig == null) {
            ruleConfig = ruleConfigService.getDefaultRuleConfig();
        }
        
        // 获取控制限制参数
        DataRefreshRuleEngine.ControlLimits controlLimits = getControlLimitsFromCloud(processName, productName, testName);
        if (controlLimits == null) {
            log.warn("未找到控制限制参数: {}", groupKey);
            return 0;
        }
        
        // 转换为Chemical列表
        List<Chemical> chemicals = convertYsToChemicalList(groupData);
        
        // 应用规则
        int modifiedCount = ruleEngine.applyAllRules(chemicals, controlLimits, ruleConfig);
        
        // 更新数据库
        updateProcessedData(groupData, chemicals);
        
        return modifiedCount;
    }

    /**
     * 获取云端数据库连接
     */
    private Connection getCloudConnection() throws SQLException {
        return DriverManager.getConnection(cloudDatabaseUrl, cloudDatabaseUsername, cloudDatabasePassword);
    }

    /**
     * 构建云端数据查询SQL
     */
    private String buildCloudDataQuerySql(LocalDateTime startDate, LocalDateTime endDate, List<String> layerNumbers) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM dbo.ChemicalData WHERE 1=1 ");
        
        if (startDate != null) {
            sql.append("AND examine_date >= ? ");
        }
        if (endDate != null) {
            sql.append("AND examine_date <= ? ");
        }
        if (layerNumbers != null && !layerNumbers.isEmpty()) {
            sql.append("AND layer_number IN (");
            for (int i = 0; i < layerNumbers.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(") ");
        }
        
        sql.append("ORDER BY examine_date DESC");
        return sql.toString();
    }

    /**
     * 设置查询参数
     */
    private void setQueryParameters(PreparedStatement stmt, LocalDateTime startDate, LocalDateTime endDate, List<String> layerNumbers) throws SQLException {
        int paramIndex = 1;
        
        if (startDate != null) {
            stmt.setTimestamp(paramIndex++, Timestamp.valueOf(startDate));
        }
        if (endDate != null) {
            stmt.setTimestamp(paramIndex++, Timestamp.valueOf(endDate));
        }
        if (layerNumbers != null && !layerNumbers.isEmpty()) {
            for (String layerNumber : layerNumbers) {
                stmt.setString(paramIndex++, layerNumber);
            }
        }
    }

    /**
     * 映射ResultSet到Chemical对象
     */
    private Chemical mapResultSetToChemical(ResultSet rs) throws SQLException {
        Chemical chemical = new Chemical();
        chemical.setId(rs.getString("id"));
        chemical.setOrganizationId(rs.getLong("organization_id"));
        chemical.setAttributeId(rs.getLong("attribute_id"));
        chemical.setExamineDate(rs.getTimestamp("examine_date"));
        chemical.setShift(rs.getString("shift"));
        chemical.setStaff(rs.getString("staff"));
        chemical.setProcessName(rs.getString("process_name"));
        chemical.setProductName(rs.getString("product_name"));
        chemical.setTestName(rs.getString("test_name"));
        chemical.setLayerNumber(rs.getString("layer_number"));
        chemical.setUpperLimit(rs.getString("upper_limit"));
        chemical.setMedianSpecification(rs.getString("median_specification"));
        chemical.setDownLimit(rs.getString("down_limit"));
        chemical.setExamine1(rs.getString("examine1"));
        chemical.setExamine2(rs.getString("examine2"));
        chemical.setExamine1ZS(rs.getString("examine1")); // 保存原始值
        chemical.setCreationDate(new Date());
        chemical.setLastUpdateDate(new Date());
        return chemical;
    }

    /**
     * 批量插入化学数据
     */
    private void insertBatchChemicalData(List<Chemical> chemicals) {
        for (Chemical chemical : chemicals) {
            try {
                chemicalMapper.insertChemical(chemical);
            } catch (Exception e) {
                log.error("插入化学数据失败: {}", chemical.getId(), e);
                errorCount.incrementAndGet();
            }
        }
    }

    /**
     * 获取需要处理的数据
     */
    private List<ChemicalYs> getDataForProcessing(LocalDateTime startDate, LocalDateTime endDate, List<String> layerNumbers) {
        // 这里应该查询chemical_ys表中的数据
        // 为了简化，直接返回空列表，实际应该实现查询逻辑
        return new ArrayList<>();
    }

    /**
     * 按产品-过程-测试分组数据
     */
    private Map<String, List<ChemicalYs>> groupDataByProductProcessTest(List<ChemicalYs> data) {
        return data.stream().collect(Collectors.groupingBy(
            ys -> ys.getProductName() + "~" + ys.getProcessName() + "~" + ys.getTestName()
        ));
    }

    /**
     * 转换ChemicalYs为Chemical列表
     */
    private List<Chemical> convertYsToChemicalList(List<ChemicalYs> chemicalYsList) {
        return chemicalYsList.stream().map(ys -> {
            Chemical chemical = new Chemical();
            chemical.setId(ys.getId());
            chemical.setExamine1(ys.getExamine1());
            chemical.setExamine2(ys.getExamine2());
            chemical.setExamine1ZS(ys.getExamine1());
            chemical.setUpperLimit(ys.getUpperLimit());
            chemical.setDownLimit(ys.getDownLimit());
            chemical.setProcessName(ys.getProcessName());
            chemical.setProductName(ys.getProductName());
            chemical.setTestName(ys.getTestName());
            return chemical;
        }).collect(Collectors.toList());
    }

    /**
     * 获取控制限制参数
     */
    private DataRefreshRuleEngine.ControlLimits getControlLimitsFromCloud(String processName, String productName, String testName) throws SQLException {
        try (Connection cloudConnection = getCloudConnection()) {
            String sql = "SELECT F_MEAN, F_SP FROM dbo.CTRL_LIM WHERE F_PRCS = ? AND F_TEST = ? AND F_PART = ? ORDER BY F_CTRL DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";
            
            try (PreparedStatement stmt = cloudConnection.prepareStatement(sql)) {
                stmt.setString(1, processName);
                stmt.setString(2, testName);
                stmt.setString(3, productName);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        double fMean = rs.getDouble("F_MEAN");
                        double fSp = rs.getDouble("F_SP");
                        return new DataRefreshRuleEngine.ControlLimits(fMean, fSp);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 更新处理后的数据
     */
    private void updateProcessedData(List<ChemicalYs> originalData, List<Chemical> processedData) {
        for (int i = 0; i < originalData.size() && i < processedData.size(); i++) {
            ChemicalYs original = originalData.get(i);
            Chemical processed = processedData.get(i);
            
            if (!processed.getExamine1().equals(original.getExamine1())) {
                original.setExamine1(processed.getExamine1());
                original.setExamine2(processed.getExamine2());
                original.setExamine1Zs(processed.getExamine1ZS());
                original.setIsModified(true);
                original.setOriginalExamine1(processed.getExamine1ZS());
                
                try {
                    chemicalYsMapper.updateChemicalYs(original);
                } catch (Exception e) {
                    log.error("更新处理后数据失败: {}", original.getId(), e);
                    errorCount.incrementAndGet();
                }
            }
        }
    }

    /**
     * 获取处理统计信息
     */
    public Map<String, Object> getProcessingStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalReadRecords", totalReadRecords.get());
        stats.put("totalProcessedRecords", totalProcessedRecords.get());
        stats.put("totalModifiedRecords", totalModifiedRecords.get());
        stats.put("errorCount", errorCount.get());
        return stats;
    }
}
