import request from '@/utils/request'

// 查询化学检测数据列表
export function listChemical(query) {
  return request({
    url: '/chemical/data/list',
    method: 'get',
    params: query
  })
}

// 查询化学检测数据详细
export function getChemical(id) {
  return request({
    url: '/chemical/data/' + id,
    method: 'get'
  })
}

// 新增化学检测数据
export function addChemical(data) {
  return request({
    url: '/chemical/data',
    method: 'post',
    data: data
  })
}

// 修改化学检测数据
export function updateChemical(data) {
  return request({
    url: '/chemical/data',
    method: 'put',
    data: data
  })
}

// 删除化学检测数据
export function delChemical(id) {
  return request({
    url: '/chemical/data/' + id,
    method: 'delete'
  })
}

// 查询未导出的化学检测数据列表
export function listUnexportedChemical(query) {
  return request({
    url: '/chemical/data/unexported',
    method: 'get',
    params: query
  })
}

// 查询未处理的化学检测数据列表
export function listUnprocessedChemical(query) {
  return request({
    url: '/chemical/data/unprocessed',
    method: 'get',
    params: query
  })
}

// 批量更新导出状态
export function updateExportedStatus(ids) {
  return request({
    url: '/chemical/data/updateExportedStatus',
    method: 'put',
    data: ids
  })
}

// 批量更新处理状态
export function updateProcessedStatus(ids) {
  return request({
    url: '/chemical/data/updateProcessedStatus',
    method: 'put',
    data: ids
  })
}

// 标记为不处理
export function markAsNotProcess(ids) {
  return request({
    url: '/chemical/data/markAsNotProcess',
    method: 'put',
    data: ids
  })
}

// 标记为已导出
export function markAsExported(ids) {
  return request({
    url: '/chemical/data/markAsExported',
    method: 'put',
    data: ids
  })
}

// 重置处理状态
export function resetProcessStatus(ids) {
  return request({
    url: '/chemical/data/resetProcessStatus',
    method: 'put',
    data: ids
  })
}

// 重新处理数据
export function reprocessChemicalData(ids) {
  return request({
    url: '/chemical/data/reprocess',
    method: 'post',
    data: ids
  })
}

// 获取数据统计信息
export function getDataStatistics() {
  return request({
    url: '/chemical/data/statistics',
    method: 'get'
  })
}

// 处理化学数据
export function processChemicalData() {
  return request({
    url: '/chemical/data/process',
    method: 'post'
  })
}

// 处理Pulsar消息数据
export function processMessageData(messageData) {
  return request({
    url: '/chemical/data/processMessage',
    method: 'post',
    data: messageData
  })
}
