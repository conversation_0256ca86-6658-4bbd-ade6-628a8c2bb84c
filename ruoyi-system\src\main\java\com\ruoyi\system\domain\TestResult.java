package com.ruoyi.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 测试结果对象 test_results
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class TestResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测试结果ID */
    private Long testResultId;

    /** 测试方案组ID */
    @Excel(name = "测试方案组ID")
    private Long planGroupId;

    /** 测试参数明细ID */
    @Excel(name = "测试参数明细ID")
    private Long testParamId;

    /** 测试方案编号（用于查询） */
    private String testPlanCode;

    /** 工艺参数组ID */
    @Excel(name = "工艺参数组ID")
    private Long groupId;

    /** 供应商Datasheet值 */
    @Excel(name = "供应商Datasheet值")
    private String supplierDatasheetVal;

    /** 实际测试值 */
    @Excel(name = "实际测试值")
    private BigDecimal testValue;

    /** 附件URL */
    private String attachments;

    /** 测试方案编号 */
    @Excel(name = "测试方案编号")
    private String planCode;

    /** 测试方案名称 */
    @Excel(name = "测试方案名称")
    private String performanceName;

    /** 参数编号 */
    @Excel(name = "参数编号")
    private String paramNumber;

    /** 材料名称 */
    @Excel(name = "材料名称")
    private String materialName;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String supplierName;

    /** 工艺类型 */
    @Excel(name = "工艺类型")
    private String processType;

    /** 参数名称 */
    @Excel(name = "参数名称")
    private String paramName;

    /** 参数单位 */
    @Excel(name = "参数单位")
    private String paramUnit;

    /** 材料型号 */
    @Excel(name = "材料型号")
    private String materialModel;

    /** 性能类型 */
    @Excel(name = "性能类型")
    private String performanceType;

    /** 测试设备 */
    @Excel(name = "测试设备")
    private String testEquipment;

    /** 测试参数 */
    @Excel(name = "测试参数")
    private String testParameter;

    public void setTestResultId(Long testResultId)
    {
        this.testResultId = testResultId;
    }

    public Long getTestResultId()
    {
        return testResultId;
    }
    public void setPlanGroupId(Long planGroupId)
    {
        this.planGroupId = planGroupId;
    }

    public Long getPlanGroupId()
    {
        return planGroupId;
    }

    public void setTestParamId(Long testParamId)
    {
        this.testParamId = testParamId;
    }

    public Long getTestParamId()
    {
        return testParamId;
    }

    public String getTestPlanCode()
    {
        return testPlanCode;
    }

    public void setTestPlanCode(String testPlanCode)
    {
        this.testPlanCode = testPlanCode;
    }
    public void setGroupId(Long groupId)
    {
        this.groupId = groupId;
    }

    public Long getGroupId()
    {
        return groupId;
    }
    public void setSupplierDatasheetVal(String supplierDatasheetVal)
    {
        this.supplierDatasheetVal = supplierDatasheetVal;
    }

    public String getSupplierDatasheetVal()
    {
        return supplierDatasheetVal;
    }
    public void setTestValue(BigDecimal testValue)
    {
        this.testValue = testValue;
    }

    public BigDecimal getTestValue()
    {
        return testValue;
    }

    public void setAttachments(String attachments)
    {
        this.attachments = attachments;
    }

    public String getAttachments()
    {
        return attachments;
    }

    public String getPlanCode()
    {
        return planCode;
    }

    public void setPlanCode(String planCode)
    {
        this.planCode = planCode;
    }

    public String getPerformanceName()
    {
        return performanceName;
    }

    public void setPerformanceName(String performanceName)
    {
        this.performanceName = performanceName;
    }

    public String getParamNumber()
    {
        return paramNumber;
    }

    public void setParamNumber(String paramNumber)
    {
        this.paramNumber = paramNumber;
    }

    public String getMaterialName()
    {
        return materialName;
    }

    public void setMaterialName(String materialName)
    {
        this.materialName = materialName;
    }

    public String getSupplierName()
    {
        return supplierName;
    }

    public void setSupplierName(String supplierName)
    {
        this.supplierName = supplierName;
    }

    public String getProcessType()
    {
        return processType;
    }

    public void setProcessType(String processType)
    {
        this.processType = processType;
    }

    public String getParamName()
    {
        return paramName;
    }

    public void setParamName(String paramName)
    {
        this.paramName = paramName;
    }

    public String getParamUnit()
    {
        return paramUnit;
    }

    public void setParamUnit(String paramUnit)
    {
        this.paramUnit = paramUnit;
    }

    public String getMaterialModel()
    {
        return materialModel;
    }

    public void setMaterialModel(String materialModel)
    {
        this.materialModel = materialModel;
    }

    public String getPerformanceType()
    {
        return performanceType;
    }

    public void setPerformanceType(String performanceType)
    {
        this.performanceType = performanceType;
    }

    public String getTestEquipment()
    {
        return testEquipment;
    }

    public void setTestEquipment(String testEquipment)
    {
        this.testEquipment = testEquipment;
    }

    public String getTestParameter()
    {
        return testParameter;
    }

    public void setTestParameter(String testParameter)
    {
        this.testParameter = testParameter;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("testResultId", getTestResultId())
                .append("planGroupId", getPlanGroupId())
                .append("testParamId", getTestParamId())
                .append("groupId", getGroupId())
                .append("supplierDatasheetVal", getSupplierDatasheetVal())
                .append("testValue", getTestValue())

                .append("attachments", getAttachments())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}