<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="检测日期" prop="examineDate">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="部门代码" prop="departmentCode">
        <el-select v-model="queryParams.departmentCode" placeholder="请选择部门" clearable>
          <el-option
            v-for="dept in departmentOptions"
            :key="dept.departmentCode"
            :label="dept.departmentName"
            :value="dept.departmentCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="工艺名称" prop="processName">
        <el-input
          v-model="queryParams.processName"
          placeholder="请输入工艺名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测试名称" prop="testName">
        <el-input
          v-model="queryParams.testName"
          placeholder="请输入测试名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="层号" prop="layerNumber">
        <el-input
          v-model="queryParams.layerNumber"
          placeholder="请输入层号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处理状态" prop="notProcess">
        <el-select v-model="queryParams.notProcess" placeholder="请选择状态" clearable>
          <el-option label="未处理" :value="false"></el-option>
          <el-option label="不处理" :value="true"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="导出状态" prop="isExported">
        <el-select v-model="queryParams.isExported" placeholder="请选择状态" clearable>
          <el-option label="未导出" :value="false"></el-option>
          <el-option label="已导出" :value="true"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['chemical:data:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['chemical:data:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['chemical:data:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['chemical:data:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          :disabled="multiple"
          @click="handleReprocess"
          v-hasPermi="['chemical:data:reprocess']"
        >重新处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-refresh-right"
          size="mini"
          @click="handleDataRefresh"
          v-hasPermi="['chemical:refresh:execute']"
        >数据刷新</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-dropdown @command="handleCommand">
          <el-button type="primary" size="mini">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="markExported" v-hasPermi="['chemical:data:edit']">标记为已导出</el-dropdown-item>
            <el-dropdown-item command="markNotProcess" v-hasPermi="['chemical:data:edit']">标记为不处理</el-dropdown-item>
            <el-dropdown-item command="resetStatus" v-hasPermi="['chemical:data:edit']">重置状态</el-dropdown-item>
            <el-dropdown-item command="exportCsv" v-hasPermi="['chemical:refresh:export']">导出CSV</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="chemicalList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" prop="id" width="120" />
      <el-table-column label="检测日期" prop="examineDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.examineDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="班次" prop="shift" width="80" />
      <el-table-column label="操作员工" prop="staff" width="100" />
      <el-table-column label="部门" prop="departmentName" width="120" />
      <el-table-column label="工艺名称" prop="processName" width="150" show-overflow-tooltip />
      <el-table-column label="产品名称" prop="productName" width="150" show-overflow-tooltip />
      <el-table-column label="测试名称" prop="testName" width="150" show-overflow-tooltip />
      <el-table-column label="层号" prop="layerNumber" width="100" />
      <el-table-column label="上限" prop="upperLimit" width="80" />
      <el-table-column label="中位规格" prop="medianSpecification" width="100" />
      <el-table-column label="下限" prop="downLimit" width="80" />
      <el-table-column label="检测值1" prop="examine1" width="100" />
      <el-table-column label="检测值2" prop="examine2" width="100" />
      <el-table-column label="处理状态" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.notProcess" type="warning" size="mini">不处理</el-tag>
          <el-tag v-else type="success" size="mini">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="导出状态" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isExported" type="success" size="mini">已导出</el-tag>
          <el-tag v-else type="info" size="mini">未导出</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="插入时间" prop="insertionTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.insertionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['chemical:data:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['chemical:data:remove']"
          >删除</el-button>
          <el-dropdown @command="(command) => handleRowCommand(command, scope.row)">
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="markExported">标记已导出</el-dropdown-item>
              <el-dropdown-item command="markNotProcess">标记不处理</el-dropdown-item>
              <el-dropdown-item command="resetStatus">重置状态</el-dropdown-item>
              <el-dropdown-item command="viewDetail">查看详情</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改化学检测数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="ID" prop="id">
              <el-input v-model="form.id" placeholder="请输入ID" :disabled="form.id != null" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测日期" prop="examineDate">
              <el-date-picker clearable
                v-model="form.examineDate"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择检测日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="班次" prop="shift">
              <el-input v-model="form.shift" placeholder="请输入班次" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作员工" prop="staff">
              <el-input v-model="form.staff" placeholder="请输入操作员工" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门代码" prop="departmentCode">
              <el-select v-model="form.departmentCode" placeholder="请选择部门">
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.departmentCode"
                  :label="dept.departmentName"
                  :value="dept.departmentCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="层号" prop="layerNumber">
              <el-input v-model="form.layerNumber" placeholder="请输入层号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工艺名称" prop="processName">
              <el-input v-model="form.processName" placeholder="请输入工艺名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="测试名称" prop="testName">
              <el-input v-model="form.testName" placeholder="请输入测试名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样本大小" prop="sampleSize">
              <el-input v-model="form.sampleSize" placeholder="请输入样本大小" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="上限" prop="upperLimit">
              <el-input v-model="form.upperLimit" placeholder="请输入上限" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="中位规格" prop="medianSpecification">
              <el-input v-model="form.medianSpecification" placeholder="请输入中位规格" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下限" prop="downLimit">
              <el-input v-model="form.downLimit" placeholder="请输入下限" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="检测值1" prop="examine1">
              <el-input v-model="form.examine1" placeholder="请输入检测值1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测值2" prop="examine2">
              <el-input v-model="form.examine2" placeholder="请输入检测值2" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="处理状态">
              <el-radio-group v-model="form.notProcess">
                <el-radio :label="false">正常处理</el-radio>
                <el-radio :label="true">不处理</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="导出状态">
              <el-radio-group v-model="form.isExported">
                <el-radio :label="false">未导出</el-radio>
                <el-radio :label="true">已导出</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="数据详情" :visible.sync="detailVisible" width="60%" append-to-body>
      <el-descriptions :column="2" border v-if="detailData">
        <el-descriptions-item label="ID">{{ detailData.id }}</el-descriptions-item>
        <el-descriptions-item label="检测日期">{{ parseTime(detailData.examineDate) }}</el-descriptions-item>
        <el-descriptions-item label="班次">{{ detailData.shift }}</el-descriptions-item>
        <el-descriptions-item label="操作员工">{{ detailData.staff }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ detailData.departmentName }}</el-descriptions-item>
        <el-descriptions-item label="层号">{{ detailData.layerNumber }}</el-descriptions-item>
        <el-descriptions-item label="工艺名称">{{ detailData.processName }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ detailData.productName }}</el-descriptions-item>
        <el-descriptions-item label="测试名称">{{ detailData.testName }}</el-descriptions-item>
        <el-descriptions-item label="样本大小">{{ detailData.sampleSize }}</el-descriptions-item>
        <el-descriptions-item label="上限">{{ detailData.upperLimit }}</el-descriptions-item>
        <el-descriptions-item label="中位规格">{{ detailData.medianSpecification }}</el-descriptions-item>
        <el-descriptions-item label="下限">{{ detailData.downLimit }}</el-descriptions-item>
        <el-descriptions-item label="检测值1">{{ detailData.examine1 }}</el-descriptions-item>
        <el-descriptions-item label="检测值2">{{ detailData.examine2 }}</el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag v-if="detailData.notProcess" type="warning" size="mini">不处理</el-tag>
          <el-tag v-else type="success" size="mini">正常</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="导出状态">
          <el-tag v-if="detailData.isExported" type="success" size="mini">已导出</el-tag>
          <el-tag v-else type="info" size="mini">未导出</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="插入时间">{{ parseTime(detailData.insertionTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailData.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailData.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 数据刷新对话框 -->
    <el-dialog title="数据刷新" :visible.sync="refreshDialogVisible" width="50%" append-to-body>
      <el-form :model="refreshForm" label-width="100px">
        <el-form-item label="开始时间" required>
          <el-date-picker
            v-model="refreshForm.startDate"
            type="date"
            placeholder="选择开始日期"
            value-format="yyyy-MM-dd"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" required>
          <el-date-picker
            v-model="refreshForm.endDate"
            type="date"
            placeholder="选择结束日期"
            value-format="yyyy-MM-dd"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="层号选择" required>
          <el-checkbox-group v-model="refreshForm.layerNumbers">
            <el-checkbox v-for="layer in layerOptions" :key="layer" :label="layer">{{ layer }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="导出路径">
          <el-input v-model="refreshForm.exportPath" placeholder="请输入导出路径" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="executeDataRefresh" :loading="refreshLoading">
          {{ refreshLoading ? '执行中...' : '开始刷新' }}
        </el-button>
        <el-button @click="refreshDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listChemical, getChemical, delChemical, addChemical, updateChemical,
  markAsExported, markAsNotProcess, resetProcessStatus, reprocessChemicalData
} from "@/api/chemical/data";
import {
  executeRefresh, exportCsv, getRefreshStatus
} from "@/api/chemical/refresh";

export default {
  name: "Chemical",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 化学检测数据表格数据
      chemicalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情对话框
      detailVisible: false,
      detailData: null,
      // 数据刷新对话框
      refreshDialogVisible: false,
      refreshLoading: false,
      refreshForm: {
        startDate: '',
        endDate: '',
        layerNumbers: [],
        exportPath: ''
      },
      // 日期范围
      dateRange: [],
      // 部门选项
      departmentOptions: [],
      // 层号选项
      layerOptions: ['L1', 'L2', 'L3', 'L4', 'L5', 'L6', 'L7', 'L8'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        examineDate: null,
        shift: null,
        staff: null,
        departmentCode: null,
        processName: null,
        productName: null,
        testName: null,
        layerNumber: null,
        isExported: null,
        notProcess: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [
          { required: true, message: "ID不能为空", trigger: "blur" }
        ],
        examineDate: [
          { required: true, message: "检测日期不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDepartmentOptions();
  },
  methods: {
    /** 查询化学检测数据列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.dateRange && '' != this.dateRange) {
        this.queryParams.params["beginTime"] = this.dateRange[0];
        this.queryParams.params["endTime"] = this.dateRange[1];
      }
      listChemical(this.queryParams).then(response => {
        this.chemicalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    
    /** 获取部门选项 */
    getDepartmentOptions() {
      // 这里应该调用获取部门列表的API
      this.departmentOptions = [
        { departmentCode: 'G2-001', departmentName: 'G2生产部' },
        { departmentCode: 'G2-002', departmentName: 'G2质检部' },
        { departmentCode: 'G2-003', departmentName: 'G2工艺部' },
        { departmentCode: 'G2-004', departmentName: 'G2设备部' }
      ];
    },
    
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    
    // 表单重置
    reset() {
      this.form = {
        id: null,
        examineDate: null,
        shift: null,
        staff: null,
        departmentCode: null,
        processName: null,
        productName: null,
        testName: null,
        layerNumber: null,
        upperLimit: null,
        medianSpecification: null,
        downLimit: null,
        examine1: null,
        examine2: null,
        isExported: false,
        notProcess: false,
        remark: null
      };
      this.resetForm("form");
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加化学检测数据";
    },
    
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getChemical(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改化学检测数据";
      });
    },
    
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateChemical(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addChemical(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除化学检测数据编号为"' + ids + '"的数据项？').then(function() {
        return delChemical(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    
    /** 导出按钮操作 */
    handleExport() {
      this.download('chemical/data/export', {
        ...this.queryParams
      }, `chemical_${new Date().getTime()}.xlsx`)
    },
    
    /** 重新处理按钮操作 */
    handleReprocess() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要重新处理的数据");
        return;
      }
      
      this.$modal.confirm('是否确认重新处理选中的数据？').then(() => {
        return reprocessChemicalData(this.ids);
      }).then((response) => {
        this.$modal.msgSuccess("重新处理成功");
        this.getList();
      }).catch(() => {});
    },
    
    /** 批量操作命令处理 */
    handleCommand(command) {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要操作的数据");
        return;
      }
      
      switch (command) {
        case 'markExported':
          this.batchMarkAsExported();
          break;
        case 'markNotProcess':
          this.batchMarkAsNotProcess();
          break;
        case 'resetStatus':
          this.batchResetStatus();
          break;
        case 'exportCsv':
          this.handleExportCsv();
          break;
      }
    },
    
    /** 行操作命令处理 */
    handleRowCommand(command, row) {
      switch (command) {
        case 'markExported':
          this.markAsExported(row.id);
          break;
        case 'markNotProcess':
          this.markAsNotProcess(row.id);
          break;
        case 'resetStatus':
          this.resetStatus(row.id);
          break;
        case 'viewDetail':
          this.viewDetail(row);
          break;
      }
    },
    
    /** 批量标记为已导出 */
    batchMarkAsExported() {
      this.$modal.confirm('是否确认标记选中数据为已导出？').then(() => {
        return markAsExported(this.ids);
      }).then(() => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      }).catch(() => {});
    },
    
    /** 批量标记为不处理 */
    batchMarkAsNotProcess() {
      this.$modal.confirm('是否确认标记选中数据为不处理？').then(() => {
        return markAsNotProcess(this.ids);
      }).then(() => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      }).catch(() => {});
    },
    
    /** 批量重置状态 */
    batchResetStatus() {
      this.$modal.confirm('是否确认重置选中数据的状态？').then(() => {
        return resetProcessStatus(this.ids);
      }).then(() => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      }).catch(() => {});
    },
    
    /** 单个标记为已导出 */
    markAsExported(id) {
      markAsExported([id]).then(() => {
        this.$modal.msgSuccess("标记成功");
        this.getList();
      });
    },
    
    /** 单个标记为不处理 */
    markAsNotProcess(id) {
      markAsNotProcess([id]).then(() => {
        this.$modal.msgSuccess("标记成功");
        this.getList();
      });
    },
    
    /** 单个重置状态 */
    resetStatus(id) {
      resetProcessStatus([id]).then(() => {
        this.$modal.msgSuccess("重置成功");
        this.getList();
      });
    },
    
    /** 查看详情 */
    viewDetail(row) {
      this.detailData = row;
      this.detailVisible = true;
    },

    /** 数据刷新按钮操作 */
    handleDataRefresh() {
      // 设置默认时间范围为最近7天
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 7);

      this.refreshForm = {
        startDate: this.formatDate(startDate),
        endDate: this.formatDate(endDate),
        layerNumbers: ['L1', 'L2', 'L3', 'L4'],
        exportPath: 'E:\\测试数据\\应审抛转数据\\G2'
      };

      this.refreshDialogVisible = true;
    },

    /** 执行数据刷新 */
    executeDataRefresh() {
      if (!this.refreshForm.startDate || !this.refreshForm.endDate) {
        this.$modal.msgWarning("请选择时间范围");
        return;
      }

      if (this.refreshForm.layerNumbers.length === 0) {
        this.$modal.msgWarning("请选择至少一个层号");
        return;
      }

      this.refreshLoading = true;

      const params = {
        startDate: this.refreshForm.startDate + ' 00:00:00',
        endDate: this.refreshForm.endDate + ' 23:59:59',
        layerNumbers: this.refreshForm.layerNumbers,
        exportPath: this.refreshForm.exportPath || 'E:\\测试数据\\应审抛转数据\\G2'
      };

      executeRefresh(params).then(response => {
        this.refreshLoading = false;
        this.refreshDialogVisible = false;

        if (response.code === 200) {
          this.$modal.msgSuccess("数据刷新任务启动成功");
          this.getList(); // 刷新列表
        } else {
          this.$modal.msgError(response.msg || "数据刷新失败");
        }
      }).catch(error => {
        this.refreshLoading = false;
        this.$modal.msgError("数据刷新失败: " + (error.message || "未知错误"));
      });
    },

    /** 导出CSV */
    handleExportCsv() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要导出的数据");
        return;
      }

      // 获取选中数据的时间范围
      const selectedData = this.chemicalList.filter(item => this.ids.includes(item.id));
      if (selectedData.length === 0) {
        this.$modal.msgWarning("未找到选中的数据");
        return;
      }

      // 计算时间范围
      const dates = selectedData.map(item => new Date(item.examineDate));
      const startDate = new Date(Math.min(...dates));
      const endDate = new Date(Math.max(...dates));

      // 获取层号
      const layerNumbers = [...new Set(selectedData.map(item => item.layerNumber))];

      const params = {
        startDate: this.formatDate(startDate) + ' 00:00:00',
        endDate: this.formatDate(endDate) + ' 23:59:59',
        layerNumbers: layerNumbers,
        exportPath: 'E:\\测试数据\\应审抛转数据\\G2'
      };

      this.$modal.confirm('确认导出选中数据到CSV文件？').then(() => {
        return exportCsv(params);
      }).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("CSV导出成功");
        } else {
          this.$modal.msgError(response.msg || "CSV导出失败");
        }
      }).catch(() => {});
    },

    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
};
</script>
