import request from '@/utils/request'

// 查询测试方案列表
export function listTestPlan(query) {
  return request({
    url: '/material/testPlan/list',
    method: 'get',
    params: query
  })
}

// 查询测试方案详细
export function getTestPlan(testPlanId) {
  return request({
    url: '/material/testPlan/' + testPlanId,
    method: 'get'
  })
}

// 新增测试方案
export function addTestPlan(data) {
  return request({
    url: '/material/testPlan',
    method: 'post',
    data: data
  })
}

// 修改测试方案
export function updateTestPlan(data) {
  return request({
    url: '/material/testPlan',
    method: 'put',
    data: data
  })
}

// 删除测试方案
export function delTestPlan(testPlanId) {
  return request({
    url: '/material/testPlan/' + testPlanId,
    method: 'delete'
  })
}

// 导出测试方案
export function exportTestPlan(query) {
  return request({
    url: '/material/testPlan/export',
    method: 'get',
    params: query
  })
}

// 获取测试方案选项（用于下拉选择）
export function getTestPlanOptions(query) {
  return request({
    url: '/material/testPlan/options',
    method: 'get',
    params: query
  })
}

// 上传附件
export function uploadPlanAttachment(data) {
  return request({
    url: '/material/testPlan/upload',
    method: 'post',
    data: data
  })
}

// 下载附件
export function downloadPlanAttachment(fileName) {
  return request({
    url: '/material/testPlan/download/' + fileName,
    method: 'get',
    responseType: 'blob'
  })
}