<template>
  <div class="app-container">
    <!-- 任务控制面板 -->
    <el-card class="mb20">
      <div slot="header" class="clearfix">
        <span>任务控制面板</span>
      </div>
      
      <el-row :gutter="20">
        <!-- 数据读取任务 -->
        <el-col :span="8">
          <div class="task-panel">
            <div class="task-header">
              <h3>数据读取任务</h3>
              <el-tag :type="getTaskStatusType(dataReadingTask.status)">
                {{ getTaskStatusText(dataReadingTask.status) }}
              </el-tag>
            </div>
            <div class="task-info">
              <p><strong>任务名称:</strong> {{ dataReadingTask.taskName || 'Pulsar数据读取' }}</p>
              <p><strong>开始时间:</strong> {{ dataReadingTask.startTime || '-' }}</p>
              <p><strong>运行时长:</strong> {{ getRunningTime(dataReadingTask.startTime) }}</p>
              <p><strong>处理记录:</strong> {{ dataReadingTask.processedRecords || 0 }} 条</p>
            </div>
            <div class="task-controls">
              <el-button 
                type="success" 
                size="small" 
                :disabled="dataReadingTask.status === 'RUNNING'"
                @click="startDataReading"
                v-hasPermi="['chemical:task:start']">
                启动
              </el-button>
              <el-button 
                type="warning" 
                size="small" 
                :disabled="dataReadingTask.status !== 'RUNNING'"
                @click="pauseDataReading"
                v-hasPermi="['chemical:task:pause']">
                暂停
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                :disabled="dataReadingTask.status === 'STOPPED'"
                @click="stopDataReading"
                v-hasPermi="['chemical:task:stop']">
                停止
              </el-button>
              <el-button 
                type="primary" 
                size="small" 
                @click="restartDataReading"
                v-hasPermi="['chemical:task:restart']">
                重启
              </el-button>
            </div>
          </div>
        </el-col>

        <!-- 数据处理任务 -->
        <el-col :span="8">
          <div class="task-panel">
            <div class="task-header">
              <h3>数据处理任务</h3>
              <el-tag :type="getTaskStatusType(dataProcessingTask.status)">
                {{ getTaskStatusText(dataProcessingTask.status) }}
              </el-tag>
            </div>
            <div class="task-info">
              <p><strong>任务名称:</strong> {{ dataProcessingTask.taskName || '化学数据处理' }}</p>
              <p><strong>开始时间:</strong> {{ dataProcessingTask.startTime || '-' }}</p>
              <p><strong>处理进度:</strong> {{ dataProcessingTask.progressPercent || 0 }}%</p>
              <p><strong>成功记录:</strong> {{ dataProcessingTask.successRecords || 0 }} 条</p>
            </div>
            <div class="task-controls">
              <el-button 
                type="success" 
                size="small" 
                :disabled="dataProcessingTask.status === 'RUNNING'"
                @click="startDataProcessing"
                v-hasPermi="['chemical:task:start']">
                启动处理
              </el-button>
              <el-button 
                type="info" 
                size="small" 
                @click="viewProcessingDetails">
                查看详情
              </el-button>
            </div>
          </div>
        </el-col>

        <!-- 日志处理任务 -->
        <el-col :span="8">
          <div class="task-panel">
            <div class="task-header">
              <h3>日志处理任务</h3>
              <el-tag :type="getTaskStatusType(logProcessingTask.status)">
                {{ getTaskStatusText(logProcessingTask.status) }}
              </el-tag>
            </div>
            <div class="task-info">
              <p><strong>任务名称:</strong> {{ logProcessingTask.taskName || '日志数据处理' }}</p>
              <p><strong>开始时间:</strong> {{ logProcessingTask.startTime || '-' }}</p>
              <p><strong>日志数量:</strong> {{ logProcessingTask.totalRecords || 0 }} 条</p>
              <p><strong>错误数量:</strong> {{ logProcessingTask.errorRecords || 0 }} 条</p>
            </div>
            <div class="task-controls">
              <el-button 
                type="success" 
                size="small" 
                :disabled="logProcessingTask.status === 'RUNNING'"
                @click="startLogProcessing"
                v-hasPermi="['chemical:task:start']">
                启动处理
              </el-button>
              <el-button 
                type="warning" 
                size="small" 
                @click="clearLogs"
                v-hasPermi="['chemical:task:start']">
                清空日志
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- Pulsar连接状态 -->
    <el-card class="mb20">
      <div slot="header" class="clearfix">
        <span>Pulsar连接状态</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshPulsarStatus">刷新</el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="connection-info">
            <div class="info-item">
              <span class="label">连接状态:</span>
              <el-tag :type="pulsarStatus.connected ? 'success' : 'danger'">
                {{ pulsarStatus.connected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="label">服务地址:</span>
              <span>{{ pulsarConfig.brokerUrl || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">主题名称:</span>
              <span>{{ pulsarConfig.topicName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">订阅名称:</span>
              <span>{{ pulsarConfig.subscriptionName || '-' }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="connection-controls">
            <el-button 
              type="primary" 
              :disabled="pulsarStatus.connected"
              @click="initPulsarClient"
              v-hasPermi="['chemical:task:start']">
              初始化连接
            </el-button>
            <el-button 
              type="danger" 
              :disabled="!pulsarStatus.connected"
              @click="closePulsarClient"
              v-hasPermi="['chemical:task:stop']">
              关闭连接
            </el-button>
            <el-button 
              type="info" 
              @click="testConnection">
              测试连接
            </el-button>
          </div>
          <div class="consumer-stats" v-if="pulsarStatus.connected">
            <h4>消费者统计</h4>
            <p>接收消息数: {{ pulsarStatus.receivedMessages || 0 }}</p>
            <p>处理成功数: {{ pulsarStatus.processedMessages || 0 }}</p>
            <p>处理失败数: {{ pulsarStatus.failedMessages || 0 }}</p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 任务历史记录 -->
    <el-card>
      <div slot="header" class="clearfix">
        <span>任务历史记录</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshTaskHistory">刷新</el-button>
      </div>
      
      <el-table :data="taskHistory" v-loading="loading" border>
        <el-table-column prop="taskName" label="任务名称" width="150"></el-table-column>
        <el-table-column prop="taskType" label="任务类型" width="120">
          <template slot-scope="scope">
            <el-tag size="mini">{{ getTaskTypeText(scope.row.taskType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taskStatus" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getTaskStatusType(scope.row.taskStatus)" size="mini">
              {{ getTaskStatusText(scope.row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="180"></el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="180"></el-table-column>
        <el-table-column prop="totalRecords" label="总记录数" width="100"></el-table-column>
        <el-table-column prop="successRecords" label="成功数" width="100"></el-table-column>
        <el-table-column prop="errorRecords" label="错误数" width="100"></el-table-column>
        <el-table-column prop="progressPercent" label="进度" width="100">
          <template slot-scope="scope">
            <el-progress :percentage="scope.row.progressPercent" :show-text="false" :stroke-width="6"></el-progress>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="viewTaskDetail(scope.row)">详情</el-button>
            <el-button 
              size="mini" 
              type="text" 
              v-if="scope.row.taskStatus === 'ERROR'"
              @click="retryTask(scope.row)">
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getTaskHistory"
      />
    </el-card>

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" :visible.sync="detailDialogVisible" width="60%">
      <div v-if="selectedTask">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.taskId }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ selectedTask.taskName }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ getTaskTypeText(selectedTask.taskType) }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getTaskStatusType(selectedTask.taskStatus)">
              {{ getTaskStatusText(selectedTask.taskStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ selectedTask.startTime }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ selectedTask.endTime }}</el-descriptions-item>
          <el-descriptions-item label="总记录数">{{ selectedTask.totalRecords }}</el-descriptions-item>
          <el-descriptions-item label="已处理数">{{ selectedTask.processedRecords }}</el-descriptions-item>
          <el-descriptions-item label="成功数">{{ selectedTask.successRecords }}</el-descriptions-item>
          <el-descriptions-item label="错误数">{{ selectedTask.errorRecords }}</el-descriptions-item>
          <el-descriptions-item label="进度百分比">{{ selectedTask.progressPercent }}%</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedTask.createTime }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedTask.errorMessage" style="margin-top: 20px;">
          <h4>错误信息:</h4>
          <el-input
            type="textarea"
            :rows="4"
            :value="selectedTask.errorMessage"
            readonly>
          </el-input>
        </div>
        
        <div v-if="selectedTask.taskConfig" style="margin-top: 20px;">
          <h4>任务配置:</h4>
          <el-input
            type="textarea"
            :rows="6"
            :value="selectedTask.taskConfig"
            readonly>
          </el-input>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  startTask, pauseTask, stopTask, restartTask, 
  getTaskStatus, startDataProcessing, startLogProcessing,
  initPulsarClient, closePulsarClient, checkPulsarStatus,
  clearLogs
} from "@/api/chemical/task";
import { getTaskMonitorList } from "@/api/chemical/monitor";

export default {
  name: "ChemicalTask",
  data() {
    return {
      // 任务状态
      dataReadingTask: {},
      dataProcessingTask: {},
      logProcessingTask: {},
      
      // Pulsar状态
      pulsarStatus: {
        connected: false
      },
      pulsarConfig: {},
      
      // 任务历史
      taskHistory: [],
      total: 0,
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      
      // 对话框
      detailDialogVisible: false,
      selectedTask: null,
      
      // 定时器
      statusTimer: null
    };
  },
  created() {
    this.loadTaskStatus();
    this.getTaskHistory();
    this.startStatusPolling();
  },
  beforeDestroy() {
    this.stopStatusPolling();
  },
  methods: {
    // 加载任务状态
    async loadTaskStatus() {
      try {
        const response = await getTaskStatus();
        if (response.code === 200) {
          const data = response.data;
          this.dataReadingTask = data.dataReading || {};
          this.dataProcessingTask = data.dataProcessing || {};
          this.logProcessingTask = data.logProcessing || {};
        }
      } catch (error) {
        console.error('加载任务状态失败:', error);
      }
    },
    
    // 启动数据读取任务
    async startDataReading() {
      try {
        const response = await startTask();
        if (response.code === 200) {
          this.$message.success('数据读取任务启动成功');
          this.loadTaskStatus();
        } else {
          this.$message.error(response.msg || '启动失败');
        }
      } catch (error) {
        this.$message.error('启动任务失败');
      }
    },
    
    // 暂停数据读取任务
    async pauseDataReading() {
      try {
        const response = await pauseTask();
        if (response.code === 200) {
          this.$message.success('数据读取任务已暂停');
          this.loadTaskStatus();
        } else {
          this.$message.error(response.msg || '暂停失败');
        }
      } catch (error) {
        this.$message.error('暂停任务失败');
      }
    },
    
    // 停止数据读取任务
    async stopDataReading() {
      try {
        const response = await stopTask();
        if (response.code === 200) {
          this.$message.success('数据读取任务已停止');
          this.loadTaskStatus();
        } else {
          this.$message.error(response.msg || '停止失败');
        }
      } catch (error) {
        this.$message.error('停止任务失败');
      }
    },
    
    // 重启数据读取任务
    async restartDataReading() {
      try {
        const response = await restartTask();
        if (response.code === 200) {
          this.$message.success('数据读取任务重启成功');
          this.loadTaskStatus();
        } else {
          this.$message.error(response.msg || '重启失败');
        }
      } catch (error) {
        this.$message.error('重启任务失败');
      }
    },
    
    // 启动数据处理任务
    async startDataProcessing() {
      try {
        const response = await startDataProcessing();
        if (response.code === 200) {
          this.$message.success('数据处理任务启动成功');
          this.loadTaskStatus();
        } else {
          this.$message.error(response.msg || '启动失败');
        }
      } catch (error) {
        this.$message.error('启动数据处理失败');
      }
    },
    
    // 启动日志处理任务
    async startLogProcessing() {
      try {
        const response = await startLogProcessing();
        if (response.code === 200) {
          this.$message.success('日志处理任务启动成功');
          this.loadTaskStatus();
        } else {
          this.$message.error(response.msg || '启动失败');
        }
      } catch (error) {
        this.$message.error('启动日志处理失败');
      }
    },
    
    // 初始化Pulsar客户端
    async initPulsarClient() {
      try {
        const response = await initPulsarClient();
        if (response.code === 200) {
          this.$message.success('Pulsar客户端初始化成功');
          this.refreshPulsarStatus();
        } else {
          this.$message.error(response.msg || '初始化失败');
        }
      } catch (error) {
        this.$message.error('初始化Pulsar客户端失败');
      }
    },
    
    // 关闭Pulsar客户端
    async closePulsarClient() {
      try {
        const response = await closePulsarClient();
        if (response.code === 200) {
          this.$message.success('Pulsar客户端已关闭');
          this.refreshPulsarStatus();
        } else {
          this.$message.error(response.msg || '关闭失败');
        }
      } catch (error) {
        this.$message.error('关闭Pulsar客户端失败');
      }
    },
    
    // 刷新Pulsar状态
    async refreshPulsarStatus() {
      try {
        const response = await checkPulsarStatus();
        if (response.code === 200) {
          this.pulsarStatus = response.data;
        }
      } catch (error) {
        console.error('刷新Pulsar状态失败:', error);
      }
    },
    
    // 测试连接
    testConnection() {
      this.refreshPulsarStatus();
      this.$message.info('连接测试完成');
    },
    
    // 清空日志
    async clearLogs() {
      try {
        await this.$confirm('确认清空所有日志吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        const response = await clearLogs();
        if (response.code === 200) {
          this.$message.success('日志已清空');
        } else {
          this.$message.error(response.msg || '清空失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('清空日志失败');
        }
      }
    },
    
    // 获取任务历史
    async getTaskHistory() {
      this.loading = true;
      try {
        const response = await getTaskMonitorList(this.queryParams);
        if (response.code === 200) {
          this.taskHistory = response.rows;
          this.total = response.total;
        }
      } catch (error) {
        console.error('获取任务历史失败:', error);
      } finally {
        this.loading = false;
      }
    },
    
    // 刷新任务历史
    refreshTaskHistory() {
      this.getTaskHistory();
    },
    
    // 查看任务详情
    viewTaskDetail(task) {
      this.selectedTask = task;
      this.detailDialogVisible = true;
    },
    
    // 查看处理详情
    viewProcessingDetails() {
      // 跳转到数据管理页面
      this.$router.push('/chemical/data/chemical');
    },
    
    // 重试任务
    retryTask(task) {
      this.$message.info('重试功能开发中...');
    },
    
    // 开始状态轮询
    startStatusPolling() {
      this.statusTimer = setInterval(() => {
        this.loadTaskStatus();
        this.refreshPulsarStatus();
      }, 10000); // 每10秒刷新一次
    },
    
    // 停止状态轮询
    stopStatusPolling() {
      if (this.statusTimer) {
        clearInterval(this.statusTimer);
      }
    },
    
    // 获取任务状态类型
    getTaskStatusType(status) {
      const statusMap = {
        'RUNNING': 'success',
        'PAUSED': 'warning',
        'STOPPED': 'info',
        'ERROR': 'danger',
        'COMPLETED': 'success'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取任务状态文本
    getTaskStatusText(status) {
      const statusMap = {
        'RUNNING': '运行中',
        'PAUSED': '已暂停',
        'STOPPED': '已停止',
        'ERROR': '错误',
        'COMPLETED': '已完成',
        'WAITING': '等待中'
      };
      return statusMap[status] || '未知';
    },
    
    // 获取任务类型文本
    getTaskTypeText(type) {
      const typeMap = {
        'DATA_READ': '数据读取',
        'DATA_PROCESS': '数据处理',
        'LOG_READ': '日志读取',
        'DATA_EXPORT': '数据导出',
        'DATA_REFRESH': '数据刷新'
      };
      return typeMap[type] || type;
    },
    
    // 获取运行时长
    getRunningTime(startTime) {
      if (!startTime) return '-';
      
      const start = new Date(startTime);
      const now = new Date();
      const diff = now - start;
      
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.task-panel {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  height: 280px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.task-header h3 {
  margin: 0;
  font-size: 16px;
}

.task-info {
  margin-bottom: 20px;
}

.task-info p {
  margin: 8px 0;
  font-size: 14px;
}

.task-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.connection-info .info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.connection-info .label {
  width: 80px;
  font-weight: bold;
}

.connection-controls {
  margin-bottom: 20px;
}

.connection-controls .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.consumer-stats {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.consumer-stats h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
}

.consumer-stats p {
  margin: 5px 0;
  font-size: 13px;
}
</style>
