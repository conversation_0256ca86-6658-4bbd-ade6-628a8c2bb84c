package com.ruoyi.audit.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.audit.domain.ChemicalRule;

/**
 * 化学处理规则Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IChemicalRuleService 
{
    /**
     * 查询化学处理规则
     * 
     * @param ruleId 化学处理规则主键
     * @return 化学处理规则
     */
    public ChemicalRule selectChemicalRuleByRuleId(Long ruleId);

    /**
     * 查询化学处理规则列表
     * 
     * @param chemicalRule 化学处理规则
     * @return 化学处理规则集合
     */
    public List<ChemicalRule> selectChemicalRuleList(ChemicalRule chemicalRule);

    /**
     * 新增化学处理规则
     * 
     * @param chemicalRule 化学处理规则
     * @return 结果
     */
    public int insertChemicalRule(ChemicalRule chemicalRule);

    /**
     * 修改化学处理规则
     * 
     * @param chemicalRule 化学处理规则
     * @return 结果
     */
    public int updateChemicalRule(ChemicalRule chemicalRule);

    /**
     * 批量删除化学处理规则
     * 
     * @param ruleIds 需要删除的化学处理规则主键集合
     * @return 结果
     */
    public int deleteChemicalRuleByRuleIds(Long[] ruleIds);

    /**
     * 删除化学处理规则信息
     * 
     * @param ruleId 化学处理规则主键
     * @return 结果
     */
    public int deleteChemicalRuleByRuleId(Long ruleId);

    /**
     * 根据产品名、工艺名、测试名查询规则
     * 
     * @param productName 产品名
     * @param processName 工艺名
     * @param testName 测试名
     * @return 化学处理规则
     */
    public ChemicalRule selectRuleByNames(String productName, String processName, String testName);

    /**
     * 检查规则是否存在
     * 
     * @param productName 产品名
     * @param processName 工艺名
     * @param testName 测试名
     * @return 是否存在
     */
    public boolean existsRule(String productName, String processName, String testName);

    /**
     * 查询启用的规则列表
     * 
     * @return 启用的规则列表
     */
    public List<ChemicalRule> selectEnabledRuleList();

    /**
     * 查询需要刷新的规则列表
     * 
     * @return 需要刷新的规则列表
     */
    public List<ChemicalRule> selectRefreshRuleList();

    /**
     * 查询特殊处理规则列表
     * 
     * @return 特殊处理规则列表
     */
    public List<ChemicalRule> selectSpecialRuleList();

    /**
     * 启用规则
     * 
     * @param ruleIds 规则ID数组
     * @return 结果
     */
    public int enableRules(Long[] ruleIds);

    /**
     * 禁用规则
     * 
     * @param ruleIds 规则ID数组
     * @return 结果
     */
    public int disableRules(Long[] ruleIds);

    /**
     * 复制规则
     * 
     * @param ruleId 规则ID
     * @return 结果
     */
    public int copyRule(Long ruleId);

    /**
     * 导入规则
     * 
     * @param rules 规则列表
     * @return 结果
     */
    public int importRules(List<ChemicalRule> rules);

    /**
     * 获取规则统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getRuleStatistics();
}
