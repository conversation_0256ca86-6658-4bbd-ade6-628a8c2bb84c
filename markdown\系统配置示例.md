# 化学审计系统配置示例

## 1. application.yml 配置示例

```yaml
# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30

# Spring配置
spring:
  application:
    name: ruoyi-chemical-audit
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      # 主数据源
      master:
        url: ****************************************************************
        username: sa
        password: your_password
      # 从数据源
      slave:
        enabled: false
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# 化学审计系统专用配置
chemical:
  # 数据库配置
  database:
    local:
      url: *****************************************************************
      username: sa
      password: your_local_password
      driver: com.microsoft.sqlserver.jdbc.SQLServerDriver
    cloud:
      url: ******************************************************************************************;sslProtocol=TLSv1
      username: sa
      password: your_cloud_password
      driver: com.microsoft.sqlserver.jdbc.SQLServerDriver
  
  # Pulsar消息队列配置
  pulsar:
    service:
      url: pulsar://localhost:6650
    consumer:
      subscription: chemical-audit-subscription
      topic: chemical-data-topic
      subscriptionType: Shared
      receiverQueueSize: 1000
      maxTotalReceiverQueueSizeAcrossPartitions: 50000
    producer:
      topic: chemical-result-topic
      sendTimeoutMs: 30000
      blockIfQueueFull: true
  
  # 导出配置
  export:
    default:
      path: E:\测试数据\应审抛转数据\G2
    backup:
      path: E:\测试数据\备份数据\G2
    max:
      records: 50000
    formats:
      - CSV
      - EXCEL
    csv:
      encoding: UTF-8
      separator: ","
      withBOM: true
    excel:
      maxRowsPerSheet: 65000
      autoSizeColumn: true
  
  # 任务配置
  task:
    # 数据读取任务配置
    dataReading:
      enabled: true
      batchSize: 1000
      intervalMs: 5000
      maxRetries: 3
    # 数据处理任务配置
    dataProcessing:
      enabled: true
      threadPoolSize: 5
      queueCapacity: 10000
    # 日志处理任务配置
    logProcessing:
      enabled: true
      maxLogSize: 10000
      cleanupIntervalHours: 24
  
  # 监控配置
  monitor:
    # 性能监控
    performance:
      enabled: true
      intervalSeconds: 30
    # 健康检查
    health:
      enabled: true
      intervalSeconds: 60
    # 告警配置
    alert:
      enabled: true
      email:
        enabled: false
        recipients:
          - <EMAIL>
      webhook:
        enabled: false
        url: http://localhost:8080/webhook/alert
  
  # 数据刷新配置
  refresh:
    # 默认刷新参数
    default:
      layerNumbers:
        - L1
        - L2
        - L3
        - L4
      timeRangeDays: 7
    # 刷新算法配置
    algorithm:
      # 控制限制计算参数
      controlLimits:
        multiplier: 6
        sampleSizeAdjustment:
          "PD全线微蚀量": 3
          "TR微蚀微蚀量": 5
      # 数据调整参数
      adjustment:
        upperRatio: 0.875  # 7/8
        lowerRatio: 0.125  # 1/8
        precision: 3       # 小数点后3位
    # 并发控制
    concurrency:
      maxConcurrentTasks: 1
      taskTimeoutMinutes: 30
  
  # 缓存配置
  cache:
    # Redis缓存配置
    redis:
      enabled: false
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 3000
    # 本地缓存配置
    local:
      enabled: true
      maxSize: 1000
      expireAfterWriteMinutes: 30

# 日志配置
logging:
  level:
    com.ruoyi.audit: DEBUG
    com.ruoyi.audit.service: INFO
    org.apache.pulsar: WARN
    org.springframework: WARN
  file:
    name: logs/chemical-audit.log
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    console: '%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 安全配置
security:
  # 忽略认证的URL
  ignore:
    urls:
      - /chemical/monitor/health
      - /chemical/export/download/**
```

## 2. application-dev.yml 开发环境配置

```yaml
# 开发环境配置
spring:
  datasource:
    druid:
      master:
        url: ********************************************************************
        username: sa
        password: dev_password

chemical:
  database:
    local:
      url: *********************************************************************
      password: dev_local_password
    cloud:
      url: **********************************************************************
      password: dev_cloud_password
  
  export:
    default:
      path: D:\temp\chemical-export
  
  pulsar:
    service:
      url: pulsar://localhost:6650

logging:
  level:
    com.ruoyi.audit: DEBUG
    root: INFO
```

## 3. application-prod.yml 生产环境配置

```yaml
# 生产环境配置
spring:
  datasource:
    druid:
      master:
        url: **********************************************************************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}

chemical:
  database:
    local:
      url: ************************************************************************************************
      username: ${LOCAL_DB_USERNAME}
      password: ${LOCAL_DB_PASSWORD}
    cloud:
      url: ******************************************************************************************
      username: ${CLOUD_DB_USERNAME}
      password: ${CLOUD_DB_PASSWORD}
  
  export:
    default:
      path: /data/chemical-export
    backup:
      path: /data/chemical-backup
  
  pulsar:
    service:
      url: pulsar://pulsar-cluster:6650
  
  monitor:
    alert:
      enabled: true
      email:
        enabled: true
        recipients:
          - <EMAIL>
          - <EMAIL>

logging:
  level:
    com.ruoyi.audit: INFO
    root: WARN
  file:
    name: /var/log/chemical-audit/application.log
```

## 4. 环境变量配置示例

```bash
# 数据库配置
export DB_USERNAME=sa
export DB_PASSWORD=your_secure_password
export LOCAL_DB_USERNAME=local_user
export LOCAL_DB_PASSWORD=local_secure_password
export CLOUD_DB_USERNAME=cloud_user
export CLOUD_DB_PASSWORD=cloud_secure_password

# Pulsar配置
export PULSAR_SERVICE_URL=pulsar://pulsar-cluster:6650
export PULSAR_TOPIC=chemical-data-topic

# 导出路径配置
export CHEMICAL_EXPORT_PATH=/data/chemical-export
export CHEMICAL_BACKUP_PATH=/data/chemical-backup

# JVM配置
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

## 5. Docker配置示例

```yaml
# docker-compose.yml
version: '3.8'
services:
  chemical-audit:
    image: chemical-audit:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
    volumes:
      - /data/chemical-export:/data/chemical-export
      - /var/log/chemical-audit:/var/log/chemical-audit
    depends_on:
      - sqlserver
      - pulsar
    restart: unless-stopped

  sqlserver:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=${DB_PASSWORD}
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql

  pulsar:
    image: apachepulsar/pulsar:2.8.0
    command: bin/pulsar standalone
    ports:
      - "6650:6650"
      - "8080:8080"
    volumes:
      - pulsar_data:/pulsar/data

volumes:
  sqlserver_data:
  pulsar_data:
```

## 6. 配置验证脚本

```bash
#!/bin/bash
# config-check.sh

echo "检查化学审计系统配置..."

# 检查数据库连接
echo "检查数据库连接..."
sqlcmd -S localhost -U sa -P $DB_PASSWORD -Q "SELECT 1" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ 数据库连接正常"
else
    echo "✗ 数据库连接失败"
fi

# 检查Pulsar服务
echo "检查Pulsar服务..."
curl -s http://localhost:8080/admin/v2/clusters > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Pulsar服务正常"
else
    echo "✗ Pulsar服务异常"
fi

# 检查导出目录
echo "检查导出目录..."
if [ -d "$CHEMICAL_EXPORT_PATH" ] && [ -w "$CHEMICAL_EXPORT_PATH" ]; then
    echo "✓ 导出目录可写"
else
    echo "✗ 导出目录不存在或不可写"
fi

echo "配置检查完成"
```
