<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MaterialMapper">
    
    <resultMap type="Material" id="MaterialResult">
        <result property="materialId"    column="material_id"    />
        <result property="materialName"    column="material_name"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="materialModel"    column="material_model"    />
        <result property="materialDescription"    column="material_description"    />
        <result property="attachments"    column="attachments"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="MaterialProcessParamGroupResult" type="Material" extends="MaterialResult">
        <collection property="processParamGroupList" notNullColumn="sub_group_id" javaType="java.util.List" resultMap="ProcessParamGroupResult" />
    </resultMap>

    <resultMap type="ProcessParamGroup" id="ProcessParamGroupResult">
        <result property="groupId"    column="sub_group_id"    />
        <result property="materialId"    column="sub_material_id"    />
        <result property="processType"    column="sub_process_type"    />
        <result property="paramNumber"    column="sub_param_number"    />
        <result property="attachments"    column="sub_attachments"    />
        <result property="remark"    column="sub_remark"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="updateTime"    column="sub_update_time"    />
    </resultMap>

    <sql id="selectMaterialVo">
        select material_id, material_name, supplier_name, material_model, material_description, attachments, remark, create_by, create_time, update_by, update_time from materials
    </sql>

    <select id="selectMaterialList" parameterType="Material" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        <where>  
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="materialModel != null  and materialModel != ''"> and material_model like concat('%', #{materialModel}, '%')</if>
            <if test="materialDescription != null  and materialDescription != ''"> and material_description like concat('%', #{materialDescription}, '%')</if>
        </where>
    </select>
    
    <select id="selectMaterialByMaterialId" parameterType="Long" resultMap="MaterialProcessParamGroupResult">
        select a.material_id, a.material_name, a.supplier_name, a.material_model, a.material_description, a.attachments, a.remark, a.create_by, a.create_time, a.update_by, a.update_time,
 b.group_id as sub_group_id, b.material_id as sub_material_id, b.process_type as sub_process_type, b.param_number as sub_param_number, b.attachments as sub_attachments, b.remark as sub_remark, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time
        from materials a
        left join process_param_group b on b.material_id = a.material_id
        where a.material_id = #{materialId}
    </select>
        
    <insert id="insertMaterial" parameterType="Material" useGeneratedKeys="true" keyProperty="materialId">
        insert into materials
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">material_name,</if>
            <if test="supplierName != null">supplier_name,</if>
            <if test="materialModel != null">material_model,</if>
            <if test="materialDescription != null">material_description,</if>
            <if test="attachments != null">attachments,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">#{materialName},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="materialModel != null">#{materialModel},</if>
            <if test="materialDescription != null">#{materialDescription},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateMaterial" parameterType="Material">
        update materials
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">material_name = #{materialName},</if>
            <if test="supplierName != null">supplier_name = #{supplierName},</if>
            <if test="materialModel != null">material_model = #{materialModel},</if>
            <if test="materialDescription != null">material_description = #{materialDescription},</if>
            attachments = #{attachments},
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where material_id = #{materialId}
    </update>

    <delete id="deleteMaterialByMaterialId" parameterType="Long">
        delete from materials where material_id = #{materialId}
    </delete>

    <delete id="deleteMaterialByMaterialIds" parameterType="String">
        delete from materials where material_id in 
        <foreach item="materialId" collection="array" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </delete>

    <delete id="deleteProcessParamGroupByMaterialIds" parameterType="String">
        delete from process_param_group where material_id in 
        <foreach item="materialId" collection="array" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </delete>

    <delete id="deleteProcessParamGroupByMaterialId" parameterType="Long">
        delete from process_param_group where material_id = #{materialId}
    </delete>

    <insert id="batchProcessParamGroup">
        insert into process_param_group( group_id, material_id, process_type, param_number, attachments, remark, create_by, create_time, update_by, update_time) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.groupId}, #{item.materialId}, #{item.processType}, #{item.paramNumber}, #{item.attachments}, #{item.remark}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
    
    <select id="selectMaterialNameOptions" resultType="String">
        select distinct material_name from materials where material_name is not null and material_name != '' order by material_name
    </select>

    <!-- 获取供应商名称选项 -->
    <select id="selectSupplierNameOptions" resultType="String">
        SELECT DISTINCT supplier_name FROM materials WHERE supplier_name IS NOT NULL AND supplier_name != '' ORDER BY supplier_name
    </select>

    <!-- 获取材料型号选项 -->
    <select id="selectMaterialModelOptions" resultType="String">
        SELECT DISTINCT material_model FROM materials WHERE material_model IS NOT NULL AND material_model != '' ORDER BY material_model
    </select>

    <!-- 根据材料名称查询材料 -->
    <select id="selectMaterialByMaterialName" parameterType="String" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        WHERE material_name = #{materialName} LIMIT 1
    </select>

    <!-- 根据材料名称、供应商、材料型号查询材料 -->
    <select id="selectMaterialByNameSupplierModel" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        WHERE material_name = #{materialName}
        <if test="supplierName != null and supplierName != ''">
            AND supplier_name = #{supplierName}
        </if>
        <if test="materialModel != null and materialModel != ''">
            AND material_model = #{materialModel}
        </if>
        LIMIT 1
    </select>

    <select id="selectMaterialOptions" parameterType="Material" resultType="String">
        SELECT DISTINCT 
        <choose>
            <when test="materialName != null">material_name</when>
            <when test="supplierName != null">supplier_name</when>
            <when test="materialModel != null">material_model</when>
            <otherwise>material_name</otherwise>
        </choose>
        FROM materials 
        WHERE 1=1
        <if test="materialName != null">AND material_name IS NOT NULL</if>
        <if test="supplierName != null">AND supplier_name IS NOT NULL</if>
        <if test="materialModel != null">AND material_model IS NOT NULL</if>
        ORDER BY 1
    </select>

    <select id="selectCompleteExportData" resultType="java.util.Map">
        SELECT 
            m.material_id as materialId,
            m.material_name as materialName,
            m.supplier_name as supplierName,
            m.material_model as materialModel,
            m.material_description as materialDescription,
            m.create_by as materialCreateBy,
            m.create_time as materialCreateTime,
            m.update_by as materialUpdateBy,
            m.update_time as materialUpdateTime,
            ppg.group_id as groupId,
            ppg.process_type as processType,
            ppg.param_number as paramNumber,
            ppg.create_by as groupCreateBy,
            ppg.create_time as groupCreateTime,
            ppg.update_by as groupUpdateBy,
            ppg.update_time as groupUpdateTime,
            ppi.item_id as paramId,
            ppi.param_name as paramName,
            ppi.param_value as paramValue,
            ppi.unit as unit,
            ppi.create_by as paramCreateBy,
            ppi.create_time as paramCreateTime,
            ppi.update_by as paramUpdateBy,
            ppi.update_time as paramUpdateTime
        FROM materials m
        LEFT JOIN process_param_group ppg ON m.material_id = ppg.material_id
        LEFT JOIN process_param_item ppi ON ppg.group_id = ppi.group_id
        WHERE 1=1
        <if test="materialName != null and materialName != ''">
            AND m.material_name like concat('%', #{materialName}, '%')
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND m.supplier_name like concat('%', #{supplierName}, '%')
        </if>
        ORDER BY m.material_id, ppg.group_id, ppi.item_id
    </select>

</mapper>
