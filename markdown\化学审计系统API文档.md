# 化学审计系统API接口文档

## 1. 接口概述

本文档描述了化学审计系统的RESTful API接口，包括数据管理、任务控制、监控、导出等功能模块的接口定义。

### 1.1 基础信息
- **Base URL**: `http://your-domain.com/prod-api`
- **Content-Type**: `application/json`
- **认证方式**: JWT Token
- **字符编码**: UTF-8

### 1.2 通用响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {},
  "rows": [],
  "total": 0
}
```

### 1.3 状态码说明
- `200`: 操作成功
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 2. 化学数据管理接口

### 2.1 查询化学数据列表
**接口地址**: `GET /chemical/data/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 页大小，默认10 |
| examineDate | string | 否 | 检测日期 |
| departmentCode | string | 否 | 部门代码 |
| processName | string | 否 | 工艺名称 |
| productName | string | 否 | 产品名称 |
| testName | string | 否 | 测试名称 |
| layerNumber | string | 否 | 层号 |
| isExported | boolean | 否 | 是否已导出 |
| notProcess | boolean | 否 | 是否不处理 |
| beginTime | string | 否 | 开始时间 |
| endTime | string | 否 | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": "CHM_20240101_001",
      "examineDate": "2024-01-01 10:30:00",
      "shift": "白班",
      "staff": "张三",
      "departmentCode": "G2-001",
      "departmentName": "G2生产部",
      "processName": "微蚀",
      "productName": "PD全线",
      "testName": "微蚀量",
      "layerNumber": "L1",
      "upperLimit": "100",
      "medianSpecification": "50",
      "downLimit": "0",
      "examine1": "45.5",
      "examine2": "46.2",
      "isExported": false,
      "notProcess": false,
      "insertionTime": "2024-01-01 10:35:00",
      "createTime": "2024-01-01 10:35:00",
      "updateTime": "2024-01-01 10:35:00"
    }
  ],
  "total": 1
}
```

### 2.2 获取化学数据详情
**接口地址**: `GET /chemical/data/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 数据ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": "CHM_20240101_001",
    "examineDate": "2024-01-01 10:30:00",
    // ... 其他字段
  }
}
```

### 2.3 新增化学数据
**接口地址**: `POST /chemical/data`

**请求体**:
```json
{
  "id": "CHM_20240101_002",
  "examineDate": "2024-01-01 11:00:00",
  "shift": "白班",
  "staff": "李四",
  "departmentCode": "G2-001",
  "processName": "微蚀",
  "productName": "PD全线",
  "testName": "微蚀量",
  "layerNumber": "L2",
  "upperLimit": "100",
  "medianSpecification": "50",
  "downLimit": "0",
  "examine1": "48.5",
  "examine2": "49.2"
}
```

### 2.4 修改化学数据
**接口地址**: `PUT /chemical/data`

**请求体**: 同新增接口，需包含id字段

### 2.5 删除化学数据
**接口地址**: `DELETE /chemical/data/{ids}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | string | 是 | 数据ID，多个用逗号分隔 |

### 2.6 批量操作接口

#### 2.6.1 标记为已导出
**接口地址**: `PUT /chemical/data/markAsExported`

**请求体**:
```json
["CHM_20240101_001", "CHM_20240101_002"]
```

#### 2.6.2 标记为不处理
**接口地址**: `PUT /chemical/data/markAsNotProcess`

#### 2.6.3 重置处理状态
**接口地址**: `PUT /chemical/data/resetProcessStatus`

#### 2.6.4 重新处理数据
**接口地址**: `POST /chemical/data/reprocess`

### 2.7 获取数据统计
**接口地址**: `GET /chemical/data/statistics`

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "totalCount": 10000,
    "unprocessedCount": 150,
    "exportedCount": 8500,
    "todayNewCount": 25,
    "processRate": 98.5,
    "exportRate": 85.0,
    "success": true,
    "message": "统计信息获取成功"
  }
}
```

## 3. 任务控制接口

### 3.1 启动数据读取任务
**接口地址**: `POST /chemical/task/start`

**响应示例**:
```json
{
  "code": 200,
  "msg": "任务启动成功",
  "data": {
    "success": true,
    "taskId": 123,
    "message": "数据读取任务已启动"
  }
}
```

### 3.2 暂停数据读取任务
**接口地址**: `POST /chemical/task/pause`

### 3.3 停止数据读取任务
**接口地址**: `POST /chemical/task/stop`

### 3.4 重启数据读取任务
**接口地址**: `POST /chemical/task/restart`

### 3.5 获取任务状态
**接口地址**: `GET /chemical/task/status`

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "dataReading": {
      "status": "RUNNING",
      "taskName": "Pulsar数据读取",
      "startTime": "2024-01-01 10:00:00",
      "processedRecords": 1000,
      "successRecords": 995,
      "errorRecords": 5
    },
    "dataProcessing": {
      "status": "STOPPED",
      "taskName": "化学数据处理",
      "progressPercent": 0
    },
    "logProcessing": {
      "status": "STOPPED",
      "taskName": "日志数据处理",
      "totalRecords": 0
    }
  }
}
```

### 3.6 启动数据处理任务
**接口地址**: `POST /chemical/task/startProcessing`

### 3.7 启动日志处理任务
**接口地址**: `POST /chemical/task/startLogProcessing`

### 3.8 获取实时日志
**接口地址**: `GET /chemical/task/logs`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lastLogId | long | 否 | 最后日志ID，默认0 |

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "logs": [
      {
        "id": 1001,
        "timestamp": "2024-01-01 10:30:15",
        "level": "INFO",
        "message": "成功处理化学数据，ID: CHM_20240101_001"
      },
      {
        "id": 1002,
        "timestamp": "2024-01-01 10:30:20",
        "level": "ERROR",
        "message": "数据处理失败，ID: CHM_20240101_002，错误: 连接超时"
      }
    ],
    "hasMore": true
  }
}
```

### 3.9 Pulsar客户端管理

#### 3.9.1 初始化Pulsar客户端
**接口地址**: `POST /chemical/task/initPulsar`

#### 3.9.2 关闭Pulsar客户端
**接口地址**: `POST /chemical/task/closePulsar`

#### 3.9.3 检查Pulsar连接状态
**接口地址**: `GET /chemical/task/pulsarStatus`

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "connected": true,
    "receivedMessages": 1500,
    "processedMessages": 1495,
    "failedMessages": 5,
    "lastMessageTime": "2024-01-01 10:30:25",
    "consumerName": "G2-Chemical-Web-001"
  }
}
```

## 4. 数据导出接口

### 4.1 导出CSV文件
**接口地址**: `POST /chemical/export/csv`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endDate | string | 是 | 结束时间，格式：yyyy-MM-dd HH:mm:ss |
| layerNumbers | array | 否 | 层号数组 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "导出成功",
  "data": {
    "success": true,
    "exportId": 1001,
    "fileName": "chemical_data_20240101.csv",
    "filePath": "/export/chemical_data_20240101.csv",
    "fileSize": 1024000,
    "recordCount": 500,
    "message": "CSV文件导出成功"
  }
}
```

### 4.2 导出Excel文件
**接口地址**: `POST /chemical/export/excel`

**请求参数**: 同CSV导出

### 4.3 获取导出历史记录
**接口地址**: `GET /chemical/export/history`

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "records": [
      {
        "exportId": 1001,
        "exportName": "化学数据导出_20240101",
        "exportType": "CSV",
        "fileName": "chemical_data_20240101.csv",
        "fileSize": 1024000,
        "totalRecords": 500,
        "exportStatus": "SUCCESS",
        "createTime": "2024-01-01 10:30:00"
      }
    ],
    "total": 1
  }
}
```

### 4.4 下载导出文件
**接口地址**: `GET /chemical/export/download/{exportId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| exportId | long | 是 | 导出记录ID |

**响应**: 文件流

### 4.5 预览导出数据
**接口地址**: `GET /chemical/export/preview`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 开始时间 |
| endDate | string | 是 | 结束时间 |
| layerNumbers | array | 否 | 层号数组 |
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 页大小，默认10 |

## 5. 监控接口

### 5.1 获取系统概览
**接口地址**: `GET /chemical/monitor/overview`

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "dataStatistics": {
      "totalCount": 10000,
      "unprocessedCount": 150,
      "exportedCount": 8500,
      "todayNewCount": 25,
      "processRate": 98.5,
      "exportRate": 85.0
    },
    "taskStatus": {
      "dataReading": "RUNNING",
      "dataProcessing": "STOPPED",
      "logProcessing": "STOPPED"
    },
    "systemStatus": {
      "cpuUsage": 45.2,
      "memoryUsage": 68.5,
      "diskUsage": 35.8
    },
    "pulsarStatus": {
      "connected": true,
      "receivedMessages": 1500,
      "processedMessages": 1495
    }
  }
}
```

### 5.2 获取实时数据
**接口地址**: `GET /chemical/monitor/realtime`

### 5.3 获取任务监控列表
**接口地址**: `GET /chemical/monitor/tasks`

### 5.4 获取系统性能指标
**接口地址**: `GET /chemical/monitor/performance`

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "memoryUsage": {
      "total": 2048,
      "used": 1400,
      "free": 648,
      "usage": 68.5
    },
    "cpuUsage": {
      "usage": 45.2,
      "cores": 4
    },
    "diskUsage": {
      "total": 102400,
      "used": 36659,
      "free": 65741,
      "usage": 35.8
    },
    "networkStatus": {
      "connected": true,
      "latency": 15
    }
  }
}
```

### 5.5 获取数据处理趋势
**接口地址**: `GET /chemical/monitor/trends`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| days | int | 否 | 天数，默认7 |

### 5.6 获取错误统计
**接口地址**: `GET /chemical/monitor/errors`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| hours | int | 否 | 小时数，默认24 |

## 6. 规则管理接口

### 6.1 查询处理规则列表
**接口地址**: `GET /chemical/rule/list`

### 6.2 新增处理规则
**接口地址**: `POST /chemical/rule`

### 6.3 修改处理规则
**接口地址**: `PUT /chemical/rule`

### 6.4 删除处理规则
**接口地址**: `DELETE /chemical/rule/{ruleIds}`

### 6.5 根据条件查询规则
**接口地址**: `GET /chemical/rule/findByNames`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productName | string | 是 | 产品名称 |
| processName | string | 是 | 工艺名称 |
| testName | string | 是 | 测试名称 |

## 7. 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 数据库连接失败 | 检查数据库配置和网络连接 |
| 1002 | Pulsar连接失败 | 检查Pulsar服务状态和配置 |
| 1003 | 数据处理失败 | 查看详细错误信息和日志 |
| 1004 | 文件导出失败 | 检查文件路径权限和磁盘空间 |
| 1005 | 参数验证失败 | 检查请求参数格式和必填项 |

## 8. 接口调用示例

### 8.1 JavaScript示例
```javascript
// 获取化学数据列表
async function getChemicalList() {
  const response = await fetch('/prod-api/chemical/data/list?pageNum=1&pageSize=10', {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  if (data.code === 200) {
    console.log('数据获取成功:', data.rows);
  } else {
    console.error('数据获取失败:', data.msg);
  }
}

// 启动任务
async function startTask() {
  const response = await fetch('/prod-api/chemical/task/start', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    }
  });
  
  const result = await response.json();
  if (result.code === 200) {
    console.log('任务启动成功:', result.data);
  } else {
    console.error('任务启动失败:', result.msg);
  }
}
```

### 8.2 cURL示例
```bash
# 获取化学数据列表
curl -X GET "http://your-domain.com/prod-api/chemical/data/list?pageNum=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 启动任务
curl -X POST "http://your-domain.com/prod-api/chemical/task/start" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 导出CSV文件
curl -X POST "http://your-domain.com/prod-api/chemical/export/csv" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-01-01 00:00:00",
    "endDate": "2024-01-31 23:59:59",
    "layerNumbers": ["L1", "L2"]
  }'
```

---

**注意**: 所有接口都需要有效的JWT Token进行认证，请确保在请求头中包含正确的Authorization信息。
