package com.ruoyi.audit.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.pulsar.client.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.audit.config.PulsarConfig;
import com.ruoyi.audit.domain.Chemical;
import com.ruoyi.audit.service.IChemicalService;

/**
 * Pulsar服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class PulsarServiceImpl 
{
    private static final Logger log = LoggerFactory.getLogger(PulsarServiceImpl.class);

    @Autowired
    private PulsarClient pulsarClient;

    @Autowired
    private PulsarConfig pulsarConfig;

    @Autowired
    private IChemicalService chemicalService;

    private Consumer<String> consumer;
    private Producer<String> producer;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 初始化Pulsar消费者
     */
    public boolean initializeConsumer() 
    {
        try {
            if (consumer != null) {
                consumer.close();
            }

            consumer = pulsarClient.newConsumer(Schema.STRING)
                    .topic(pulsarConfig.getTopic())
                    .subscriptionName(pulsarConfig.getSubscription())
                    .subscriptionType(SubscriptionType.Shared)
                    .receiverQueueSize(pulsarConfig.getReceiverQueueSize())
                    .maxTotalReceiverQueueSizeAcrossPartitions(pulsarConfig.getMaxTotalReceiverQueueSizeAcrossPartitions())
                    .subscribe();

            log.info("Pulsar消费者初始化成功，主题: {}, 订阅: {}", 
                    pulsarConfig.getTopic(), pulsarConfig.getSubscription());
            return true;

        } catch (PulsarClientException e) {
            log.error("Pulsar消费者初始化失败", e);
            return false;
        }
    }

    /**
     * 初始化Pulsar生产者
     */
    public boolean initializeProducer() 
    {
        try {
            if (producer != null) {
                producer.close();
            }

            producer = pulsarClient.newProducer(Schema.STRING)
                    .topic(pulsarConfig.getTopic() + "-result")
                    .sendTimeout(pulsarConfig.getSendTimeoutMs(), TimeUnit.MILLISECONDS)
                    .blockIfQueueFull(pulsarConfig.isBlockIfQueueFull())
                    .create();

            log.info("Pulsar生产者初始化成功，主题: {}", pulsarConfig.getTopic() + "-result");
            return true;

        } catch (PulsarClientException e) {
            log.error("Pulsar生产者初始化失败", e);
            return false;
        }
    }

    /**
     * 启动消息消费
     */
    public boolean startConsuming() 
    {
        if (isRunning.get()) {
            log.warn("Pulsar消费者已在运行中");
            return false;
        }

        if (consumer == null && !initializeConsumer()) {
            return false;
        }

        isRunning.set(true);

        // 启动消费线程
        Thread consumerThread = new Thread(() -> {
            log.info("开始消费Pulsar消息");
            
            while (isRunning.get()) {
                try {
                    Message<String> message = consumer.receive(1, TimeUnit.SECONDS);
                    if (message != null) {
                        processMessage(message);
                        consumer.acknowledge(message);
                    }
                } catch (PulsarClientException e) {
                    if (isRunning.get()) {
                        log.error("接收Pulsar消息失败", e);
                    }
                } catch (Exception e) {
                    log.error("处理Pulsar消息失败", e);
                }
            }
            
            log.info("Pulsar消息消费已停止");
        });

        consumerThread.setName("pulsar-consumer-thread");
        consumerThread.setDaemon(true);
        consumerThread.start();

        return true;
    }

    /**
     * 停止消息消费
     */
    public boolean stopConsuming() 
    {
        isRunning.set(false);
        
        try {
            if (consumer != null) {
                consumer.close();
                consumer = null;
            }
            
            if (producer != null) {
                producer.close();
                producer = null;
            }
            
            log.info("Pulsar消费者已停止");
            return true;
            
        } catch (PulsarClientException e) {
            log.error("停止Pulsar消费者失败", e);
            return false;
        }
    }

    /**
     * 处理接收到的消息
     */
    private void processMessage(Message<String> message) 
    {
        try {
            String messageData = message.getValue();
            log.debug("接收到Pulsar消息: {}", messageData);

            // 解析JSON消息为Chemical对象
            Chemical chemical = objectMapper.readValue(messageData, Chemical.class);
            
            // 验证必要字段
            if (chemical.getId() == null || chemical.getId().trim().isEmpty()) {
                log.warn("消息缺少ID字段，跳过处理: {}", messageData);
                return;
            }

            // 处理化学数据
            int result = chemicalService.insertChemical(chemical);
            if (result > 0) {
                log.debug("成功处理化学数据: {}", chemical.getId());
            } else {
                log.warn("处理化学数据失败: {}", chemical.getId());
            }

        } catch (Exception e) {
            log.error("处理Pulsar消息失败: {}", message.getValue(), e);
        }
    }

    /**
     * 发送消息
     */
    public boolean sendMessage(String message) 
    {
        try {
            if (producer == null && !initializeProducer()) {
                return false;
            }

            MessageId messageId = producer.send(message);
            log.debug("成功发送Pulsar消息: {}", messageId);
            return true;

        } catch (PulsarClientException e) {
            log.error("发送Pulsar消息失败", e);
            return false;
        }
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() 
    {
        try {
            return pulsarClient != null && !pulsarClient.isClosed();
        } catch (Exception e) {
            log.error("检查Pulsar连接状态失败", e);
            return false;
        }
    }

    /**
     * 检查消费者状态
     */
    public boolean isConsuming() 
    {
        return isRunning.get() && consumer != null && consumer.isConnected();
    }

    /**
     * 获取消费者统计信息
     */
    public Map<String, Object> getConsumerStats()
    {
        Map<String, Object> stats = new HashMap<>();

        if (consumer != null && consumer.isConnected()) {
            stats.put("connected", true);
            stats.put("topic", consumer.getTopic());
            stats.put("subscription", consumer.getSubscription());
        } else {
            stats.put("connected", false);
        }

        return stats;
    }

    /**
     * 获取生产者统计信息
     */
    public Map<String, Object> getProducerStats()
    {
        Map<String, Object> stats = new HashMap<>();

        if (producer != null && producer.isConnected()) {
            stats.put("connected", true);
            stats.put("topic", producer.getTopic());
        } else {
            stats.put("connected", false);
        }

        return stats;
    }
}
