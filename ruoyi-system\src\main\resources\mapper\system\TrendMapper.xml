<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TrendMapper">

    <select id="selectTrendData" resultType="java.util.HashMap">
        SELECT 
            ppi.param_number as paramNumber,
            tp.performance_name as performanceName,
            tr.test_value as testValue,
            tr.unit as unit,
            tr.test_date as testDate,
            m.material_name as materialName,
            ppg.process_type as processType,
            ppi.param_name as paramName,
            ppi.param_value as paramValue
        FROM test_results tr
        LEFT JOIN process_param_item ppi ON tr.param_id = ppi.param_id
        LEFT JOIN process_param_group ppg ON ppi.group_id = ppg.group_id
        LEFT JOIN materials m ON ppg.material_id = m.material_id
        LEFT JOIN test_plans tp ON tr.plan_id = tp.test_plan_id
        WHERE 1=1
        <if test="paramNumbers != null and paramNumbers.length > 0">
            AND ppi.param_number IN
            <foreach item="item" index="index" collection="paramNumbers" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="performanceNames != null and performanceNames.length > 0">
            AND tp.performance_name IN
            <foreach item="item" index="index" collection="performanceNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY ppi.param_number, tr.test_date
    </select>

    <select id="selectParamNumbers" resultType="String">
        SELECT DISTINCT param_number 
        FROM process_param_item 
        WHERE param_number IS NOT NULL 
        ORDER BY param_number
    </select>

    <select id="selectPerformanceNames" resultType="String">
        SELECT DISTINCT performance_name 
        FROM test_plans 
        WHERE performance_name IS NOT NULL 
        ORDER BY performance_name
    </select>

    <select id="selectParamDetails" resultType="java.util.HashMap">
        SELECT 
            ppi.param_number as paramNumber,
            ppi.param_name as paramName,
            ppi.param_value as paramValue,
            ppi.unit as unit,
            ppg.process_type as processType,
            m.material_name as materialName,
            m.supplier_name as supplierName,
            ppi.remark as remark
        FROM process_param_item ppi
        LEFT JOIN process_param_group ppg ON ppi.group_id = ppg.group_id
        LEFT JOIN materials m ON ppg.material_id = m.material_id
        WHERE ppi.param_number = #{paramNumber}
    </select>

</mapper>