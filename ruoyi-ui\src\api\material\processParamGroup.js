import request from '@/utils/request'

// 查询工艺参数组列表
export function listProcessParamGroup(query) {
  return request({
    url: '/material/processParamGroup/list',
    method: 'get',
    params: query
  })
}

// 根据材料ID查询工艺参数组列表
export function listByMaterialId(materialId) {
  return request({
    url: '/material/processParamGroup/listByMaterialId/' + materialId,
    method: 'get'
  })
}

// 查询工艺参数组详细
export function getProcessParamGroup(groupId) {
  return request({
    url: '/material/processParamGroup/' + groupId,
    method: 'get'
  })
}

// 新增工艺参数组
export function addProcessParamGroup(data) {
  // 处理附件数据
  if (data.attachmentList && Array.isArray(data.attachmentList)) {
    data.attachments = JSON.stringify(data.attachmentList);
    delete data.attachmentList;
  }
  return request({
    url: '/material/processParamGroup',
    method: 'post',
    data: data
  })
}

// 修改工艺参数组
export function updateProcessParamGroup(data) {
  // 处理附件数据
  if (data.attachmentList && Array.isArray(data.attachmentList)) {
    data.attachments = JSON.stringify(data.attachmentList);
    delete data.attachmentList;
  }
  return request({
    url: '/material/processParamGroup',
    method: 'put',
    data: data
  })
}

// 删除工艺参数组
export function delProcessParamGroup(groupId) {
  return request({
    url: '/material/processParamGroup/' + groupId,
    method: 'delete'
  })
}


// 导出工艺参数组
export function exportProcessParamGroup(query) {
  return request({
    url: '/material/processParamGroup/export',
    method: 'post',
    data: query
  })
}

// 获取工艺参数组选项数据
export function getProcessParamGroupOptions(query) {
  return request({
    url: '/material/processParamGroup/options',
    method: 'get',
    params: query
  })
}



// 整体导出数据
export function exportCompleteData(query) {
  return request({
    url: '/material/material/exportComplete',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}
