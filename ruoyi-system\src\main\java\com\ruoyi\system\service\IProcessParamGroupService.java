package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.domain.ProcessParamGroup;

/**
 * 工艺参数组Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IProcessParamGroupService 
{
    /**
     * 查询工艺参数组
     * 
     * @param groupId 工艺参数组主键
     * @return 工艺参数组
     */
    public ProcessParamGroup selectProcessParamGroupByGroupId(Long groupId);

    /**
     * 查询工艺参数组列表
     * 
     * @param processParamGroup 工艺参数组
     * @return 工艺参数组集合
     */
    public List<ProcessParamGroup> selectProcessParamGroupList(ProcessParamGroup processParamGroup);

    /**
     * 根据材料ID查询工艺参数组列表
     * 
     * @param materialId 材料ID
     * @return 工艺参数组集合
     */
    public List<ProcessParamGroup> selectProcessParamGroupByMaterialId(Long materialId);

    /**
     * 新增工艺参数组
     * 
     * @param processParamGroup 工艺参数组
     * @return 结果
     */
    public int insertProcessParamGroup(ProcessParamGroup processParamGroup);

    /**
     * 修改工艺参数组
     * 
     * @param processParamGroup 工艺参数组
     * @return 结果
     */
    public int updateProcessParamGroup(ProcessParamGroup processParamGroup);

    /**
    /**
     * 批量删除工艺参数组
     *
     * @param groupIds 需要删除的工艺参数组主键集合
     * @return 结果
     */
    public int deleteProcessParamGroupByGroupIds(Long[] groupIds);


    /**
     * 删除工艺参数组信息
     *
     * @param groupId 工艺参数组主键
     * @return 结果
     */
    public int deleteProcessParamGroupByGroupId(Long groupId);
    
    /**
     * 获取工艺类型选项
     *
     * @return 工艺类型列表
     */
    public List<String> selectProcessTypeOptions();
    
    /**
     * 根据材料ID获取完整数据用于导出
     *
     * @param materialId 材料ID
     * @return 完整数据列表
     */
    public List<Map<String, Object>> selectCompleteDataByMaterialId(Long materialId);

    /**
     * 获取工艺参数组选项数据
     * 
     * @param type 选项类型
     * @return 选项列表
     */
    public List<String> selectProcessParamGroupOptions(String type);
}
