# 化学审计系统测试指南

## 测试前准备

### 1. 环境配置
确保以下配置正确：

```properties
# application.yml 或 application.properties
chemical:
  database:
    local:
      url: *****************************************************************
      username: sa
      password: your_password
    cloud:
      url: ************************************************************************************************************
      username: hhh
      password: root1234
  pulsar:
    service:
      url: pulsar://localhost:6650
    consumer:
      subscription: chemical-audit-subscription
      topic: chemical-data-topic
```

### 2. 数据库准备
- 确保本地SQL Server数据库运行正常
- 执行SQL脚本创建必要的表结构
- 插入一些测试数据

### 3. Pulsar服务（可选）
如果要测试Pulsar功能：
```bash
# 启动Pulsar服务
docker run -it -p 6650:6650 -p 8080:8080 apachepulsar/pulsar:latest bin/pulsar standalone
```

## 功能测试

### 1. 数据读取任务测试

**测试接口：**
```
POST /audit/chemical/task/start-data-reading
Content-Type: application/json

{
    "layerNumbers": ["L1", "L2"],
    "startDate": "2024-01-01",
    "endDate": "2024-01-31"
}
```

**预期结果：**
- 返回成功状态
- 任务开始运行
- 可以通过状态查询接口查看进度

### 2. 数据刷新功能测试

**测试接口：**
```
POST /audit/chemical/refresh/execute
Content-Type: application/json

{
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "layerNumbers": ["L1"],
    "exportPath": "/tmp/export"
}
```

**预期结果：**
- 数据被正确刷新
- examine1_zs字段保存原始值
- examine1字段保存调整后的值

### 3. 数据导出功能测试

**CSV导出测试：**
```
POST /audit/chemical/export/csv
Content-Type: application/json

{
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "layerNumbers": ["L1", "L2"],
    "exportPath": "/tmp/export"
}
```

**Excel导出测试：**
```
POST /audit/chemical/export/excel
Content-Type: application/json

{
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "layerNumbers": ["L1", "L2"],
    "exportPath": "/tmp/export"
}
```

### 4. 任务状态查询测试

**查询任务状态：**
```
GET /audit/chemical/task/status
```

**查询Pulsar状态：**
```
GET /audit/chemical/task/pulsar-status
```

## 单元测试示例

### 1. 数据刷新算法测试

```java
@Test
public void testDataRefreshAlgorithm() {
    // 准备测试数据
    ChemicalRefreshTask task = new ChemicalRefreshTask();
    task.setStartDate(Date.from(LocalDate.of(2024, 1, 1).atStartOfDay(ZoneId.systemDefault()).toInstant()));
    task.setEndDate(Date.from(LocalDate.of(2024, 1, 31).atStartOfDay(ZoneId.systemDefault()).toInstant()));
    task.setLayerNumbers(Arrays.asList("L1"));
    task.setExportPath("/tmp/test");

    // 执行测试
    Map<String, Object> result = chemicalRefreshService.executeRefresh(task);

    // 验证结果
    assertTrue((Boolean) result.get("success"));
    assertNotNull(result.get("modifiedRecords"));
}
```

### 2. 导出功能测试

```java
@Test
public void testCsvExport() {
    // 准备测试数据
    Map<String, Object> params = new HashMap<>();
    params.put("startDate", "2024-01-01");
    params.put("endDate", "2024-01-31");
    params.put("layerNumbers", Arrays.asList("L1"));
    params.put("exportPath", "/tmp/test");

    // 执行导出
    Map<String, Object> result = chemicalExportService.exportToCsv(params);

    // 验证结果
    assertTrue((Boolean) result.get("success"));
    assertNotNull(result.get("filePath"));
    
    // 验证文件是否存在
    String filePath = (String) result.get("filePath");
    assertTrue(new File(filePath).exists());
}
```

### 3. Pulsar服务测试

```java
@Test
public void testPulsarConnection() {
    // 测试连接
    boolean connected = pulsarService.isConnected();
    assertTrue(connected);

    // 测试消息发送
    boolean sent = pulsarService.sendMessage("{\"id\":\"test\",\"examine1\":\"1.23\"}");
    assertTrue(sent);
}
```

## 性能测试

### 1. 大数据量处理测试

```java
@Test
public void testLargeDataProcessing() {
    // 插入大量测试数据（如10000条）
    List<Chemical> testData = generateTestData(10000);
    
    // 记录开始时间
    long startTime = System.currentTimeMillis();
    
    // 执行数据处理
    Map<String, Object> result = chemicalTaskService.startDataProcessingTask();
    
    // 等待处理完成
    while (chemicalTaskService.isTaskRunning()) {
        Thread.sleep(1000);
    }
    
    // 记录结束时间
    long endTime = System.currentTimeMillis();
    
    // 验证性能
    long duration = endTime - startTime;
    assertTrue("处理时间应小于60秒", duration < 60000);
}
```

### 2. 并发测试

```java
@Test
public void testConcurrentExport() throws InterruptedException {
    int threadCount = 5;
    CountDownLatch latch = new CountDownLatch(threadCount);
    List<Future<Boolean>> futures = new ArrayList<>();

    ExecutorService executor = Executors.newFixedThreadPool(threadCount);

    for (int i = 0; i < threadCount; i++) {
        Future<Boolean> future = executor.submit(() -> {
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("startDate", "2024-01-01");
                params.put("endDate", "2024-01-31");
                params.put("exportPath", "/tmp/test_" + Thread.currentThread().getId());

                Map<String, Object> result = chemicalExportService.exportToCsv(params);
                return (Boolean) result.get("success");
            } finally {
                latch.countDown();
            }
        });
        futures.add(future);
    }

    latch.await(60, TimeUnit.SECONDS);

    // 验证所有任务都成功
    for (Future<Boolean> future : futures) {
        assertTrue(future.get());
    }

    executor.shutdown();
}
```

## 集成测试

### 1. 完整流程测试

```java
@Test
public void testCompleteWorkflow() {
    // 1. 启动数据读取任务
    Map<String, Object> startResult = chemicalTaskService.startDataReadingTask();
    assertTrue((Boolean) startResult.get("success"));

    // 2. 等待一些数据被处理
    Thread.sleep(5000);

    // 3. 执行数据刷新
    ChemicalRefreshTask refreshTask = new ChemicalRefreshTask();
    refreshTask.setStartDate(new Date());
    refreshTask.setEndDate(new Date());
    refreshTask.setLayerNumbers(Arrays.asList("L1"));
    
    Map<String, Object> refreshResult = chemicalRefreshService.executeRefresh(refreshTask);
    assertTrue((Boolean) refreshResult.get("success"));

    // 4. 导出数据
    Map<String, Object> exportParams = new HashMap<>();
    exportParams.put("startDate", "2024-01-01");
    exportParams.put("endDate", "2024-01-31");
    exportParams.put("exportPath", "/tmp/integration_test");

    Map<String, Object> exportResult = chemicalExportService.exportToCsv(exportParams);
    assertTrue((Boolean) exportResult.get("success"));

    // 5. 停止任务
    Map<String, Object> stopResult = chemicalTaskService.stopDataReadingTask();
    assertTrue((Boolean) stopResult.get("success"));
}
```

## 错误场景测试

### 1. 数据库连接失败测试
### 2. 无效数据处理测试
### 3. 文件权限错误测试
### 4. 内存不足测试

## 测试数据清理

测试完成后，记得清理测试数据：

```sql
-- 清理测试数据
DELETE FROM chemical WHERE id LIKE 'test_%';
DELETE FROM chemical_ys WHERE id LIKE 'test_%';

-- 清理导出文件
-- 删除 /tmp/test* 目录下的文件
```

## 注意事项

1. **测试环境隔离**：确保测试不会影响生产数据
2. **资源清理**：每次测试后清理临时文件和测试数据
3. **异常处理**：测试各种异常情况的处理
4. **性能监控**：关注内存使用和处理速度
5. **日志检查**：检查日志中是否有错误信息

## 自动化测试

建议使用Maven或Gradle配置自动化测试：

```xml
<plugin>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-maven-plugin</artifactId>
    <configuration>
        <profiles>
            <profile>test</profile>
        </profiles>
    </configuration>
</plugin>
```

运行测试：
```bash
mvn test
# 或
./gradlew test
```
