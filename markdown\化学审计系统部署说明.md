# 化学审计系统部署说明

## 1. 系统概述

化学审计系统是基于若依框架开发的Web应用，用于替代原有的JavaFX桌面应用程序。系统主要功能包括：
- 从Pulsar消息队列接收化学检测数据
- 对数据进行处理和调整
- 提供数据查询、导出、监控等功能

## 2. 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │  后端 (Spring)   │    │  数据库 (SQL)    │
│                 │    │                 │    │                 │
│ - 数据监控页面   │◄──►│ - RESTful API   │◄──►│ - chemical      │
│ - 任务控制页面   │    │ - 业务逻辑处理   │    │ - chemical_ys   │
│ - 数据管理页面   │    │ - 数据处理服务   │    │ - chemical_rule │
│ - 导出功能页面   │    │ - Pulsar集成    │    │ - 监控表        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Pulsar消息队列   │
                       │                 │
                       │ - 数据接收       │
                       │ - 消息处理       │
                       └─────────────────┘
```

## 3. 环境要求

### 3.1 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **磁盘**: 100GB以上可用空间
- **网络**: 稳定的网络连接

### 3.2 软件要求
- **操作系统**: Windows Server 2016+ 或 Linux CentOS 7+
- **JDK**: 1.8或以上版本
- **Node.js**: 12.0或以上版本
- **SQL Server**: 2012或以上版本
- **Apache Pulsar**: 2.8或以上版本
- **Web服务器**: Nginx 1.16+ 或 Apache 2.4+

## 4. 安装步骤

### 4.1 安装JDK
```bash
# Windows
# 下载并安装Oracle JDK 1.8
# 配置JAVA_HOME环境变量

# Linux
sudo yum install java-1.8.0-openjdk-devel
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
```

### 4.2 安装Node.js
```bash
# Windows
# 从官网下载并安装Node.js

# Linux
curl -sL https://rpm.nodesource.com/setup_14.x | sudo bash -
sudo yum install nodejs
```

### 4.3 安装SQL Server
```sql
-- 1. 安装SQL Server 2019
-- 2. 创建数据库实例
-- 3. 配置网络协议和端口
-- 4. 创建登录用户和权限
```

### 4.4 安装Apache Pulsar
```bash
# 下载Pulsar
wget https://archive.apache.org/dist/pulsar/pulsar-2.8.0/apache-pulsar-2.8.0-bin.tar.gz

# 解压
tar -xzf apache-pulsar-2.8.0-bin.tar.gz

# 启动Pulsar
cd apache-pulsar-2.8.0
bin/pulsar standalone
```

## 5. 数据库配置

### 5.1 创建数据库
```sql
-- 连接到SQL Server
-- 创建数据库
CREATE DATABASE [g2-qis]
COLLATE Chinese_PRC_CI_AS;

-- 创建用户
CREATE LOGIN [chemical_user] WITH PASSWORD = 'YourPassword123!';
USE [g2-qis];
CREATE USER [chemical_user] FOR LOGIN [chemical_user];
ALTER ROLE db_datareader ADD MEMBER [chemical_user];
ALTER ROLE db_datawriter ADD MEMBER [chemical_user];
ALTER ROLE db_ddladmin ADD MEMBER [chemical_user];
```

### 5.2 执行初始化脚本
```bash
# 1. 执行表结构脚本
sqlcmd -S localhost -d g2-qis -i sql/chemical_audit_tables.sql

# 2. 执行菜单权限脚本
sqlcmd -S localhost -d g2-qis -i sql/chemical_audit_menu.sql
```

## 6. 后端部署

### 6.1 配置文件修改
编辑 `ruoyi-admin/src/main/resources/application.yml`:

```yaml
# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      master:
        url: *****************************************************************
        username: chemical_user
        password: YourPassword123!

# Pulsar配置
pulsar:
  broker:
    url: pulsar://localhost:6650
  auth:
    token: eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzcGMtMzMxIn0.U7QrwJTShmbcucMtIqqzC5gfK8mbgsSAF20yyed1W6A
  topic:
    medicine: persistent://spc/331-interface/labMedicineData
  subscription:
    name: G2-Chemical-Web

# 文件上传路径
ruoyi:
  profile: /opt/ruoyi/uploadPath
```

### 6.2 编译和打包
```bash
# 进入项目根目录
cd /path/to/RuoYi-Vue

# 编译打包
mvn clean package -Dmaven.test.skip=true

# 生成的jar文件位于
# ruoyi-admin/target/ruoyi-admin.jar
```

### 6.3 启动服务
```bash
# 创建启动脚本
cat > start.sh << 'EOF'
#!/bin/bash
JAVA_OPTS="-Xms512m -Xmx2048m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
nohup java $JAVA_OPTS -jar ruoyi-admin.jar > application.log 2>&1 &
echo $! > application.pid
EOF

chmod +x start.sh
./start.sh
```

### 6.4 创建系统服务（可选）
```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/chemical-audit.service > /dev/null <<EOF
[Unit]
Description=Chemical Audit System
After=network.target

[Service]
Type=forking
User=ruoyi
ExecStart=/opt/chemical-audit/start.sh
ExecStop=/bin/kill -TERM \$MAINPID
PIDFile=/opt/chemical-audit/application.pid
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
sudo systemctl enable chemical-audit
sudo systemctl start chemical-audit
```

## 7. 前端部署

### 7.1 安装依赖
```bash
cd ruoyi-ui
npm install
```

### 7.2 修改配置
编辑 `.env.production`:
```bash
# 生产环境配置
ENV = 'production'

# 若依管理系统/生产环境
VUE_APP_BASE_API = '/prod-api'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true
```

### 7.3 构建生产版本
```bash
npm run build:prod
```

### 7.4 部署到Web服务器

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /opt/chemical-audit/dist;
    index index.html;

    # 前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

## 8. Pulsar配置

### 8.1 创建命名空间和主题
```bash
# 创建租户
bin/pulsar-admin tenants create spc

# 创建命名空间
bin/pulsar-admin namespaces create spc/331-interface

# 创建主题
bin/pulsar-admin topics create persistent://spc/331-interface/labMedicineData

# 创建订阅
bin/pulsar-admin topics create-subscription \
  --subscription G2-Chemical-Web \
  persistent://spc/331-interface/labMedicineData
```

### 8.2 配置认证（可选）
```bash
# 生成密钥对
bin/pulsar tokens create-key-pair --output-private-key private.key --output-public-key public.key

# 创建Token
bin/pulsar tokens create --private-key private.key --subject spc-331

# 配置broker认证
# 编辑conf/standalone.conf
authenticationEnabled=true
authenticationProviders=org.apache.pulsar.broker.authentication.AuthenticationProviderToken
tokenSecretKey=file:///path/to/private.key
```

## 9. 验证部署

### 9.1 检查服务状态
```bash
# 检查后端服务
curl http://localhost:8080/actuator/health

# 检查前端服务
curl http://localhost/

# 检查Pulsar服务
bin/pulsar-admin brokers healthcheck
```

### 9.2 功能测试
1. 访问系统首页：`http://your-domain.com`
2. 使用admin/admin123登录
3. 进入化学审计菜单
4. 测试数据监控页面
5. 测试任务控制功能
6. 测试数据查询和导出

### 9.3 日志检查
```bash
# 后端日志
tail -f logs/ruoyi.log

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Pulsar日志
tail -f logs/pulsar.log
```

## 10. 监控和维护

### 10.1 系统监控
- 使用系统内置的监控页面查看实时状态
- 配置Prometheus + Grafana进行详细监控
- 设置告警规则和通知

### 10.2 日志管理
```bash
# 日志轮转配置
cat > /etc/logrotate.d/chemical-audit << 'EOF'
/opt/chemical-audit/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 ruoyi ruoyi
}
EOF
```

### 10.3 备份策略
```bash
# 数据库备份脚本
cat > backup_db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
sqlcmd -S localhost -Q "BACKUP DATABASE [g2-qis] TO DISK = '/backup/g2-qis_$DATE.bak'"
# 删除30天前的备份
find /backup -name "g2-qis_*.bak" -mtime +30 -delete
EOF
```

### 10.4 性能优化
- 定期清理历史数据
- 优化数据库索引
- 调整JVM参数
- 配置连接池参数

## 11. 故障排除

### 11.1 常见问题
1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串和凭据
   - 检查防火墙设置

2. **Pulsar连接异常**
   - 确认Pulsar服务运行状态
   - 检查网络连接
   - 验证认证配置

3. **前端页面无法访问**
   - 检查Nginx配置
   - 验证静态文件路径
   - 查看浏览器控制台错误

### 11.2 应急处理
- 准备回滚方案
- 建立故障联系机制
- 制定数据恢复流程

---

**部署完成后，请及时修改默认密码并进行安全加固！**
