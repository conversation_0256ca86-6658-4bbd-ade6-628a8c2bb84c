package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.TestParamItem;

/**
 * 测试参数明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ITestParamItemService 
{
    /**
     * 查询测试参数明细
     * 
     * @param testParamId 测试参数明细主键
     * @return 测试参数明细
     */
    public TestParamItem selectTestParamItemByTestParamId(Long testParamId);

    /**
     * 查询测试参数明细列表
     * 
     * @param testParamItem 测试参数明细
     * @return 测试参数明细集合
     */
    public List<TestParamItem> selectTestParamItemList(TestParamItem testParamItem);

    /**
     * 根据测试方案组ID查询测试参数明细列表
     * 
     * @param planGroupId 测试方案组ID
     * @return 测试参数明细集合
     */
    public List<TestParamItem> selectTestParamItemByPlanGroupId(Long planGroupId);

    /**
     * 新增测试参数明细
     * 
     * @param testParamItem 测试参数明细
     * @return 结果
     */
    public int insertTestParamItem(TestParamItem testParamItem);

    /**
     * 修改测试参数明细
     * 
     * @param testParamItem 测试参数明细
     * @return 结果
     */
    public int updateTestParamItem(TestParamItem testParamItem);

    /**
     * 批量删除测试参数明细
     * 
     * @param testParamIds 需要删除的测试参数明细主键集合
     * @return 结果
     */
    public int deleteTestParamItemByTestParamIds(Long[] testParamIds);

    /**
     * 删除测试参数明细信息
     * 
     * @param testParamId 测试参数明细主键
     * @return 结果
     */
    public int deleteTestParamItemByTestParamId(Long testParamId);

    /**
     * 获取测试参数明细选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    public List<String> selectTestParamItemOptions(String type);
}
