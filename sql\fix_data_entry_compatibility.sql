-- 数据录入模块兼容性修复脚本
-- 解决 Table 'codebuddy.test_plans' doesn't exist 的问题
-- 创建兼容性视图和数据同步

-- ======== 第一步：检查当前表结构 ========

-- 1.1 检查新表是否存在
SELECT 'test_plan_group表检查:' as info;
SELECT COUNT(*) as record_count FROM test_plan_group;

SELECT 'test_param_item表检查:' as info;
SELECT COUNT(*) as record_count FROM test_param_item;

-- 1.2 检查是否存在旧的test_plans表
SELECT 'test_plans表检查:' as info;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('存在，包含 ', COUNT(*), ' 条记录')
        ELSE '不存在'
    END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'test_plans';

-- ======== 第二步：创建兼容性视图 ========

-- 2.1 如果test_plans表不存在，创建基于新表结构的视图
DROP VIEW IF EXISTS test_plans;

CREATE VIEW test_plans AS
SELECT 
    tpg.plan_group_id as test_plan_id,
    tpg.plan_code,
    tpg.performance_type,
    tpg.performance_name,
    tpg.test_equipment,
    -- 将测试参数明细合并为一个字段
    GROUP_CONCAT(DISTINCT tpi.param_name ORDER BY tpi.param_name SEPARATOR ', ') as test_parameter,
    tpg.attachments,
    tpg.remark,
    tpg.create_by,
    tpg.create_time,
    tpg.update_by,
    tpg.update_time
FROM test_plan_group tpg
LEFT JOIN test_param_item tpi ON tpg.plan_group_id = tpi.plan_group_id
GROUP BY tpg.plan_group_id, tpg.plan_code, tpg.performance_type, tpg.performance_name, 
         tpg.test_equipment, tpg.attachments, tpg.remark, tpg.create_by, tpg.create_time, 
         tpg.update_by, tpg.update_time;

-- ======== 第三步：验证视图创建结果 ========

SELECT '=== test_plans视图验证 ===' as info;
SELECT 
    test_plan_id,
    plan_code,
    performance_type,
    performance_name,
    test_equipment,
    test_parameter,
    CASE 
        WHEN attachments IS NULL THEN '无附件'
        ELSE '有附件'
    END as attachment_status
FROM test_plans
ORDER BY plan_code
LIMIT 10;

-- ======== 第四步：检查数据录入模块相关表 ========

-- 4.1 检查test_results表结构
SELECT '=== test_results表结构检查 ===' as info;
DESCRIBE test_results;

-- 4.2 检查test_results表中的数据
SELECT '=== test_results表数据检查 ===' as info;
SELECT COUNT(*) as total_records FROM test_results;

-- 4.3 验证关联查询是否正常
SELECT '=== 关联查询验证 ===' as info;
SELECT 
    tr.test_result_id,
    tr.plan_group_id,
    tr.test_param_id,
    tr.group_id,
    tpg.plan_code,
    tpg.performance_name,
    tpi.param_name,
    pg.param_number,
    m.material_name
FROM test_results tr
LEFT JOIN test_plan_group tpg ON tr.plan_group_id = tpg.plan_group_id
LEFT JOIN test_param_item tpi ON tr.test_param_id = tpi.test_param_id
LEFT JOIN process_param_group pg ON tr.group_id = pg.group_id
LEFT JOIN materials m ON pg.material_id = m.material_id
LIMIT 5;

-- ======== 第五步：插入测试数据（如果表为空） ========

-- 5.1 检查test_plan_group是否有数据，如果没有则插入测试数据
INSERT INTO test_plan_group (plan_code, performance_type, performance_name, test_equipment, attachments, remark, create_by, create_time)
SELECT * FROM (
    SELECT 'TP001' as plan_code, '机械性能' as performance_type, '拉伸强度测试方案' as performance_name, '万能试验机' as test_equipment, 'http://example.com/file1.pdf' as attachments, '标准拉伸测试方案' as remark, 'admin' as create_by, NOW() as create_time
    UNION ALL
    SELECT 'TP002', '热性能', '热膨胀系数测试方案', '热膨胀仪', NULL, '热膨胀性能测试', 'admin', NOW()
    UNION ALL
    SELECT 'TP003', '电性能', '电阻率测试方案', '四探针测试仪', NULL, '电阻率测试方案', 'admin', NOW()
    UNION ALL
    SELECT 'TP004', '机械性能', '冲击韧性测试方案', '冲击试验机', 'http://example.com/file4.pdf', '冲击韧性测试标准', 'admin', NOW()
    UNION ALL
    SELECT 'TP005', '化学性能', '耐腐蚀性测试方案', '盐雾试验箱', NULL, '耐腐蚀性能评估', 'admin', NOW()
) AS new_data
WHERE NOT EXISTS (SELECT 1 FROM test_plan_group LIMIT 1);

-- 5.2 为每个测试方案组插入测试参数明细
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, remark, create_by, create_time)
SELECT tpg.plan_group_id, param_data.param_name, param_data.param_value, param_data.unit, param_data.remark, 'admin', NOW()
FROM test_plan_group tpg
CROSS JOIN (
    SELECT '拉伸强度' as param_name, '500' as param_value, 'MPa' as unit, '最大拉伸强度' as remark
    UNION ALL SELECT '屈服强度', '350', 'MPa', '屈服点强度'
    UNION ALL SELECT '延伸率', '15', '%', '断裂延伸率'
) AS param_data
WHERE tpg.plan_code = 'TP001'
AND NOT EXISTS (SELECT 1 FROM test_param_item WHERE plan_group_id = tpg.plan_group_id);

-- 为其他测试方案组也插入参数明细
INSERT INTO test_param_item (plan_group_id, param_name, param_value, unit, remark, create_by, create_time)
SELECT tpg.plan_group_id, param_data.param_name, param_data.param_value, param_data.unit, param_data.remark, 'admin', NOW()
FROM test_plan_group tpg
CROSS JOIN (
    SELECT '线膨胀系数' as param_name, '2.3E-5' as param_value, '/K' as unit, '线性热膨胀系数' as remark
    UNION ALL SELECT '体膨胀系数', '6.9E-5', '/K', '体积热膨胀系数'
) AS param_data
WHERE tpg.plan_code = 'TP002'
AND NOT EXISTS (SELECT 1 FROM test_param_item WHERE plan_group_id = tpg.plan_group_id);

-- ======== 第六步：最终验证 ========

SELECT '=== 修复完成验证 ===' as info;

-- 验证test_plans视图
SELECT 'test_plans视图记录数:' as item, COUNT(*) as count FROM test_plans;

-- 验证test_plan_group表
SELECT 'test_plan_group表记录数:' as item, COUNT(*) as count FROM test_plan_group;

-- 验证test_param_item表
SELECT 'test_param_item表记录数:' as item, COUNT(*) as count FROM test_param_item;

-- 显示修复结果
SELECT
    '数据录入模块兼容性修复完成！' as status,
    '现在可以正常使用数据录入功能' as message;

-- ======== 第七步：创建数据录入详情查询优化 ========

-- 7.1 创建用于数据录入详情显示的视图
DROP VIEW IF EXISTS test_result_detail_view;

CREATE VIEW test_result_detail_view AS
SELECT
    tr.test_result_id,
    tr.plan_group_id,
    tr.test_param_id,
    tr.group_id,
    tr.supplier_datasheet_val,
    tr.test_value,
    tr.attachments as test_attachments,
    tr.remark as test_remark,
    tr.create_by as test_create_by,
    tr.create_time as test_create_time,
    tr.update_by as test_update_by,
    tr.update_time as test_update_time,
    -- 测试方案组信息
    tpg.plan_code,
    tpg.performance_type,
    tpg.performance_name,
    tpg.test_equipment,
    tpg.attachments as plan_attachments,
    tpg.remark as plan_remark,
    -- 测试参数明细信息
    tpi.param_name,
    tpi.param_value,
    tpi.unit as param_unit,
    tpi.attachments as param_attachments,
    tpi.remark as param_remark,
    -- 工艺参数组信息
    pg.param_number,
    pg.process_type,
    pg.attachments as process_attachments,
    pg.remark as process_remark,
    -- 材料信息
    m.material_name,
    m.supplier_name,
    m.material_model,
    m.material_description,
    m.attachments as material_attachments,
    m.remark as material_remark
FROM test_results tr
LEFT JOIN test_plan_group tpg ON tr.plan_group_id = tpg.plan_group_id
LEFT JOIN test_param_item tpi ON tr.test_param_id = tpi.test_param_id
LEFT JOIN process_param_group pg ON tr.group_id = pg.group_id
LEFT JOIN materials m ON pg.material_id = m.material_id;

-- 7.2 验证详情视图
SELECT '=== 数据录入详情视图验证 ===' as info;
SELECT
    test_result_id,
    plan_code,
    performance_name,
    param_name,
    param_number,
    material_name,
    supplier_name
FROM test_result_detail_view
LIMIT 5;

-- ======== 第八步：创建测试数据录入记录 ========

-- 8.1 如果test_results表为空，插入一些测试数据
INSERT INTO test_results (plan_group_id, test_param_id, group_id, supplier_datasheet_val, test_value, remark, create_by, create_time)
SELECT
    tpg.plan_group_id,
    tpi.test_param_id,
    pg.group_id,
    '500',
    485.5,
    '测试数据示例',
    'admin',
    NOW()
FROM test_plan_group tpg
JOIN test_param_item tpi ON tpg.plan_group_id = tpi.plan_group_id
JOIN process_param_group pg ON pg.group_id = 1  -- 假设存在group_id=1的工艺参数组
WHERE tpg.plan_code = 'TP001'
AND tpi.param_name = '拉伸强度'
AND NOT EXISTS (SELECT 1 FROM test_results LIMIT 1)
LIMIT 1;

-- 8.2 最终验证所有表的数据
SELECT '=== 最终数据验证 ===' as info;
SELECT 'test_plan_group' as table_name, COUNT(*) as record_count FROM test_plan_group
UNION ALL
SELECT 'test_param_item', COUNT(*) FROM test_param_item
UNION ALL
SELECT 'test_results', COUNT(*) FROM test_results
UNION ALL
SELECT 'test_plans(视图)', COUNT(*) FROM test_plans;

SELECT '数据录入模块修复完成，包含参数明细显示功能！' as final_status;
