# 数据库字段修改说明

## 需要添加的字段

### 1. materials 表（材料信息表）
如果数据库中没有以下字段，请执行以下SQL语句添加：

```sql
-- 添加附件字段（如果不存在）
ALTER TABLE materials ADD COLUMN attachments TEXT COMMENT '附件信息(JSON格式)';

-- 确保审计字段存在
ALTER TABLE materials ADD COLUMN create_by VARCHAR(64) DEFAULT '' COMMENT '创建者';
ALTER TABLE materials ADD COLUMN create_time DATETIME COMMENT '创建时间';
ALTER TABLE materials ADD COLUMN update_by VARCHAR(64) DEFAULT '' COMMENT '更新者';
ALTER TABLE materials ADD COLUMN update_time DATETIME COMMENT '更新时间';
ALTER TABLE materials ADD COLUMN remark VARCHAR(500) DEFAULT NULL COMMENT '备注';
```

### 2. process_param_group 表（工艺参数组表）
```sql
-- 添加附件字段（如果不存在）
ALTER TABLE process_param_group ADD COLUMN attachments TEXT COMMENT '附件信息(JSON格式)';

-- 确保审计字段存在
ALTER TABLE process_param_group ADD COLUMN create_by VARCHAR(64) DEFAULT '' COMMENT '创建者';
ALTER TABLE process_param_group ADD COLUMN create_time DATETIME COMMENT '创建时间';
ALTER TABLE process_param_group ADD COLUMN update_by VARCHAR(64) DEFAULT '' COMMENT '更新者';
ALTER TABLE process_param_group ADD COLUMN update_time DATETIME COMMENT '更新时间';
ALTER TABLE process_param_group ADD COLUMN remark VARCHAR(500) DEFAULT NULL COMMENT '备注';
```

### 3. process_param_item 表（参数明细表）
```sql
-- 添加附件字段（如果不存在）
ALTER TABLE process_param_item ADD COLUMN attachments TEXT COMMENT '附件信息(JSON格式)';

-- 确保审计字段存在
ALTER TABLE process_param_item ADD COLUMN create_by VARCHAR(64) DEFAULT '' COMMENT '创建者';
ALTER TABLE process_param_item ADD COLUMN create_time DATETIME COMMENT '创建时间';
ALTER TABLE process_param_item ADD COLUMN update_by VARCHAR(64) DEFAULT '' COMMENT '更新者';
ALTER TABLE process_param_item ADD COLUMN update_time DATETIME COMMENT '更新时间';
ALTER TABLE process_param_item ADD COLUMN remark VARCHAR(500) DEFAULT NULL COMMENT '备注';
```

### 4. test_plan 表（测试方案表）
```sql
-- 创建测试方案表（如果不存在）
CREATE TABLE IF NOT EXISTS test_plan (
    test_plan_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '测试方案ID',
    plan_code VARCHAR(100) NOT NULL COMMENT '方案编号',
    performance_name VARCHAR(200) NOT NULL COMMENT '性能名称',
    test_type VARCHAR(100) COMMENT '测试类型',
    test_description TEXT COMMENT '测试描述',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0草稿 1启用 2停用）',
    priority CHAR(1) DEFAULT '2' COMMENT '优先级（1低 2中 3高 4紧急）',
    attachments TEXT COMMENT '附件信息(JSON格式)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (test_plan_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='测试方案表';
```

### 5. test_result 表（测试结果表）
```sql
-- 创建测试结果表（如果不存在）
CREATE TABLE IF NOT EXISTS test_result (
    test_result_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '测试结果ID',
    test_plan_id BIGINT(20) NOT NULL COMMENT '测试方案ID',
    group_id BIGINT(20) NOT NULL COMMENT '参数组ID',
    supplier_datasheet_val VARCHAR(100) COMMENT '供应商Datasheet值',
    test_value DECIMAL(18,6) COMMENT '实际测试值',
    unit VARCHAR(50) COMMENT '单位',
    test_status CHAR(1) DEFAULT '0' COMMENT '测试状态（0待测试 1测试中 2已完成 3异常）',
    test_date DATE COMMENT '测试日期',
    test_person VARCHAR(100) COMMENT '测试人员',
    test_condition TEXT COMMENT '测试条件',
    result_description TEXT COMMENT '测试结果说明',
    attachments TEXT COMMENT '附件信息(JSON格式)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (test_result_id),
    KEY idx_test_plan_id (test_plan_id),
    KEY idx_group_id (group_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='测试结果表';
```

## 字段说明

### attachments 字段
- 类型：TEXT
- 用途：存储附件信息的JSON字符串
- 格式示例：
```json
[
  {
    "name": "文件名.pdf",
    "url": "/upload/2024/01/01/xxx.pdf",
    "size": "1.2MB"
  }
]
```

### 审计字段
- create_by: 创建者用户名
- create_time: 创建时间
- update_by: 更新者用户名  
- update_time: 更新时间
- remark: 备注信息

## 索引建议

```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_material_name ON materials(material_name);
CREATE INDEX idx_supplier_name ON materials(supplier_name);
CREATE INDEX idx_process_type ON process_param_group(process_type);
CREATE INDEX idx_param_number ON process_param_group(param_number);
CREATE INDEX idx_param_name ON process_param_item(param_name);
CREATE INDEX idx_plan_code ON test_plan(plan_code);
CREATE INDEX idx_test_status ON test_result(test_status);
CREATE INDEX idx_test_date ON test_result(test_date);
```

## 权限配置

确保在系统菜单管理中添加相应的权限：

```sql
-- 材料管理权限
INSERT INTO sys_menu VALUES('材料管理', '材料信息', 'material:material:list', 'material:material:query', 'material:material:add', 'material:material:edit', 'material:material:remove', 'material:material:export', 'material:material:import');

-- 工艺参数组权限  
INSERT INTO sys_menu VALUES('工艺参数组', 'material:processParamGroup:list', 'material:processParamGroup:query', 'material:processParamGroup:add', 'material:processParamGroup:edit', 'material:processParamGroup:remove', 'material:processParamGroup:export');

-- 参数明细权限
INSERT INTO sys_menu VALUES('参数明细', 'material:processParamItem:list', 'material:processParamItem:query', 'material:processParamItem:add', 'material:processParamItem:edit', 'material:processParamItem:remove', 'material:processParamItem:export');

-- 测试方案权限
INSERT INTO sys_menu VALUES('测试方案', 'material:testPlan:list', 'material:testPlan:query', 'material:testPlan:add', 'material:testPlan:edit', 'material:testPlan:remove', 'material:testPlan:export');

-- 测试结果权限
INSERT INTO sys_menu VALUES('测试结果', 'material:testResult:list', 'material:testResult:query', 'material:testResult:add', 'material:testResult:edit', 'material:testResult:remove', 'material:testResult:export');
```

## 注意事项

1. 执行SQL前请先备份数据库
2. 根据实际的表名和字段名进行调整
3. 如果表已存在但字段不存在，只需执行ADD COLUMN语句
4. 附件字段使用TEXT类型以支持较长的JSON字符串
5. 所有时间字段建议使用DATETIME类型
6. 建议为经常查询的字段添加索引以提高性能