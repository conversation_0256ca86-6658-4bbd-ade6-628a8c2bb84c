-- 测试方案配置问题修复脚本
-- 解决测试方案配置中"无法找到接口"的问题

-- 1. 确保测试方案表存在且结构正确
DROP TABLE IF EXISTS test_plans;
CREATE TABLE `test_plans` (
  `test_plan_id`     INT NOT NULL AUTO_INCREMENT COMMENT '测试方案ID（主键）',
  `plan_code`        VARCHAR(50)  DEFAULT NULL COMMENT '方案编号',
  `performance_type` VARCHAR(50)  DEFAULT NULL COMMENT '性能类型',
  `performance_name` VARCHAR(100) DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment`   VARCHAR(100) DEFAULT NULL COMMENT '测试设备',
  `test_parameter`   VARCHAR(100) DEFAULT NULL COMMENT '测试参数',
  `attachments`      VARCHAR(2000) DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark`           VARCHAR(500)  DEFAULT NULL COMMENT '备注',
  `create_by`        VARCHAR(64)   DEFAULT NULL COMMENT '创建者',
  `create_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by`        VARCHAR(64)   DEFAULT NULL COMMENT '更新者',
  `update_time`      DATETIME      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_plan_id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试方案表';

-- 2. 插入测试数据
INSERT INTO test_plans (plan_code, performance_type, performance_name, test_equipment, test_parameter, attachments, remark, create_by, create_time) VALUES
('TP001', '机械性能', '拉伸强度测试', '万能试验机', '拉伸强度、屈服强度、延伸率', 'http://example.com/file1.pdf,http://example.com/file2.jpg', '标准拉伸测试方案', 'admin', NOW()),
('TP002', '热性能', '热膨胀系数测试', '热膨胀仪', '线膨胀系数、体膨胀系数', 'http://example.com/file3.doc', '热膨胀性能测试', 'admin', NOW()),
('TP003', '电性能', '电阻率测试', '四探针测试仪', '体积电阻率、表面电阻率', NULL, '电阻率测试方案', 'admin', NOW()),
('TP004', '机械性能', '冲击韧性测试', '冲击试验机', '冲击韧性值', 'http://example.com/file4.pdf', '冲击韧性测试标准', 'admin', NOW()),
('TP005', '化学性能', '耐腐蚀性测试', '盐雾试验箱', '腐蚀速率、腐蚀深度', 'http://example.com/file5.xlsx,http://example.com/file6.png', '耐腐蚀性能评估', 'admin', NOW()),
('TP006', '热性能', '导热系数测试', '导热系数测试仪', '导热系数、热扩散率', NULL, '导热性能测试', 'admin', NOW()),
('TP007', '机械性能', '硬度测试', '硬度计', '布氏硬度、洛氏硬度', 'http://example.com/file7.pdf', '硬度测试标准方案', 'admin', NOW()),
('TP008', '电性能', '介电常数测试', '介电常数测试仪', '介电常数、介电损耗', 'http://example.com/file8.doc,http://example.com/file9.jpg', '介电性能测试', 'admin', NOW()),
('TP009', '机械性能', '疲劳测试', '疲劳试验机', '疲劳寿命、疲劳强度', NULL, '疲劳性能测试', 'admin', NOW()),
('TP010', '热性能', '热稳定性测试', '热重分析仪', '分解温度、热稳定性', 'http://example.com/file10.pdf', '热稳定性评估', 'admin', NOW());

-- 3. 验证数据插入结果
SELECT 
    COUNT(*) as '总记录数',
    COUNT(DISTINCT plan_code) as '不同方案编号数',
    COUNT(DISTINCT performance_type) as '不同性能类型数',
    COUNT(DISTINCT test_equipment) as '不同测试设备数'
FROM test_plans;

-- 4. 显示各选项的候选项数据
SELECT '=== 方案编号选项 ===' as '选项类型';
SELECT DISTINCT plan_code FROM test_plans
WHERE plan_code IS NOT NULL AND plan_code != ''
ORDER BY plan_code;

SELECT '=== 性能类型选项 ===' as '选项类型';
SELECT DISTINCT performance_type FROM test_plans
WHERE performance_type IS NOT NULL AND performance_type != ''
ORDER BY performance_type;

SELECT '=== 测试设备选项 ===' as '选项类型';
SELECT DISTINCT test_equipment FROM test_plans
WHERE test_equipment IS NOT NULL AND test_equipment != ''
ORDER BY test_equipment;

-- 5. 显示示例数据
SELECT 
    plan_code as '方案编号',
    performance_type as '性能类型', 
    performance_name as '方案名称',
    test_equipment as '测试设备',
    CASE 
        WHEN attachments IS NULL THEN '无附件'
        ELSE CONCAT('有附件(', CHAR_LENGTH(attachments) - CHAR_LENGTH(REPLACE(attachments, ',', '')) + 1, '个)')
    END as '附件状态',
    create_time as '创建时间'
FROM test_plans 
ORDER BY plan_code;

SELECT '测试方案配置数据修复完成！' as '修复状态';
