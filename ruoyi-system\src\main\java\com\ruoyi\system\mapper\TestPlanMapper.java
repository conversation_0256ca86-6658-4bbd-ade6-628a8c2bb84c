package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.TestPlan;

/**
 * 测试方案Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface TestPlanMapper 
{
    /**
     * 查询测试方案
     * 
     * @param testPlanId 测试方案主键
     * @return 测试方案
     */
    public TestPlan selectTestPlanByTestPlanId(Long testPlanId);

    /**
     * 查询测试方案列表
     * 
     * @param testPlan 测试方案
     * @return 测试方案集合
     */
    public List<TestPlan> selectTestPlanList(TestPlan testPlan);

    /**
     * 新增测试方案
     * 
     * @param testPlan 测试方案
     * @return 结果
     */
    public int insertTestPlan(TestPlan testPlan);

    /**
     * 修改测试方案
     * 
     * @param testPlan 测试方案
     * @return 结果
     */
    public int updateTestPlan(TestPlan testPlan);

    /**
    /**
     * 删除测试方案
     * 
     * @param testPlanId 测试方案主键
     * @return 结果
     */
    public int deleteTestPlanByTestPlanId(Long testPlanId);

    /**
     * 批量删除测试方案
     *
     * @param testPlanIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTestPlanByTestPlanIds(Long[] testPlanIds);

    /**
     * 获取方案编号选项
     *
     * @return 方案编号列表
     */
    public List<String> selectPlanCodeOptions();

    /**
     * 获取性能类型选项
     *
     * @return 性能类型列表
     */
    public List<String> selectPerformanceTypeOptions();

    /**
     * 获取测试设备选项
     *
     * @return 测试设备列表
     */
    public List<String> selectTestEquipmentOptions();

}
