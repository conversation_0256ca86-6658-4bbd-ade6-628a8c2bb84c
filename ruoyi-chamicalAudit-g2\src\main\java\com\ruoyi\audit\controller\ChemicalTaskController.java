package com.ruoyi.audit.controller;

import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.audit.service.IChemicalTaskService;

/**
 * 化学审计任务控制Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/chemical/task")
public class ChemicalTaskController extends BaseController
{
    @Autowired
    private IChemicalTaskService chemicalTaskService;

    /**
     * 启动数据读取任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:start')")
    @Log(title = "化学审计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/start")
    public AjaxResult startTask()
    {
        Map<String, Object> result = chemicalTaskService.startDataReadingTask();
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 暂停数据读取任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:pause')")
    @Log(title = "化学审计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/pause")
    public AjaxResult pauseTask()
    {
        Map<String, Object> result = chemicalTaskService.pauseDataReadingTask();
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 停止数据读取任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:stop')")
    @Log(title = "化学审计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/stop")
    public AjaxResult stopTask()
    {
        Map<String, Object> result = chemicalTaskService.stopDataReadingTask();
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 重启数据读取任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:restart')")
    @Log(title = "化学审计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/restart")
    public AjaxResult restartTask()
    {
        Map<String, Object> result = chemicalTaskService.restartDataReadingTask();
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 获取任务状态
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:view')")
    @GetMapping("/status")
    public AjaxResult getTaskStatus()
    {
        Map<String, Object> result = chemicalTaskService.getTaskStatus();
        return success(result);
    }

    /**
     * 获取任务监控信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:view')")
    @GetMapping("/monitor")
    public AjaxResult getTaskMonitor()
    {
        Map<String, Object> result = chemicalTaskService.getTaskMonitorInfo();
        return success(result);
    }

    /**
     * 启动数据处理任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:start')")
    @Log(title = "化学审计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/startProcessing")
    public AjaxResult startDataProcessing()
    {
        Map<String, Object> result = chemicalTaskService.startDataProcessingTask();
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 启动日志处理任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:start')")
    @Log(title = "化学审计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/startLogProcessing")
    public AjaxResult startLogProcessing()
    {
        Map<String, Object> result = chemicalTaskService.startLogProcessingTask();
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 获取实时日志信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:view')")
    @GetMapping("/logs")
    public AjaxResult getRealtimeLogs(@RequestParam(value = "lastLogId", defaultValue = "0") Long lastLogId)
    {
        Map<String, Object> result = chemicalTaskService.getRealtimeLogs(lastLogId);
        return success(result);
    }

    /**
     * 清空日志信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:start')")
    @Log(title = "化学审计任务", businessType = BusinessType.DELETE)
    @PostMapping("/clearLogs")
    public AjaxResult clearLogs()
    {
        Map<String, Object> result = chemicalTaskService.clearLogs();
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? success(message) : error(message);
    }

    /**
     * 获取系统运行状态
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:view')")
    @GetMapping("/systemStatus")
    public AjaxResult getSystemStatus()
    {
        Map<String, Object> result = chemicalTaskService.getSystemStatus();
        return success(result);
    }

    /**
     * 初始化Pulsar客户端
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:start')")
    @Log(title = "化学审计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/initPulsar")
    public AjaxResult initPulsarClient()
    {
        boolean result = chemicalTaskService.initializePulsarClient();
        return result ? success("Pulsar客户端初始化成功") : error("Pulsar客户端初始化失败");
    }

    /**
     * 关闭Pulsar客户端
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:stop')")
    @Log(title = "化学审计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/closePulsar")
    public AjaxResult closePulsarClient()
    {
        boolean result = chemicalTaskService.closePulsarClient();
        return result ? success("Pulsar客户端关闭成功") : error("Pulsar客户端关闭失败");
    }

    /**
     * 检查Pulsar连接状态
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:view')")
    @GetMapping("/pulsarStatus")
    public AjaxResult checkPulsarStatus()
    {
        boolean connected = chemicalTaskService.checkPulsarConnection();
        Map<String, Object> result = chemicalTaskService.getPulsarConsumerStatus();
        result.put("connected", connected);
        
        return success(result);
    }

    /**
     * 启动统一数据处理任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:unified')")
    @Log(title = "统一数据处理任务", businessType = BusinessType.UPDATE)
    @PostMapping("/startUnified")
    public AjaxResult startUnifiedDataProcessingTask(@RequestBody Map<String, Object> params)
    {
        Map<String, Object> result = chemicalTaskService.startUnifiedDataProcessingTask(params);
        return success(result);
    }

    /**
     * 获取统一数据处理统计信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:task:unified')")
    @GetMapping("/unifiedStats")
    public AjaxResult getUnifiedProcessingStatistics()
    {
        Map<String, Object> result = chemicalTaskService.getUnifiedProcessingStatistics();
        return success(result);
    }
}
