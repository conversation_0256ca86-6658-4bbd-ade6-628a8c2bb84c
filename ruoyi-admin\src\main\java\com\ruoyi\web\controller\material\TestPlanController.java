package com.ruoyi.web.controller.material;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.TestPlan;
import com.ruoyi.system.service.ITestPlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 测试方案Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/material/testPlan")
public class TestPlanController extends BaseController
{
    @Autowired
    private ITestPlanService testPlanService;

    /**
     * 查询测试方案列表
     */
    @PreAuthorize("@ss.hasPermi('material:testPlan:list')")
    @GetMapping("/list")
    public TableDataInfo list(TestPlan testPlan)
    {
        startPage();
        List<TestPlan> list = testPlanService.selectTestPlanList(testPlan);
        return getDataTable(list);
    }

    /**
     * 导出测试方案列表
     */
    @PreAuthorize("@ss.hasPermi('material:testPlan:export')")
    @Log(title = "测试方案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestPlan testPlan)
    {
        List<TestPlan> list = testPlanService.selectTestPlanList(testPlan);
        ExcelUtil<TestPlan> util = new ExcelUtil<TestPlan>(TestPlan.class);
        util.exportExcel(response, list, "测试方案数据");
    }

    /**
     * 获取测试方案详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:testPlan:query')")
    @GetMapping(value = "/{testPlanId}")
    public AjaxResult getInfo(@PathVariable("testPlanId") Long testPlanId)
    {
        return AjaxResult.success(testPlanService.selectTestPlanByTestPlanId(testPlanId));
    }

    /**
     * 新增测试方案
     */
    @PreAuthorize("@ss.hasPermi('material:testPlan:add')")
    @Log(title = "测试方案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestPlan testPlan)
    {
        return toAjax(testPlanService.insertTestPlan(testPlan));
    }

    /**
     * 修改测试方案
     */
    @PreAuthorize("@ss.hasPermi('material:testPlan:edit')")
    @Log(title = "测试方案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestPlan testPlan)
    {
        return toAjax(testPlanService.updateTestPlan(testPlan));
    }

    /**
     * 删除测试方案
     */
    @PreAuthorize("@ss.hasPermi('material:testPlan:remove')")
    @Log(title = "测试方案", businessType = BusinessType.DELETE)
	@DeleteMapping("/{testPlanIds}")
    public AjaxResult remove(@PathVariable Long[] testPlanIds)
    {
        return toAjax(testPlanService.deleteTestPlanByTestPlanIds(testPlanIds));
    }

    /**
     * 获取测试方案选项数据
     */
    @GetMapping("/options")
    public AjaxResult getOptions(@RequestParam(required = false) String type)
    {
        List<String> options = testPlanService.selectTestPlanOptions(type);
        return AjaxResult.success(options);
    }
}
