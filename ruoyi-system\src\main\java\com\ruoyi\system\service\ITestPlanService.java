package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.TestPlan;

/**
 * 测试方案Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ITestPlanService 
{
    /**
     * 查询测试方案
     * 
     * @param testPlanId 测试方案主键
     * @return 测试方案
     */
    public TestPlan selectTestPlanByTestPlanId(Long testPlanId);

    /**
     * 查询测试方案列表
     * 
     * @param testPlan 测试方案
     * @return 测试方案集合
     */
    public List<TestPlan> selectTestPlanList(TestPlan testPlan);

    /**
     * 新增测试方案
     * 
     * @param testPlan 测试方案
     * @return 结果
     */
    public int insertTestPlan(TestPlan testPlan);

    /**
     * 修改测试方案
     * 
     * @param testPlan 测试方案
     * @return 结果
     */
    public int updateTestPlan(TestPlan testPlan);

    /**
     * 批量删除测试方案
     * 
     * @param testPlanIds 需要删除的测试方案主键集合
     * @return 结果
     */
    public int deleteTestPlanByTestPlanIds(Long[] testPlanIds);

    int deleteTestPlanByTestPlanId(Long testPlanId);

    /**
     * 获取测试方案选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    public List<String> selectTestPlanOptions(String type);
}
