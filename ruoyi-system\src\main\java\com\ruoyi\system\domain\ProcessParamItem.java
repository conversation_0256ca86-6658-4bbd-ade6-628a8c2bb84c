package com.ruoyi.system.domain;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;

/**
 * 工艺参数明细对象 process_param_items
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class ProcessParamItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 参数明细ID */
    private Long itemId;

    /** 参数组ID */
    @Excel(name = "参数组ID")
    private Long groupId;

    /** 参数名称 */
    @Excel(name = "参数名称")
    private String paramName;

    /** 参数数值 */
    @Excel(name = "参数数值")
    private String paramValue;

    /** 参数单位 */
    @Excel(name = "参数单位")
    private String unit;

    /** 附件 */
    private String attachments;

    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setGroupId(Long groupId) 
    {
        this.groupId = groupId;
    }

    public Long getGroupId() 
    {
        return groupId;
    }
    public void setParamName(String paramName) 
    {
        this.paramName = paramName;
    }

    public String getParamName() 
    {
        return paramName;
    }
    public void setParamValue(String paramValue)
    {
        this.paramValue = paramValue;
    }

    public String getParamValue()
    {
        return paramValue;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("groupId", getGroupId())
            .append("paramName", getParamName())
            .append("paramValue", getParamValue())
            .append("unit", getUnit())
            .append("attachments", getAttachments())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}