<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TestParamItemMapper">
    
    <resultMap type="TestParamItem" id="TestParamItemResult">
        <result property="testParamId"    column="test_param_id"    />
        <result property="planGroupId"    column="plan_group_id"    />
        <result property="paramName"    column="param_name"    />
        <result property="paramValue"    column="param_value"    />
        <result property="unit"    column="unit"    />
        <result property="attachments"    column="attachments"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="planCode"    column="plan_code"    />
        <result property="performanceType"    column="performance_type"    />
    </resultMap>

    <sql id="selectTestParamItemVo">
        select t.test_param_id, t.plan_group_id, t.param_name, t.param_value, t.unit, t.attachments, t.remark, t.create_by, t.create_time, t.update_by, t.update_time, g.plan_code, g.performance_type 
        from test_param_item t
        left join test_plan_group g on g.plan_group_id = t.plan_group_id
    </sql>

    <select id="selectTestParamItemList" parameterType="TestParamItem" resultMap="TestParamItemResult">
        <include refid="selectTestParamItemVo"/>
        <where>  
            <if test="planGroupId != null "> and t.plan_group_id = #{planGroupId}</if>
            <if test="paramName != null  and paramName != ''"> and t.param_name like concat('%', #{paramName}, '%')</if>
            <if test="paramValue != null  and paramValue != ''"> and t.param_value like concat('%', #{paramValue}, '%')</if>
            <if test="unit != null  and unit != ''"> and t.unit like concat('%', #{unit}, '%')</if>
        </where>
        order by t.test_param_id desc
    </select>
    
    <select id="selectTestParamItemByTestParamId" parameterType="Long" resultMap="TestParamItemResult">
        <include refid="selectTestParamItemVo"/>
        where t.test_param_id = #{testParamId}
    </select>

    <select id="selectTestParamItemByPlanGroupId" parameterType="Long" resultMap="TestParamItemResult">
        <include refid="selectTestParamItemVo"/>
        where t.plan_group_id = #{planGroupId}
        order by t.test_param_id
    </select>
        
    <insert id="insertTestParamItem" parameterType="TestParamItem" useGeneratedKeys="true" keyProperty="testParamId">
        insert into test_param_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planGroupId != null">plan_group_id,</if>
            <if test="paramName != null">param_name,</if>
            <if test="paramValue != null">param_value,</if>
            <if test="unit != null">unit,</if>
            <if test="attachments != null">attachments,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planGroupId != null">#{planGroupId},</if>
            <if test="paramName != null">#{paramName},</if>
            <if test="paramValue != null">#{paramValue},</if>
            <if test="unit != null">#{unit},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateTestParamItem" parameterType="TestParamItem">
        update test_param_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="planGroupId != null">plan_group_id = #{planGroupId},</if>
            <if test="paramName != null">param_name = #{paramName},</if>
            <if test="paramValue != null">param_value = #{paramValue},</if>
            <if test="unit != null">unit = #{unit},</if>
            attachments = #{attachments},
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where test_param_id = #{testParamId}
    </update>

    <delete id="deleteTestParamItemByTestParamId" parameterType="Long">
        delete from test_param_item where test_param_id = #{testParamId}
    </delete>

    <delete id="deleteTestParamItemByTestParamIds" parameterType="String">
        delete from test_param_item where test_param_id in 
        <foreach item="testParamId" collection="array" open="(" separator="," close=")">
            #{testParamId}
        </foreach>
    </delete>

    <delete id="deleteTestParamItemByPlanGroupId" parameterType="Long">
        delete from test_param_item where plan_group_id = #{planGroupId}
    </delete>

    <select id="selectParamNameOptions" resultType="String">
        SELECT DISTINCT param_name FROM test_param_item
        WHERE param_name IS NOT NULL AND param_name != ''
        ORDER BY param_name
    </select>

    <select id="selectUnitOptions" resultType="String">
        SELECT DISTINCT unit FROM test_param_item
        WHERE unit IS NOT NULL AND unit != ''
        ORDER BY unit
    </select>

</mapper>
