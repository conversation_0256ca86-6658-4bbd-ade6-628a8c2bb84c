package com.ruoyi.system.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TestResultMapper;
import com.ruoyi.system.domain.TestResult;
import com.ruoyi.system.service.ITestResultService;

/**
 * 测试结果Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class TestResultServiceImpl implements ITestResultService 
{
    @Autowired
    private TestResultMapper testResultMapper;

    /**
     * 查询测试结果
     * 
     * @param testResultId 测试结果主键
     * @return 测试结果
     */
    @Override
    public TestResult selectTestResultByTestResultId(Long testResultId)
    {
        return testResultMapper.selectTestResultByTestResultId(testResultId);
    }

    /**
     * 查询测试结果列表
     * 
     * @param testResult 测试结果
     * @return 测试结果
     */
    @Override
    public List<TestResult> selectTestResultList(TestResult testResult)
    {
        return testResultMapper.selectTestResultList(testResult);
    }

    /**
     * 查询测试结果趋势对比数据
     * 
     * @param testResult 测试结果
     * @return 测试结果集合
     */
    @Override
    public List<TestResult> selectTestResultTrendList(TestResult testResult)
    {
        return testResultMapper.selectTestResultTrendList(testResult);
    }

    /**
     * 新增测试结果
     * 
     * @param testResult 测试结果
     * @return 结果
     */
    @Override
    public int insertTestResult(TestResult testResult)
    {
        testResult.setCreateTime(DateUtils.getNowDate());
        return testResultMapper.insertTestResult(testResult);
    }

    /**
     * 修改测试结果
     * 
     * @param testResult 测试结果
     * @return 结果
     */
    @Override
    public int updateTestResult(TestResult testResult)
    {
        testResult.setUpdateTime(DateUtils.getNowDate());
        return testResultMapper.updateTestResult(testResult);
    }

    /**
     * 批量删除测试结果
     * 
     * @param testResultIds 需要删除的测试结果主键
     * @return 结果
     */
    @Override
    public int deleteTestResultByTestResultIds(Long[] testResultIds)
    {
        return testResultMapper.deleteTestResultByTestResultIds(testResultIds);
    }

    /**
    /**
     * 删除测试结果信息
     * 
     * @param testResultId 测试结果主键
     * @return 结果
     */
    @Override
    public int deleteTestResultByTestResultId(Long testResultId)
    {
        return testResultMapper.deleteTestResultByTestResultId(testResultId);
    }


    /**
     * 获取方案编号选项
     * 
     * @return 方案编号列表
     */
    @Override
    public List<String> selectPlanCodeOptions()
    {
        return testResultMapper.selectPlanCodeOptions();
    }

    /**
     * 获取参数编号选项
     *
     * @return 参数编号列表
     */
    @Override
    public List<String> selectParamNumberOptions()
    {
        return testResultMapper.selectParamNumberOptions();
    }

    /**
     * 获取材料名称选项
     *
     * @return 材料名称列表
     */
    @Override
    public List<String> selectMaterialNameOptions()
    {
        return testResultMapper.selectMaterialNameOptions();
    }

    /**
     * 获取供应商名称选项
     *
     * @return 供应商名称列表
     */
    @Override
    public List<String> selectSupplierNameOptions()
    {
        return testResultMapper.selectSupplierNameOptions();
    }

    /**
     * 获取工艺类型选项
     *
     * @return 工艺类型列表
     */
    @Override
    public List<String> selectProcessTypeOptions()
    {
        return testResultMapper.selectProcessTypeOptions();
    }

    /**
     * 获取测试方案组选项
     *
     * @return 测试方案组列表
     */
    @Override
    public List<String> selectTestPlanGroupOptions()
    {
        return testResultMapper.selectTestPlanGroupOptions();
    }

    /**
     * 获取测试参数明细选项
     *
     * @return 测试参数明细列表
     */
    @Override
    public List<String> selectTestParamItemOptions()
    {
        return testResultMapper.selectTestParamItemOptions();
    }

    /**
     * 根据测试方案组ID获取测试参数明细选项
     *
     * @param planGroupId 测试方案组ID
     * @return 测试参数明细列表
     */
    @Override
    public List<String> selectTestParamItemOptionsByPlanGroupId(Long planGroupId)
    {
        return testResultMapper.selectTestParamItemOptionsByPlanGroupId(planGroupId);
    }

    /**
     * 获取材料型号选项
     *
     * @return 材料型号列表
     */
    @Override
    public List<String> selectMaterialModelOptions()
    {
        return testResultMapper.selectMaterialModelOptions();
    }

    /**
     * 获取性能类型选项
     *
     * @return 性能类型列表
     */
    @Override
    public List<String> selectPerformanceTypeOptions()
    {
        return testResultMapper.selectPerformanceTypeOptions();
    }

    /**
     * 获取测试设备选项
     *
     * @return 测试设备列表
     */
    @Override
    public List<String> selectTestEquipmentOptions()
    {
        return testResultMapper.selectTestEquipmentOptions();
    }

    /**
     * 获取参数详情
     *
     * @param paramNumber 参数编号
     * @return 参数详情
     */
    @Override
    public Map<String, Object> getParamDetail(String paramNumber)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 根据参数编号查询相关的测试结果
            TestResult queryParam = new TestResult();
            queryParam.setParamNumber(paramNumber);
            List<TestResult> testResults = testResultMapper.selectTestResultList(queryParam);

            if (testResults != null && !testResults.isEmpty()) {
                TestResult firstResult = testResults.get(0);

                // 构建参数详情
                result.put("paramNumber", paramNumber);
                result.put("paramName", firstResult.getParamName());
                result.put("materialName", firstResult.getMaterialName());
                result.put("supplierName", firstResult.getSupplierName());
                result.put("processType", firstResult.getProcessType());
                result.put("planGroupId", firstResult.getPlanGroupId());
                result.put("testParamId", firstResult.getTestParamId());
                result.put("materialModel", firstResult.getMaterialModel());
                result.put("performanceType", firstResult.getPerformanceType());
                result.put("testEquipment", firstResult.getTestEquipment());
                result.put("totalResults", testResults.size());

                // 计算统计信息
                if (testResults.size() > 1) {
                    double sum = 0;
                    double min = Double.MAX_VALUE;
                    double max = Double.MIN_VALUE;
                    int validCount = 0;

                    for (TestResult tr : testResults) {
                        if (tr.getTestValue() != null) {
                            double value = tr.getTestValue().doubleValue();
                            sum += value;
                            min = Math.min(min, value);
                            max = Math.max(max, value);
                            validCount++;
                        }
                    }

                    if (validCount > 0) {
                        result.put("averageValue", sum / validCount);
                        result.put("minValue", min);
                        result.put("maxValue", max);
                        result.put("validCount", validCount);
                    }
                }

                result.put("success", true);
            } else {
                result.put("success", false);
                result.put("message", "未找到参数编号为 " + paramNumber + " 的测试结果");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询参数详情失败: " + e.getMessage());
        }

        return result;
    }
}
