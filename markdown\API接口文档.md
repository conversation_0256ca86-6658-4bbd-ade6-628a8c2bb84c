# 化学审计系统 API 接口文档

## 接口概述

化学审计系统提供了完整的RESTful API接口，支持化学数据管理、任务控制、数据刷新、数据导出等功能。

## 认证方式

所有API接口都需要通过若依框架的认证机制，在请求头中携带有效的Token：

```
Authorization: Bearer {token}
```

## 接口列表

### 1. 化学数据管理接口

#### 1.1 查询化学数据列表

**接口地址**: `GET /chemical/data/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 页大小，默认10 |
| examineDate | string | 否 | 检测日期 |
| shift | string | 否 | 班次 |
| staff | string | 否 | 操作员工 |
| departmentCode | string | 否 | 部门代码 |
| processName | string | 否 | 工艺名称 |
| productName | string | 否 | 产品名称 |
| testName | string | 否 | 测试名称 |
| layerNumber | string | 否 | 层号 |
| isExported | boolean | 否 | 是否已导出 |
| notProcess | boolean | 否 | 是否不处理 |
| beginTime | string | 否 | 开始时间 |
| endTime | string | 否 | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": "CHM_20240101_001",
      "organizationId": 1,
      "attributeId": 1,
      "examineDate": "2024-01-01 10:30:00",
      "shift": "白班",
      "staff": "张三",
      "departmentCode": "G2-001",
      "processName": "微蚀",
      "productName": "PD全线",
      "testName": "PD全线微蚀量",
      "layerNumber": "L1",
      "upperLimit": "10.5",
      "medianSpecification": "8.0",
      "downLimit": "5.5",
      "examine1": "7.8",
      "examine1Ys": "7.8",
      "examine2": "7.9",
      "isExported": false,
      "notProcess": false,
      "insertionTime": "2024-01-01 10:31:00",
      "createTime": "2024-01-01 10:31:00",
      "updateTime": "2024-01-01 10:31:00"
    }
  ],
  "total": 1
}
```

#### 1.2 获取化学数据详情

**接口地址**: `GET /chemical/data/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 化学数据ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": "CHM_20240101_001",
    "organizationId": 1,
    // ... 其他字段
  }
}
```

#### 1.3 新增化学数据

**接口地址**: `POST /chemical/data`

**请求体**:
```json
{
  "id": "CHM_20240101_002",
  "organizationId": 1,
  "attributeId": 1,
  "examineDate": "2024-01-01 11:00:00",
  "shift": "白班",
  "staff": "李四",
  "departmentCode": "G2-001",
  "processName": "微蚀",
  "productName": "PD全线",
  "testName": "PD全线微蚀量",
  "layerNumber": "L2",
  "upperLimit": "10.5",
  "medianSpecification": "8.0",
  "downLimit": "5.5",
  "examine1": "8.2",
  "examine2": "8.1"
}
```

#### 1.4 批量操作

**接口地址**: `POST /chemical/data/batch`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 数据ID数组 |
| operation | string | 是 | 操作类型：markExported/markNotProcess/resetStatus |

**请求示例**:
```json
{
  "ids": ["CHM_20240101_001", "CHM_20240101_002"],
  "operation": "markExported"
}
```

### 2. 任务控制接口

#### 2.1 启动数据读取任务

**接口地址**: `POST /chemical/task/start`

**响应示例**:
```json
{
  "code": 200,
  "msg": "任务启动成功",
  "data": {
    "taskId": "task_001",
    "status": "RUNNING",
    "startTime": "2024-01-01 12:00:00"
  }
}
```

#### 2.2 暂停数据读取任务

**接口地址**: `POST /chemical/task/pause`

#### 2.3 停止数据读取任务

**接口地址**: `POST /chemical/task/stop`

#### 2.4 获取任务状态

**接口地址**: `GET /chemical/task/status`

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "taskStatus": "RUNNING",
    "startTime": "2024-01-01 12:00:00",
    "processedRecords": 1500,
    "errorCount": 0,
    "lastProcessTime": "2024-01-01 12:30:00"
  }
}
```

#### 2.5 获取任务监控信息

**接口地址**: `GET /chemical/task/monitor`

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "cpuUsage": 45.2,
    "memoryUsage": 68.5,
    "diskUsage": 32.1,
    "networkIn": 1024,
    "networkOut": 2048,
    "pulsarConnected": true,
    "databaseConnected": true
  }
}
```

### 3. 数据刷新接口

#### 3.1 执行数据刷新

**接口地址**: `POST /chemical/refresh/execute`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 开始时间 (yyyy-MM-dd HH:mm:ss) |
| endDate | string | 是 | 结束时间 (yyyy-MM-dd HH:mm:ss) |
| layerNumbers | array | 是 | 层号数组 |
| exportPath | string | 否 | 导出路径 |
| backupPath | string | 否 | 备份路径 |

**请求示例**:
```json
{
  "startDate": "2024-01-01 00:00:00",
  "endDate": "2024-01-07 23:59:59",
  "layerNumbers": ["L1", "L2", "L3", "L4"],
  "exportPath": "E:\\测试数据\\应审抛转数据\\G2",
  "backupPath": "E:\\测试数据\\备份数据\\G2"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "数据刷新任务启动成功",
  "data": {
    "taskId": 1001,
    "totalGroups": 25,
    "modifiedRecords": 150,
    "duration": 45000,
    "exportFile": "E:\\测试数据\\应审抛转数据\\G2\\chemical_refresh_20240101_120000.csv"
  }
}
```

#### 3.2 获取刷新状态

**接口地址**: `GET /chemical/refresh/status`

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "hasRunningTask": false,
    "completedCount": 15,
    "failedCount": 2,
    "totalCount": 17
  }
}
```

#### 3.3 停止刷新任务

**接口地址**: `POST /chemical/refresh/stop`

#### 3.4 查询刷新任务列表

**接口地址**: `GET /chemical/refresh/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码 |
| pageSize | int | 否 | 页大小 |
| taskName | string | 否 | 任务名称 |
| taskStatus | string | 否 | 任务状态 |

### 4. 数据导出接口

#### 4.1 导出CSV文件

**接口地址**: `POST /chemical/export/csv`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 开始时间 |
| endDate | string | 是 | 结束时间 |
| layerNumbers | array | 否 | 层号数组 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "CSV导出成功",
  "data": {
    "filePath": "E:\\测试数据\\应审抛转数据\\G2\\chemical_export_20240101_120000.csv",
    "fileName": "chemical_export_20240101_120000.csv",
    "recordCount": 1500
  }
}
```

#### 4.2 导出Excel文件

**接口地址**: `POST /chemical/export/excel`

**请求参数**: 同CSV导出

#### 4.3 预览导出数据

**接口地址**: `GET /chemical/export/preview`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 开始时间 |
| endDate | string | 是 | 结束时间 |
| layerNumbers | array | 否 | 层号数组 |
| pageNum | int | 否 | 页码 |
| pageSize | int | 否 | 页大小 |

#### 4.4 验证导出参数

**接口地址**: `POST /chemical/export/validate`

**请求参数**: 同导出接口

**响应示例**:
```json
{
  "code": 200,
  "msg": "参数验证通过",
  "data": {
    "estimatedCount": 1500,
    "timeRangeDays": 7,
    "layerCount": 4
  }
}
```

### 5. 监控接口

#### 5.1 系统健康检查

**接口地址**: `GET /chemical/monitor/health`

**响应示例**:
```json
{
  "code": 200,
  "msg": "系统正常",
  "data": {
    "status": "UP",
    "database": "UP",
    "pulsar": "UP",
    "disk": "UP",
    "memory": "UP"
  }
}
```

#### 5.2 获取系统指标

**接口地址**: `GET /chemical/monitor/metrics`

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "processedRecords": 15000,
    "errorRecords": 5,
    "averageProcessTime": 150,
    "queueSize": 100,
    "memoryUsage": 68.5,
    "cpuUsage": 45.2
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 数据库连接失败 |
| 1002 | Pulsar连接失败 |
| 1003 | 文件操作失败 |
| 1004 | 任务执行失败 |
| 1005 | 参数验证失败 |

## 接口调用示例

### JavaScript示例

```javascript
// 查询化学数据列表
async function getChemicalList(params) {
  const response = await fetch('/chemical/data/list?' + new URLSearchParams(params), {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    }
  });
  return await response.json();
}

// 执行数据刷新
async function executeRefresh(data) {
  const response = await fetch('/chemical/refresh/execute', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  return await response.json();
}
```

### cURL示例

```bash
# 查询化学数据列表
curl -X GET "http://localhost:8080/chemical/data/list?pageNum=1&pageSize=10" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json"

# 执行数据刷新
curl -X POST "http://localhost:8080/chemical/refresh/execute" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-01-01 00:00:00",
    "endDate": "2024-01-07 23:59:59",
    "layerNumbers": ["L1", "L2", "L3", "L4"]
  }'
```

## 注意事项

1. 所有时间参数格式为：`yyyy-MM-dd HH:mm:ss`
2. 分页查询默认页大小为10，最大不超过100
3. 导出操作可能耗时较长，建议异步处理
4. 数据刷新任务同时只能运行一个
5. 大数据量查询建议使用分页和时间范围限制
