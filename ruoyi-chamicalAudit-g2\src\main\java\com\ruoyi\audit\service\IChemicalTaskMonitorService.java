package com.ruoyi.audit.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.audit.domain.ChemicalTaskMonitor;

/**
 * 化学任务监控Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IChemicalTaskMonitorService 
{
    /**
     * 查询化学任务监控
     * 
     * @param monitorId 化学任务监控主键
     * @return 化学任务监控
     */
    public ChemicalTaskMonitor selectChemicalTaskMonitorByMonitorId(Long monitorId);

    /**
     * 查询化学任务监控列表
     * 
     * @param chemicalTaskMonitor 化学任务监控
     * @return 化学任务监控集合
     */
    public List<ChemicalTaskMonitor> selectChemicalTaskMonitorList(ChemicalTaskMonitor chemicalTaskMonitor);

    /**
     * 新增化学任务监控
     * 
     * @param chemicalTaskMonitor 化学任务监控
     * @return 结果
     */
    public int insertChemicalTaskMonitor(ChemicalTaskMonitor chemicalTaskMonitor);

    /**
     * 修改化学任务监控
     * 
     * @param chemicalTaskMonitor 化学任务监控
     * @return 结果
     */
    public int updateChemicalTaskMonitor(ChemicalTaskMonitor chemicalTaskMonitor);

    /**
     * 批量删除化学任务监控
     * 
     * @param monitorIds 需要删除的化学任务监控主键集合
     * @return 结果
     */
    public int deleteChemicalTaskMonitorByMonitorIds(Long[] monitorIds);

    /**
     * 删除化学任务监控信息
     * 
     * @param monitorId 化学任务监控主键
     * @return 结果
     */
    public int deleteChemicalTaskMonitorByMonitorId(Long monitorId);

    /**
     * 查询正在运行的任务
     * 
     * @return 正在运行的任务列表
     */
    public List<ChemicalTaskMonitor> selectRunningTasks();

    /**
     * 根据任务ID查询任务监控信息
     * 
     * @param taskId 任务ID
     * @return 任务监控信息
     */
    public ChemicalTaskMonitor selectChemicalTaskMonitorByTaskId(Long taskId);

    /**
     * 根据任务类型查询最新的任务
     * 
     * @param taskType 任务类型
     * @return 最新任务
     */
    public ChemicalTaskMonitor selectLatestTaskByType(String taskType);

    /**
     * 获取数据趋势
     * 
     * @param days 天数
     * @return 数据趋势
     */
    public Map<String, Object> getDataTrends(int days);

    /**
     * 获取错误统计
     * 
     * @param hours 小时数
     * @return 错误统计
     */
    public Map<String, Object> getErrorStatistics(int hours);

    /**
     * 获取系统告警
     * 
     * @return 告警列表
     */
    public List<Map<String, Object>> getSystemAlerts();

    /**
     * 清理历史任务
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    public int cleanHistoryTasks(int days);

    /**
     * 导出监控报告
     * 
     * @param days 天数
     * @return 导出结果
     */
    public Map<String, Object> exportMonitorReport(int days);

    /**
     * 重置任务状态
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    public int resetTaskStatus(Long taskId);

    /**
     * 批量删除任务记录
     *
     * @param taskIds 任务ID数组
     * @return 结果
     */
    public int deleteChemicalTaskMonitorByTaskIds(Long[] taskIds);

    /**
     * 获取系统监控信息
     *
     * @return 系统监控信息
     */
    public Map<String, Object> getSystemMonitorInfo();

    /**
     * 获取任务执行统计
     *
     * @return 任务执行统计
     */
    public Map<String, Object> getTaskExecutionStats();

    /**
     * 获取数据处理统计
     *
     * @return 数据处理统计
     */
    public Map<String, Object> getDataProcessingStats();

    /**
     * 获取错误日志统计
     *
     * @return 错误日志统计
     */
    public Map<String, Object> getErrorLogStats();

    /**
     * 获取性能监控数据
     *
     * @return 性能监控数据
     */
    public Map<String, Object> getPerformanceMonitorData();

    /**
     * 记录任务执行信息
     *
     * @param taskName 任务名称
     * @param status 执行状态
     * @param message 执行消息
     * @param duration 执行耗时
     * @return 结果
     */
    public int recordTaskExecution(String taskName, String status, String message, Long duration);
}
