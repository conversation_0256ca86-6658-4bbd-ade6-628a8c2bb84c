<template>
  <div class="app-container">
    <!-- 页面标题和说明 -->
    <div class="page-header">
      <div class="page-title">
        <i class="el-icon-edit-outline"></i>
        <span>测试结果数据录入</span>
      </div>
      <div class="page-description">
        <p>📊 录入和管理测试结果数据，支持多维度筛选和批量操作</p>
        <el-alert
          title="使用提示：先选择筛选条件，然后点击查询按钮查看数据，支持列设置自定义显示"
          type="info"
          :closable="false"
          show-icon
          style="margin-top: 10px;">
        </el-alert>
      </div>
    </div>

    <el-card class="result-card enhanced-card">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-edit-outline"></i>
          <span class="header-title">测试结果管理</span>
          <el-badge :value="total" class="item-count-badge" type="primary" />
        </div>
        <div class="header-right">
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">
            <span>新增结果</span>
          </el-button>
          <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple" @click="handleDelete">
            <span>批量删除</span>
          </el-button>
          <el-button type="success" icon="el-icon-download" size="small" @click="handleExport">
            <span>导出</span>
          </el-button>
          <el-button type="info" icon="el-icon-setting" size="small" @click="handleColumnSetting" class="column-setting-btn">
            <span>列设置</span>
          </el-button>
        </div>
      </div>

      <!-- 查询条件 -->
      <div class="search-section">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" class="search-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="参数编号" prop="paramNumber">
                <el-autocomplete
                  v-model="queryParams.paramNumber"
                  :fetch-suggestions="queryParamNumberSuggestions"
                  placeholder="请输入参数编号"
                  clearable
                  style="width: 100%;"
                  @focus="handleParamNumberFocus"
                  :trigger-on-focus="true"
                  prefix-icon="el-icon-tickets"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="材料名称" prop="materialName">
                <el-autocomplete
                  v-model="queryParams.materialName"
                  :fetch-suggestions="queryMaterialNameSuggestions"
                  placeholder="请输入材料名称"
                  clearable
                  style="width: 100%;"
                  @focus="handleMaterialNameFocus"
                  :trigger-on-focus="true"
                  prefix-icon="el-icon-box"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="供应商" prop="supplierName">
                <el-autocomplete
                  v-model="queryParams.supplierName"
                  :fetch-suggestions="querySupplierNameSuggestions"
                  placeholder="请输入供应商名称"
                  clearable
                  style="width: 100%;"
                  @focus="handleSupplierNameFocus"
                  :trigger-on-focus="true"
                  prefix-icon="el-icon-office-building"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工艺类型" prop="processType">
                <el-autocomplete
                  v-model="queryParams.processType"
                  :fetch-suggestions="queryProcessTypeSuggestions"
                  placeholder="请输入工艺类型"
                  clearable
                  style="width: 100%;"
                  @focus="handleProcessTypeFocus"
                  :trigger-on-focus="true"
                  prefix-icon="el-icon-setting"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="材料型号" prop="materialModel">
                <el-autocomplete
                  v-model="queryParams.materialModel"
                  :fetch-suggestions="queryMaterialModelSuggestions"
                  placeholder="请输入材料型号"
                  clearable
                  style="width: 100%;"
                  @focus="handleMaterialModelFocus"
                  :trigger-on-focus="true"
                  prefix-icon="el-icon-goods"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性能类型" prop="performanceType">
                <el-autocomplete
                  v-model="queryParams.performanceType"
                  :fetch-suggestions="queryPerformanceTypeSuggestions"
                  placeholder="请输入性能类型"
                  clearable
                  style="width: 100%;"
                  @focus="handlePerformanceTypeFocus"
                  :trigger-on-focus="true"
                  prefix-icon="el-icon-lightning"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="测试设备" prop="testEquipment">
                <el-autocomplete
                  v-model="queryParams.testEquipment"
                  :fetch-suggestions="queryTestEquipmentSuggestions"
                  placeholder="请输入测试设备"
                  clearable
                  style="width: 100%;"
                  @focus="handleTestEquipmentFocus"
                  :trigger-on-focus="true"
                  prefix-icon="el-icon-cpu"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" style="text-align: right;">
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery" class="search-btn">
                  <span>搜索</span>
                </el-button>
                <el-button icon="el-icon-refresh" size="small" @click="resetQuery" class="reset-btn">
                  <span>重置</span>
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <el-table
        v-loading="loading"
        :data="testResultList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        ref="multipleTable"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" width="60" align="center" />

        <!-- 动态列显示 -->
        <el-table-column
          v-if="visibleColumns.materialName"
          prop="materialName"
          label="材料名称"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          v-if="visibleColumns.supplierName"
          prop="supplierName"
          label="供应商"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          v-if="visibleColumns.materialModel"
          prop="materialModel"
          label="材料型号"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          v-if="visibleColumns.processType"
          prop="processType"
          label="工艺类型"
          width="100"
        />
        <el-table-column
          v-if="visibleColumns.paramNumber"
          prop="paramNumber"
          label="参数编号"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          v-if="visibleColumns.performanceType"
          prop="performanceType"
          label="性能类型"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          v-if="visibleColumns.performanceName"
          prop="performanceName"
          label="性能名称"
          min-width="150"
          show-overflow-tooltip
        />

        <el-table-column
          v-if="visibleColumns.testEquipment"
          prop="testEquipment"
          label="测试设备"
          min-width="120"
          show-overflow-tooltip
        />

        <el-table-column
          v-if="visibleColumns.supplierDatasheetVal"
          prop="supplierDatasheetVal"
          label="供应商数据"
          width="120"
          align="right"
        >
          <template slot-scope="scope">
            <span>{{ formatDecimal(scope.row.supplierDatasheetVal) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="visibleColumns.testValue"
          prop="testValue"
          label="测试值"
          width="100"
          align="right"
        >
          <template slot-scope="scope">
            <span>{{ formatDecimal(scope.row.testValue) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="visibleColumns.createBy"
          prop="createBy"
          label="创建人"
          width="100"
        />
        <el-table-column
          v-if="visibleColumns.createTime"
          prop="createTime"
          label="创建时间"
          width="160"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="visibleColumns.updateBy"
          prop="updateBy"
          label="更新人"
          width="100"
        />
        <el-table-column
          v-if="visibleColumns.updateTime"
          prop="updateTime"
          label="更新时间"
          width="160"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="附件" width="80" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.attachments" size="mini" type="text" @click="handleViewAttachments(scope.row.attachments)">查看</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="text" style="color: #f56c6c" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        style="margin-top: 15px;"
      />
    </el-card>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body v-drag>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="参数编号" prop="groupId">
              <el-select
                v-model="form.groupId"
                placeholder="请选择参数编号"
                style="width: 100%;"
                filterable
                @change="handleParamGroupChange"
              >
                <el-option
                  v-for="group in filteredParamGroupOptions"
                  :key="group.groupId"
                  :label="group.paramNumber + ' - ' + (group.materialName || '') + ' (' + (group.supplierName || '') + ')'"
                  :value="group.groupId"
                >
                  <div style="display: flex; justify-content: space-between;">
                    <span>{{ group.paramNumber || 'N/A' }}</span>
                    <span style="color: #8492a6; font-size: 13px;">{{ (group.materialName || 'N/A') + ' - ' + (group.supplierName || 'N/A') }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测试方案组" prop="planGroupId">
              <el-select
                v-model="form.planGroupId"
                placeholder="请选择测试方案组"
                style="width: 100%;"
                clearable
                filterable
                @change="handleFormPlanGroupChange"
              >
                <el-option
                  v-for="group in testPlanGroupOptions"
                  :key="group.planGroupId"
                  :label="group.planCode + ' - ' + group.performanceName"
                  :value="group.planGroupId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>


        <!-- 工艺参数信息展示 -->
        <el-card v-if="selectedParamDetail" class="param-detail-card enhanced-card" style="margin-bottom: 15px;" :key="selectedParamDetail.groupId + '_' + (selectedParamDetail.paramItems ? selectedParamDetail.paramItems.length : 0)">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-setting"></i>
              <span class="header-title">工艺参数信息</span>
              <el-tag type="success" size="small" style="margin-left: 10px;">
                <i class="el-icon-tickets"></i>
                {{ selectedParamDetail.paramNumber || '-' }}
              </el-tag>
            </div>
          </div>
          <el-descriptions :column="3" border size="small" class="param-descriptions">
            <el-descriptions-item label="材料名称">
              <span class="param-value">{{ selectedParamDetail.materialName || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="供应商">
              <span class="param-value">{{ selectedParamDetail.supplierName || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="工艺类型">
              <span class="param-value">{{ selectedParamDetail.processType || '-' }}</span>
            </el-descriptions-item>
          </el-descriptions>
          <div v-if="selectedParamDetail.paramItems && selectedParamDetail.paramItems.length > 0" style="margin-top: 15px;">
            <div class="param-section-title">
              <i class="el-icon-data-line"></i>
              <span>参数明细</span>
              <el-badge :value="selectedParamDetail.paramItems.length" class="item-count-badge" type="primary" />
            </div>
            <el-table :data="selectedParamDetail.paramItems" size="mini" style="width: 100%;" border class="param-detail-table">
              <el-table-column prop="paramName" label="参数名称" min-width="120" show-overflow-tooltip />
              <el-table-column prop="paramValue" label="参数值" width="120" align="right">
                <template slot-scope="scope">
                  <span class="param-number-value">{{ formatDecimal(scope.row.paramValue) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="unit" label="单位" width="80">
                <template slot-scope="scope">
                  <el-tag size="mini" type="info" v-if="scope.row.unit">{{ scope.row.unit }}</el-tag>
                  <span v-else class="empty-data">-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-state">
            <i class="el-icon-info"></i>
            <span>暂无参数明细信息</span>
          </div>
        </el-card>

        <!-- 测试方案参数信息展示 -->
        <el-card v-if="selectedTestPlanDetail" class="test-plan-detail-card enhanced-card" style="margin-bottom: 15px;">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-document"></i>
              <span class="header-title">测试方案参数信息</span>
              <el-tag type="warning" size="small" style="margin-left: 10px;">
                <i class="el-icon-document"></i>
                {{ selectedTestPlanDetail.planCode || '-' }}
              </el-tag>
            </div>
          </div>
          <el-descriptions :column="3" border size="small" class="param-descriptions">
            <el-descriptions-item label="方案编号">
              <span class="param-value">{{ selectedTestPlanDetail.planCode || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="性能类型">
              <span class="param-value">{{ selectedTestPlanDetail.performanceType || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="性能名称">
              <span class="param-value">{{ selectedTestPlanDetail.performanceName || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="测试设备">
              <span class="param-value">{{ selectedTestPlanDetail.testEquipment || '-' }}</span>
            </el-descriptions-item>
          </el-descriptions>
          <div v-if="selectedTestPlanDetail.testParamItems && selectedTestPlanDetail.testParamItems.length > 0" style="margin-top: 15px;">
            <div class="param-section-title">
              <i class="el-icon-data-line"></i>
              <span>测试参数明细</span>
              <el-badge :value="selectedTestPlanDetail.testParamItems.length" class="item-count-badge" type="warning" />
            </div>
            <el-table :data="selectedTestPlanDetail.testParamItems" size="mini" style="width: 100%;" border class="param-detail-table">
              <el-table-column prop="paramName" label="参数名称" min-width="120" show-overflow-tooltip />
              <el-table-column prop="paramValue" label="参数值" width="120" align="right">
                <template slot-scope="scope">
                  <span class="param-number-value">{{ formatDecimal(scope.row.paramValue) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="unit" label="单位" width="80">
                <template slot-scope="scope">
                  <el-tag size="mini" type="info" v-if="scope.row.unit">{{ scope.row.unit }}</el-tag>
                  <span v-else class="empty-data">-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-state">
            <i class="el-icon-info"></i>
            <span>暂无测试参数明细信息</span>
          </div>
        </el-card>

        <el-row>
          <el-col :span="8">
            <el-form-item label="供应商数据" prop="supplierDatasheetVal">
              <el-input v-model="form.supplierDatasheetVal" placeholder="请输入供应商Datasheet值" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="实际测试值" prop="testValue">
              <el-input-number v-model="form.testValue" :precision="6" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="附件上传">
          <el-upload
            ref="upload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="fileList"
            :on-success="handleUploadSuccess"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持多文件上传，单个文件大小不超过10MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="测试结果详情" :visible.sync="detailDialogVisible" width="900px" append-to-body v-drag>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="材料名称">{{ detailData.materialName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="供应商">{{ detailData.supplierName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="材料型号">{{ detailData.materialModel || '-' }}</el-descriptions-item>
        <el-descriptions-item label="工艺类型">{{ detailData.processType || '-' }}</el-descriptions-item>
        <el-descriptions-item label="参数编号">{{ detailData.paramNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="性能类型">{{ detailData.performanceType || '-' }}</el-descriptions-item>
        <el-descriptions-item label="性能名称">{{ detailData.performanceName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="测试设备">{{ detailData.testEquipment || '-' }}</el-descriptions-item>

        <el-descriptions-item label="供应商数据">{{ formatDecimal(detailData.supplierDatasheetVal) }}</el-descriptions-item>
        <el-descriptions-item label="测试值">{{ formatDecimal(detailData.testValue) }} {{ detailData.paramUnit || '' }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailData.createBy || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailData.createTime) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新人">{{ detailData.updateBy || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailData.updateTime) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '-' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 材料参数明细信息 -->
      <el-card v-if="detailParamItems && detailParamItems.length > 0" class="param-detail-card enhanced-card" style="margin-top: 20px;">
        <div slot="header" class="card-header">
          <div class="header-left">
            <i class="el-icon-setting"></i>
            <span class="header-title">工艺参数信息</span>
            <el-tag type="success" size="small" style="margin-left: 10px;">
              <i class="el-icon-tickets"></i>
              {{ detailData.paramNumber || '-' }}
            </el-tag>
          </div>
        </div>
        <el-descriptions :column="3" border size="small" class="param-descriptions">
          <el-descriptions-item label="材料名称">
            <span class="param-value">{{ detailData.materialName || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="供应商">
            <span class="param-value">{{ detailData.supplierName || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="工艺类型">
            <span class="param-value">{{ detailData.processType || '-' }}</span>
          </el-descriptions-item>
        </el-descriptions>
        <div style="margin-top: 15px;">
          <div class="param-section-title">
            <i class="el-icon-data-line"></i>
            <span>参数明细</span>
            <el-badge :value="detailParamItems.length" class="item-count-badge" type="primary" />
          </div>
          <el-table :data="detailParamItems" size="mini" style="width: 100%;" border class="param-detail-table">
            <el-table-column prop="paramName" label="参数名称" min-width="120" show-overflow-tooltip />
            <el-table-column prop="paramValue" label="参数值" width="120" align="right">
              <template slot-scope="scope">
                <span class="param-number-value">{{ formatDecimal(scope.row.paramValue) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="80">
              <template slot-scope="scope">
                <el-tag size="mini" type="info" v-if="scope.row.unit">{{ scope.row.unit }}</el-tag>
                <span v-else class="empty-data">-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- 测试方案参数信息 -->
      <el-card v-if="detailTestPlanParams" class="test-plan-detail-card enhanced-card" style="margin-top: 20px;">
        <div slot="header" class="card-header">
          <div class="header-left">
            <i class="el-icon-document"></i>
            <span class="header-title">测试方案参数信息</span>
            <el-tag type="warning" size="small" style="margin-left: 10px;">
              <i class="el-icon-document"></i>
              {{ detailTestPlanParams.planCode || '-' }}
            </el-tag>
          </div>
        </div>
        <el-descriptions :column="3" border size="small" class="param-descriptions">
          <el-descriptions-item label="方案编号">
            <span class="param-value">{{ detailTestPlanParams.planCode || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="性能类型">
            <span class="param-value">{{ detailTestPlanParams.performanceType || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="性能名称">
            <span class="param-value">{{ detailTestPlanParams.performanceName || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="测试设备">
            <span class="param-value">{{ detailTestPlanParams.testEquipment || '-' }}</span>
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="detailTestPlanParams.testParams && detailTestPlanParams.testParams.length > 0" style="margin-top: 15px;">
          <div class="param-section-title">
            <i class="el-icon-data-line"></i>
            <span>测试参数明细</span>
            <el-badge :value="detailTestPlanParams.testParams.length" class="item-count-badge" type="warning" />
          </div>
          <el-table :data="detailTestPlanParams.testParams" size="mini" style="width: 100%;" border class="param-detail-table">
            <el-table-column prop="paramName" label="参数名称" min-width="120" show-overflow-tooltip />
            <el-table-column prop="paramValue" label="参数值" width="120" align="right">
              <template slot-scope="scope">
                <span class="param-number-value">{{ formatDecimal(scope.row.paramValue) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="80">
              <template slot-scope="scope">
                <el-tag size="mini" type="info" v-if="scope.row.unit">{{ scope.row.unit }}</el-tag>
                <span v-else class="empty-data">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.remark || '-' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else class="empty-state">
          <i class="el-icon-info"></i>
          <span>该测试方案组暂无测试参数</span>
        </div>
      </el-card>

      <!-- 附件信息 -->
      <el-card v-if="detailData.attachments" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span style="font-weight: bold;">附件信息</span>
        </div>
        <el-button size="mini" type="text" @click="handleViewAttachments(detailData.attachments)">查看附件</el-button>
      </el-card>
    </el-dialog>

    <!-- 列设置对话框 -->
    <el-dialog title="列设置" :visible.sync="columnSettingVisible" width="500px" append-to-body v-drag>
      <el-checkbox-group v-model="selectedColumns">
        <el-row>
          <el-col :span="12" v-for="(label, key) in columnOptions" :key="key">
            <el-checkbox :label="key">{{ label }}</el-checkbox>
          </el-col>
        </el-row>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleColumnConfirm">确 定</el-button>
        <el-button @click="columnSettingVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 附件查看对话框 -->
    <el-dialog title="附件列表" :visible.sync="attachmentDialogVisible" width="600px" append-to-body>
      <el-table :data="attachmentList" style="width: 100%">
        <el-table-column prop="name" label="文件名" show-overflow-tooltip />
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="downloadAttachment(scope.row.url, scope.row.name)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 附件查看对话框 -->
    <el-dialog title="附件列表" :visible.sync="attachmentDialogVisible" width="600px" append-to-body v-drag>
      <el-table :data="attachmentList" style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="downloadAttachment(scope.row.url, scope.row.name)"
            >
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="attachmentDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listTestResult, getTestResult, delTestResult, addTestResult, updateTestResult,
  getTestResultOptions
} from "@/api/material/testResult";
import { listTestPlanGroup } from "@/api/material/testPlanGroup";
import { listTestParamItem } from "@/api/material/testParamItem";
import { listProcessParamGroup, getProcessParamGroup } from "@/api/material/processParamGroup";
import { listProcessParamItem } from "@/api/material/processParamItem";
import { getToken } from "@/utils/auth";

export default {
  name: "TestResult",
  directives: {
    // 拖拽指令
    drag: {
      bind(el) {
        const dialogHeaderEl = el.querySelector('.el-dialog__header');
        const dragDom = el.querySelector('.el-dialog');
        dialogHeaderEl.style.cursor = 'move';

        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);

        dialogHeaderEl.onmousedown = (e) => {
          // 鼠标按下，计算当前元素距离可视区的距离
          const disX = e.clientX - dialogHeaderEl.offsetLeft;
          const disY = e.clientY - dialogHeaderEl.offsetTop;

          // 获取到的值带px 正则匹配替换
          let styL, styT;

          // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
          if (sty.left.includes('%')) {
            styL = +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100);
            styT = +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100);
          } else {
            styL = +sty.left.replace(/px/g, '');
            styT = +sty.top.replace(/px/g, '');
          }

          document.onmousemove = function (e) {
            // 通过事件委托，计算移动的距离
            const l = e.clientX - disX;
            const t = e.clientY - disY;

            // 移动当前元素
            dragDom.style.left = `${l + styL}px`;
            dragDom.style.top = `${t + styT}px`;

            // 将此时的位置传出去
            // binding.value({x:e.pageX,y:e.pageY})
          };

          document.onmouseup = function (e) {
            document.onmousemove = null;
            document.onmouseup = null;
          };
        }
      }
    }
  },
  data() {
    return {

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 测试结果表格数据
      testResultList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 测试方案组选项
      testPlanGroupOptions: [],

      // 参数组选项
      paramGroupOptions: [],
      // 过滤后的参数组选项
      filteredParamGroupOptions: [],
      // 选中的参数详情
      selectedParamDetail: null,
      // 选中的测试方案详情
      selectedTestPlanDetail: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        testPlanCode: null,

        groupId: null,
        paramNumber: null,
        materialName: null,
        supplierName: null,
        materialModel: null,
        processType: null,
        performanceType: null,
        testEquipment: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        groupId: [
          { required: true, message: "参数编号不能为空", trigger: "change" }
        ],
        planGroupId: [
          { required: true, message: "测试方案组不能为空", trigger: "change" }
        ],

        testValue: [
          { required: true, message: "实际测试值不能为空", trigger: "blur" }
        ]
      },

      // 详情对话框
      detailDialogVisible: false,
      detailData: {},
      detailParamItems: [],
      detailTestPlanParams: null,

      // 列设置相关
      columnSettingVisible: false,
      columnOptions: {
        materialName: '材料名称',
        supplierName: '供应商',
        materialModel: '材料型号',
        processType: '工艺类型',
        paramNumber: '参数编号',
        performanceType: '性能类型',
        performanceName: '性能名称',

        testEquipment: '测试设备',
        supplierDatasheetVal: '供应商数据',
        testValue: '测试值',
        createBy: '创建人',
        createTime: '创建时间',
        updateBy: '更新人',
        updateTime: '更新时间'
      },
      selectedColumns: ['materialName', 'supplierName', 'materialModel', 'processType', 'paramNumber', 'performanceType', 'performanceName', 'testEquipment', 'supplierDatasheetVal', 'testValue', 'createBy', 'createTime', 'updateBy', 'updateTime'],
      visibleColumns: {},

      // 搜索建议数据
      paramNumberSuggestions: [],
      materialNameSuggestions: [],
      supplierNameSuggestions: [],
      materialModelSuggestions: [],
      processTypeSuggestions: [],
      performanceTypeSuggestions: [],
      testEquipmentSuggestions: [],
      testPlanGroupSuggestions: [],

      // 附件相关
      fileList: [],
      attachmentList: [],
      attachmentDialogVisible: false,
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      uploadHeaders: { Authorization: "Bearer " + getToken() }
    };
  },
  created() {
    this.getList();
    this.getTestPlanGroupOptions();
    this.getParamGroupOptions();
    this.loadSuggestions();
    this.initVisibleColumns();
  },
  methods: {
    /** 初始化可见列 */
    initVisibleColumns() {
      this.visibleColumns = {};
      this.selectedColumns.forEach(col => {
        this.visibleColumns[col] = true;
      });
    },

    /** 加载搜索建议数据 */
    loadSuggestions() {
      // 获取参数编号建议
      getTestResultOptions({ type: 'paramNumber' }).then(response => {
        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取材料名称建议
      getTestResultOptions({ type: 'materialName' }).then(response => {
        this.materialNameSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取供应商名称建议
      getTestResultOptions({ type: 'supplierName' }).then(response => {
        this.supplierNameSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取材料型号建议
      getTestResultOptions({ type: 'materialModel' }).then(response => {
        this.materialModelSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取工艺类型建议
      getTestResultOptions({ type: 'processType' }).then(response => {
        this.processTypeSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取性能类型建议
      getTestResultOptions({ type: 'performanceType' }).then(response => {
        this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取测试设备建议
      getTestResultOptions({ type: 'testEquipment' }).then(response => {
        this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});

      // 获取测试方案建议
      getTestResultOptions({ type: 'planCode' }).then(response => {
        this.testPlanSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 参数编号搜索建议 */
    queryParamNumberSuggestions(queryString, cb) {
      let suggestions = this.paramNumberSuggestions;
      if (queryString && suggestions && suggestions.length > 0) {
        suggestions = this.paramNumberSuggestions.filter(item => {
          return item && item.value && item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions || []);
    },

    /** 材料名称搜索建议 */
    queryMaterialNameSuggestions(queryString, cb) {
      let suggestions = this.materialNameSuggestions;
      if (queryString) {
        suggestions = this.materialNameSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 供应商名称搜索建议 */
    querySupplierNameSuggestions(queryString, cb) {
      let suggestions = this.supplierNameSuggestions;
      if (queryString) {
        suggestions = this.supplierNameSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 材料型号搜索建议 */
    queryMaterialModelSuggestions(queryString, cb) {
      let suggestions = this.materialModelSuggestions;
      if (queryString) {
        suggestions = this.materialModelSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 工艺类型搜索建议 */
    queryProcessTypeSuggestions(queryString, cb) {
      let suggestions = this.processTypeSuggestions;
      if (queryString) {
        suggestions = this.processTypeSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 性能类型搜索建议 */
    queryPerformanceTypeSuggestions(queryString, cb) {
      let suggestions = this.performanceTypeSuggestions;
      if (queryString) {
        suggestions = this.performanceTypeSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 测试设备搜索建议 */
    queryTestEquipmentSuggestions(queryString, cb) {
      let suggestions = this.testEquipmentSuggestions;
      if (queryString) {
        suggestions = this.testEquipmentSuggestions.filter(item => {
          return item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions);
    },

    /** 参数编号焦点事件 */
    handleParamNumberFocus() {
      getTestResultOptions({ type: 'paramNumber' }).then(response => {
        this.paramNumberSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 材料名称焦点事件 */
    handleMaterialNameFocus() {
      getTestResultOptions({ type: 'materialName' }).then(response => {
        this.materialNameSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 供应商名称焦点事件 */
    handleSupplierNameFocus() {
      getTestResultOptions({ type: 'supplierName' }).then(response => {
        this.supplierNameSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 材料型号焦点事件 */
    handleMaterialModelFocus() {
      getTestResultOptions({ type: 'materialModel' }).then(response => {
        this.materialModelSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 工艺类型焦点事件 */
    handleProcessTypeFocus() {
      getTestResultOptions({ type: 'processType' }).then(response => {
        this.processTypeSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 性能类型焦点事件 */
    handlePerformanceTypeFocus() {
      getTestResultOptions({ type: 'performanceType' }).then(response => {
        this.performanceTypeSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 测试设备焦点事件 */
    handleTestEquipmentFocus() {
      getTestResultOptions({ type: 'testEquipment' }).then(response => {
        this.testEquipmentSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 测试方案组搜索建议 */
    queryTestPlanGroupSuggestions(queryString, cb) {
      let suggestions = this.testPlanGroupSuggestions;
      if (queryString && suggestions && suggestions.length > 0) {
        suggestions = this.testPlanGroupSuggestions.filter(item => {
          return item && item.value && item.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
        });
      }
      cb(suggestions || []);
    },

    /** 测试方案组焦点事件 */
    handleTestPlanGroupFocus() {
      getTestResultOptions({ type: 'testPlanGroup' }).then(response => {
        this.testPlanGroupSuggestions = response.data.map(item => ({ value: item }));
      }).catch(() => {});
    },

    /** 测试方案组选择事件 */
    handleTestPlanGroupSelect(item) {
      this.queryParams.testPlanCode = item.value;
    },

    /** 表单测试方案改变事件 */
    handleFormTestPlanChange(value) {
      // 当测试方案改变时，可以重新加载参数详情
      if (value && this.form.groupId) {
        this.handleParamGroupChange(this.form.groupId);
      }
    },

    /** 查询测试结果列表 */
    getList() {
      this.loading = true;
      listTestResult(this.queryParams).then(response => {
        this.testResultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 获取测试方案组选项 */
    getTestPlanGroupOptions() {
      listTestPlanGroup().then(response => {
        this.testPlanGroupOptions = response.rows || [];
      }).catch(() => {
        this.testPlanGroupOptions = [];
      });
    },



    /** 处理测试方案组变化 */
    handleFormPlanGroupChange(planGroupId) {
      this.selectedTestPlanDetail = null;

      if (!planGroupId) {
        return;
      }

      // 获取选中的测试方案组详情
      const selectedGroup = this.testPlanGroupOptions.find(group => group.planGroupId === planGroupId);
      if (selectedGroup) {
        // 加载该测试方案组下的所有测试参数明细
        listTestParamItem({ planGroupId: planGroupId }).then(response => {
          this.selectedTestPlanDetail = {
            planCode: selectedGroup.planCode,
            performanceType: selectedGroup.performanceType,
            performanceName: selectedGroup.performanceName,
            testEquipment: selectedGroup.testEquipment,
            testParamItems: response.rows || []
          };

          // 强制更新视图
          this.$forceUpdate();
        }).catch(error => {
          console.error('加载测试参数明细失败:', error);
          this.selectedTestPlanDetail = {
            planCode: selectedGroup.planCode,
            performanceType: selectedGroup.performanceType,
            performanceName: selectedGroup.performanceName,
            testEquipment: selectedGroup.testEquipment,
            testParamItems: []
          };
        });
      }
    },





    /** 获取参数组选项 */
    getParamGroupOptions() {
      listProcessParamGroup().then(response => {
        this.paramGroupOptions = response.rows;
        this.filteredParamGroupOptions = response.rows;
      });
    },

    /** 方案改变事件 */
    handlePlanChange(value) {
      // 可以根据方案过滤参数组
      this.queryParams.groupId = null;
      this.handleQuery();
    },



    /** 参数组改变事件 */
    handleParamGroupChange(value) {
      if (value) {
        // 先清空之前的数据
        this.selectedParamDetail = null;

        // 获取参数组详情
        getProcessParamGroup(value).then(response => {
          this.selectedParamDetail = response.data;
          console.log('获取到的参数组详情：', this.selectedParamDetail);

          // 立即获取参数明细，不需要等待测试方案选择
          listProcessParamItem({ groupId: value }).then(paramResponse => {
            if (this.selectedParamDetail) {
              this.selectedParamDetail.paramItems = paramResponse.rows || [];
              // 为每个参数项添加显示文本，包含参数值
              this.selectedParamDetail.paramItems.forEach(item => {
                let displayText = item.paramName || 'N/A';
                if (item.paramValue !== null && item.paramValue !== undefined) {
                  displayText += `: ${this.formatDecimal(item.paramValue)}`;
                }
                if (item.unit) {
                  displayText += ` ${item.unit}`;
                }
                item.displayText = displayText;
              });
              console.log('获取到的参数明细：', this.selectedParamDetail.paramItems);

              // 强制更新视图
              this.$forceUpdate();
            }
          }).catch(error => {
            console.error('获取参数明细失败：', error);
            if (this.selectedParamDetail) {
              this.selectedParamDetail.paramItems = [];
            }
          });
        }).catch(error => {
          console.error('获取参数组详情失败：', error);
          this.selectedParamDetail = null;
        });
      } else {
        this.selectedParamDetail = null;
      }
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        testResultId: null,
        planGroupId: null,
        groupId: null,
        supplierDatasheetVal: null,
        testValue: null,
        attachments: null,
        remark: null
      };
      this.fileList = [];
      this.selectedParamDetail = null;
      this.selectedTestPlanDetail = null;
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.testResultId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    // 行点击选择
    handleRowClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row);
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加测试结果";
    },

    /** 修改按钮操作 */
    handleEdit(row) {
      this.reset();
      const testResultId = row.testResultId || this.ids[0];
      getTestResult(testResultId).then(response => {
        console.log('编辑获取的数据：', response.data);
        this.form = response.data;
        // 解析附件数据
        this.fileList = this.parseAttachments(response.data.attachments);
        console.log('编辑时解析的文件列表：', this.fileList);
        this.open = true;
        this.title = "修改测试结果";

        // 触发参数组改变事件以加载材料参数详情
        if (this.form.groupId) {
          this.handleParamGroupChange(this.form.groupId);
        }

        // 触发测试方案组改变事件以加载测试方案参数详情
        if (this.form.planGroupId) {
          this.handleFormPlanGroupChange(this.form.planGroupId);
        }
      });
    },

    /** 详情按钮操作 */
    handleDetail(row) {
      this.detailData = row;
      this.detailParamItems = [];
      this.detailTestPlanParams = null;

      // 如果有参数组ID，获取参数明细
      if (row.groupId) {
        listProcessParamItem({ groupId: row.groupId }).then(response => {
          this.detailParamItems = response.rows || [];
        }).catch(error => {
          console.error('获取参数明细失败：', error);
          this.detailParamItems = [];
        });
      }

      // 如果有测试方案组ID，获取测试方案参数信息
      if (row.planGroupId) {
        // 从当前的测试方案组选项中查找对应的方案信息
        const planGroup = this.testPlanGroupOptions.find(group => group.planGroupId === row.planGroupId);
        if (planGroup) {
          // 获取测试方案组下的所有测试参数
          listTestParamItem({ planGroupId: row.planGroupId }).then(response => {
            this.detailTestPlanParams = {
              planCode: planGroup.planCode,
              performanceType: planGroup.performanceType,
              performanceName: planGroup.performanceName,
              testEquipment: planGroup.testEquipment,
              testParams: response.rows || []
            };
          }).catch(error => {
            console.error('获取测试方案参数失败：', error);
          });
        }
      }

      this.detailDialogVisible = true;
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 将附件列表转换为逗号分隔的URL字符串
          if (this.fileList && this.fileList.length > 0) {
            this.form.attachments = this.fileList.map(file => file.url).join(',');
          } else {
            this.form.attachments = '';
          }

          // testPlanId已经直接选择，无需转换

          // 设置创建人和更新人
          if (this.form.testResultId != null) {
            // 更新操作，设置更新人
            this.form.updateBy = this.$store.state.user.name;
          } else {
            // 新增操作，设置创建人
            this.form.createBy = this.$store.state.user.name;
          }

          console.log('提交的表单数据：', this.form);

          if (this.form.testResultId != null) {
            updateTestResult(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTestResult(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const testResultIds = row.testResultId || this.ids;
      this.$modal.confirm('是否确认删除测试结果编号为"' + testResultIds + '"的数据项？').then(function() {
        return delTestResult(testResultIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('material/testResult/export', {
        ...this.queryParams
      }, `test_result_${new Date().getTime()}.xlsx`);
    },

    /** 列设置 */
    handleColumnSetting() {
      this.columnSettingVisible = true;
    },

    /** 列设置确认 */
    handleColumnConfirm() {
      this.visibleColumns = {};
      this.selectedColumns.forEach(col => {
        this.visibleColumns[col] = true;
      });
      this.columnSettingVisible = false;
    },

    /** 附件上传成功 */
    handleUploadSuccess(response, file, fileList) {
      console.log('上传成功回调：', { response, file, fileList });
      if (response.code === 200) {
        // 确保fileList是数组
        if (Array.isArray(fileList)) {
          this.fileList = fileList.map(item => ({
            name: item.name,
            url: item.response ? item.response.url : item.url,
            size: this.formatFileSize(item.size || item.raw?.size),
            uid: item.uid,
            status: 'success'
          }));
        } else {
          console.error('fileList不是数组：', fileList);
          this.fileList = [];
        }
        this.$modal.msgSuccess("上传成功");
      } else {
        this.$modal.msgError(response.msg || "上传失败");
      }
    },

    /** 附件移除 */
    handleFileRemove(file, fileList) {
      console.log('附件移除回调：', { file, fileList });
      // 确保fileList是数组
      if (Array.isArray(fileList)) {
        this.fileList = fileList.map(item => ({
          name: item.name,
          url: item.response ? item.response.url : item.url,
          size: this.formatFileSize(item.size || item.raw?.size),
          uid: item.uid,
          status: item.status || 'success'
        }));
      } else {
        console.error('fileList不是数组：', fileList);
        this.fileList = [];
      }
      this.$modal.msgSuccess("附件删除成功");
    },

    /** 附件上传前检查 */
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
      }
      return isLt10M;
    },

    /** 查看附件 */
    handleViewAttachments(attachments) {
      console.log('查看附件被调用，附件数据：', attachments, '类型：', typeof attachments);

      this.attachmentList = [];

      if (!attachments) {
        console.log('附件数据为空');
        this.attachmentDialogVisible = true;
        return;
      }

      try {
        if (typeof attachments === 'string') {
          const trimmed = attachments.trim();
          if (!trimmed) {
            console.log('附件字符串为空');
            this.attachmentDialogVisible = true;
            return;
          }

          // 尝试解析JSON格式
          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
            try {
              const parsed = JSON.parse(trimmed);
              if (Array.isArray(parsed)) {
                this.attachmentList = parsed.map((item, index) => ({
                  name: item.name || `附件${index + 1}`,
                  url: item.url || item,
                  size: item.size || '未知大小'
                }));
              }
            } catch (jsonError) {
              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);
              // 按逗号分割处理
              this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {
                const cleanUrl = url.trim();
                const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;
                return {
                  name: fileName,
                  url: cleanUrl,
                  size: '未知大小'
                };
              });
            }
          } else {
            // 按逗号分割处理
            this.attachmentList = trimmed.split(',').filter(url => url.trim()).map((url, index) => {
              const cleanUrl = url.trim();
              const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;
              return {
                name: fileName,
                url: cleanUrl,
                size: '未知大小'
              };
            });
          }
        } else if (Array.isArray(attachments)) {
          this.attachmentList = attachments.map((item, index) => ({
            name: item.name || `附件${index + 1}`,
            url: item.url || item,
            size: item.size || '未知大小'
          }));
        }
      } catch (error) {
        console.error('解析附件数据时发生错误：', error);
        this.attachmentList = [];
      }

      console.log('解析后的附件列表：', this.attachmentList);
      this.attachmentDialogVisible = true;
    },

    /** 下载附件 */
    downloadAttachment(url, fileName) {
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /** 解析附件数据 */
    parseAttachments(attachments) {
      console.log('解析附件数据：', attachments, '类型：', typeof attachments);
      if (!attachments) {
        return [];
      }

      try {
        // 如果已经是数组，直接返回
        if (Array.isArray(attachments)) {
          return attachments.map((item, index) => ({
            name: item.name || `附件${index + 1}`,
            url: item.url || item,
            uid: item.uid || Date.now() + index,
            status: 'success'
          }));
        }

        // 如果是字符串，尝试解析
        if (typeof attachments === 'string') {
          const trimmed = attachments.trim();
          if (!trimmed) {
            return [];
          }

          // 尝试解析JSON格式
          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
            try {
              const parsed = JSON.parse(trimmed);
              if (Array.isArray(parsed)) {
                return parsed.map((item, index) => ({
                  name: item.name || `附件${index + 1}`,
                  url: item.url || item,
                  uid: item.uid || Date.now() + index,
                  status: 'success'
                }));
              }
            } catch (jsonError) {
              console.warn('JSON解析失败，尝试按逗号分割：', jsonError);
            }
          }

          // 按逗号分割处理
          const urls = trimmed.split(',').filter(url => url.trim());
          console.log('分割后的URL列表：', urls);
          return urls.map((url, index) => {
            const cleanUrl = url.trim();
            const fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1) || `附件${index + 1}`;
            return {
              name: fileName,
              url: cleanUrl,
              uid: Date.now() + index,
              status: 'success'
            };
          });
        }
      } catch (error) {
        console.error('解析附件数据时发生错误：', error);
      }

      return [];
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return '未知大小';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return Math.round(size * 100) / 100 + ' ' + units[index];
    },

    /** 格式化小数 */
    formatDecimal(value) {
      if (value === null || value === undefined || value === '') {
        return '-';
      }
      const num = parseFloat(value);
      if (isNaN(num)) {
        return value;
      }
      // 如果是整数，直接返回，不添加.00
      if (num % 1 === 0) {
        return num.toString();
      }
      // 保留两位小数
      return num.toFixed(2);
    }
    },

    /** 下载附件 */
    downloadAttachment(url, name) {
      const link = document.createElement('a');
      link.href = url;
      link.download = name;
      link.click();
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB';
      }
    }
  }
</script>

<style scoped>
.app-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-title i {
  margin-right: 10px;
  color: #409EFF;
  font-size: 28px;
}

.page-description p {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

/* 增强卡片样式 */
.enhanced-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  background: white;
}

.enhanced-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left i {
  color: #409EFF;
  font-size: 18px;
}

.header-title {
  font-weight: bold;
  font-size: 16px;
  color: #2c3e50;
}

.item-count-badge {
  margin-left: 8px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.header-right .el-button {
  border-radius: 6px;
  font-weight: 500;
}

.column-setting-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.column-setting-btn:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* 搜索区域样式 */
.search-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 15px;
  border: 1px solid #e9ecef;
}

.search-form {
  margin: 0;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.form-row .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  flex: 1;
  min-width: 250px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 20px;
}

.reset-btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 20px;
}

/* 通用样式 */
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.dialog-footer {
  text-align: center;
}

.el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
}

/* 参数详情卡片样式 */
.param-detail-card, .test-plan-detail-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
}

.param-descriptions {
  margin-bottom: 0;
}

.param-value {
  font-weight: 600;
  color: #2c3e50;
}

.param-number-value {
  font-weight: 600;
  color: #67C23A;
  font-family: 'Courier New', monospace;
}

.param-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-weight: bold;
  color: #606266;
  font-size: 14px;
}

.param-detail-table {
  border-radius: 4px;
}

.param-detail-table .el-table__header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.param-detail-table .el-table__header th {
  background: transparent;
  color: #495057;
  font-weight: 600;
  border-bottom: 1px solid #dee2e6;
}

.empty-state {
  margin-top: 15px;
  text-align: center;
  color: #909399;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.empty-data {
  color: #C0C4CC;
  font-style: italic;
}

.detail-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.detail-item span {
  color: #303133;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .page-header {
    padding: 15px;
  }

  .header-right {
    flex-wrap: wrap;
  }

  .form-row {
    flex-direction: column;
  }

  .form-row .el-form-item {
    min-width: 100%;
  }

  .search-section {
    padding: 15px;
  }
}

/* 统一按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}

.el-button--success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
  border: none !important;
}

.el-button--info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  border: none !important;
}

.el-button--warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  border: none !important;
}

.el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
  border: none !important;
}
</style>
