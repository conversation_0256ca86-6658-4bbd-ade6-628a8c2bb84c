<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProcessParamItemMapper">
    
    <resultMap type="ProcessParamItem" id="ProcessParamItemResult">
        <result property="itemId"    column="item_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="paramName"    column="param_name"    />
        <result property="paramValue"    column="param_value"    />
        <result property="unit"    column="unit"    />
        <result property="attachments"    column="attachments"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProcessParamItemVo">
        select item_id, group_id, param_name, param_value, unit, attachments, remark, create_by, create_time, update_by, update_time from process_param_item
    </sql>

    <select id="selectProcessParamItemList" parameterType="ProcessParamItem" resultMap="ProcessParamItemResult">
        <include refid="selectProcessParamItemVo"/>
        <where>  
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="paramName != null  and paramName != ''"> and param_name like concat('%', #{paramName}, '%')</if>
            <if test="paramValue != null "> and param_value = #{paramValue}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
        </where>
    </select>
    
    <select id="selectProcessParamItemByItemId" parameterType="Long" resultMap="ProcessParamItemResult">
        <include refid="selectProcessParamItemVo"/>
        where item_id = #{itemId}
    </select>

    <select id="selectProcessParamItemByGroupId" parameterType="Long" resultMap="ProcessParamItemResult">
        <include refid="selectProcessParamItemVo"/>
        where group_id = #{groupId}
        order by item_id
    </select>
        
    <insert id="insertProcessParamItem" parameterType="ProcessParamItem" useGeneratedKeys="true" keyProperty="itemId">
        insert into process_param_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupId != null">group_id,</if>
            <if test="paramName != null and paramName != ''">param_name,</if>
            <if test="paramValue != null">param_value,</if>
            <if test="unit != null">unit,</if>
            <if test="attachments != null">attachments,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupId != null">#{groupId},</if>
            <if test="paramName != null and paramName != ''">#{paramName},</if>
            <if test="paramValue != null">#{paramValue},</if>
            <if test="unit != null">#{unit},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateProcessParamItem" parameterType="ProcessParamItem">
        update process_param_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="paramName != null and paramName != ''">param_name = #{paramName},</if>
            <if test="paramValue != null">param_value = #{paramValue},</if>
            <if test="unit != null">unit = #{unit},</if>
            attachments = #{attachments},
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where item_id = #{itemId}
    </update>

    <delete id="deleteProcessParamItemByItemId" parameterType="Long">
        delete from process_param_item where item_id = #{itemId}
    </delete>

    <delete id="deleteProcessParamItemByItemIds" parameterType="String">
        delete from process_param_item where item_id in 
        <foreach item="itemId" collection="array" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </delete>
    
    <select id="selectParamNameOptions" resultType="String">
        select distinct param_name from process_param_item where param_name is not null and param_name != '' order by param_name
    </select>
    
    <select id="selectUnitOptions" resultType="String">
        select distinct unit from process_param_item where unit is not null and unit != '' order by unit
    </select>

</mapper>
