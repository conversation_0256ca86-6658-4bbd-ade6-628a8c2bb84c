<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TestPlanMapper">
    
    <resultMap type="TestPlan" id="TestPlanResult">
        <result property="testPlanId"    column="test_plan_id"    />
        <result property="planCode"    column="plan_code"    />
        <result property="performanceType"    column="performance_type"    />
        <result property="performanceName"    column="performance_name"    />
        <result property="testEquipment"    column="test_equipment"    />
        <result property="testParameter"    column="test_parameter"    />
        <result property="attachments"    column="attachments"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTestPlanVo">
        select test_plan_id, plan_code, performance_type, performance_name, test_equipment, test_parameter, attachments, remark, create_by, create_time, update_by, update_time from test_plans
    </sql>

    <select id="selectTestPlanList" parameterType="TestPlan" resultMap="TestPlanResult">
        <include refid="selectTestPlanVo"/>
        <where>  
            <if test="planCode != null  and planCode != ''"> and plan_code like concat('%', #{planCode}, '%')</if>
            <if test="performanceType != null  and performanceType != ''"> and performance_type like concat('%', #{performanceType}, '%')</if>
            <if test="performanceName != null  and performanceName != ''"> and performance_name like concat('%', #{performanceName}, '%')</if>
            <if test="testEquipment != null  and testEquipment != ''"> and test_equipment like concat('%', #{testEquipment}, '%')</if>
            <if test="testParameter != null  and testParameter != ''"> and test_parameter like concat('%', #{testParameter}, '%')</if>
        </where>
    </select>
    
    <select id="selectTestPlanByTestPlanId" parameterType="Long" resultMap="TestPlanResult">
        <include refid="selectTestPlanVo"/>
        where test_plan_id = #{testPlanId}
    </select>
        
    <insert id="insertTestPlan" parameterType="TestPlan" useGeneratedKeys="true" keyProperty="testPlanId">
        insert into test_plans
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planCode != null">plan_code,</if>
            <if test="performanceType != null">performance_type,</if>
            <if test="performanceName != null">performance_name,</if>
            <if test="testEquipment != null">test_equipment,</if>
            <if test="testParameter != null">test_parameter,</if>
            <if test="attachments != null">attachments,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planCode != null">#{planCode},</if>
            <if test="performanceType != null">#{performanceType},</if>
            <if test="performanceName != null">#{performanceName},</if>
            <if test="testEquipment != null">#{testEquipment},</if>
            <if test="testParameter != null">#{testParameter},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateTestPlan" parameterType="TestPlan">
        update test_plans
        <trim prefix="SET" suffixOverrides=",">
            <if test="planCode != null">plan_code = #{planCode},</if>
            <if test="performanceType != null">performance_type = #{performanceType},</if>
            <if test="performanceName != null">performance_name = #{performanceName},</if>
            <if test="testEquipment != null">test_equipment = #{testEquipment},</if>
            <if test="testParameter != null">test_parameter = #{testParameter},</if>
            attachments = #{attachments},
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where test_plan_id = #{testPlanId}
    </update>

    <delete id="deleteTestPlanByTestPlanId" parameterType="Long">
        delete from test_plans where test_plan_id = #{testPlanId}
    </delete>

    <delete id="deleteTestPlanByTestPlanIds" parameterType="String">
        delete from test_plans where test_plan_id in 
        <foreach item="testPlanId" collection="array" open="(" separator="," close=")">
            #{testPlanId}
        </foreach>
    </delete>

    <select id="selectPlanCodeOptions" resultType="String">
        SELECT DISTINCT plan_code FROM test_plans
        WHERE plan_code IS NOT NULL AND plan_code != ''
        ORDER BY plan_code
    </select>

    <select id="selectPerformanceTypeOptions" resultType="String">
        SELECT DISTINCT performance_type FROM test_plans
        WHERE performance_type IS NOT NULL AND performance_type != ''
        ORDER BY performance_type
    </select>

    <select id="selectTestEquipmentOptions" resultType="String">
        SELECT DISTINCT test_equipment FROM test_plans
        WHERE test_equipment IS NOT NULL AND test_equipment != ''
        ORDER BY test_equipment
    </select>

</mapper>
