package com.ruoyi.web.controller.material;

import java.util.List;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.ITrendService;

/**
 * 趋势对比Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/material/trend")
public class TrendController extends BaseController
{
    @Autowired
    private ITrendService trendService;

    /**
     * 获取趋势对比数据
     */
    @PreAuthorize("@ss.hasPermi('material:trend:list')")
    @GetMapping("/data")
    public AjaxResult getTrendData(String paramNumbers, String performanceNames)
    {
        Map<String, Object> data = trendService.getTrendData(paramNumbers, performanceNames);
        return AjaxResult.success(data);
    }

    /**
     * 获取参数编号选项
     */
    @GetMapping("/paramNumbers")
    public AjaxResult getParamNumbers()
    {
        List<String> paramNumbers = trendService.getParamNumbers();
        return AjaxResult.success(paramNumbers);
    }

    /**
     * 获取性能名称选项
     */
    @GetMapping("/performanceNames")
    public AjaxResult getPerformanceNames()
    {
        List<String> performanceNames = trendService.getPerformanceNames();
        return AjaxResult.success(performanceNames);
    }

    /**
     * 根据参数编号获取参数详情
     */
    @GetMapping("/paramDetails")
    public AjaxResult getParamDetails(String paramNumber)
    {
        Map<String, Object> details = trendService.getParamDetails(paramNumber);
        return AjaxResult.success(details);
    }
}