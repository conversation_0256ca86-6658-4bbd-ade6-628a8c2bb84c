package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 测试方案对象 test_plans
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class TestPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测试方案ID */
    private Long testPlanId;

    /** 方案编号 */
    @Excel(name = "方案编号")
    private String planCode;

    /** 性能类型 */
    @Excel(name = "性能类型")
    private String performanceType;

    /** 方案/性能名称 */
    @Excel(name = "方案/性能名称")
    private String performanceName;

    /** 测试设备 */
    @Excel(name = "测试设备")
    private String testEquipment;

    /** 测试参数 */
    @Excel(name = "测试参数")
    private String testParameter;

    /** 附件URL(多个用逗号分隔) */
    private String attachments;

    public void setTestPlanId(Long testPlanId)
    {
        this.testPlanId = testPlanId;
    }

    public Long getTestPlanId()
    {
        return testPlanId;
    }
    public void setPlanCode(String planCode)
    {
        this.planCode = planCode;
    }

    public String getPlanCode()
    {
        return planCode;
    }
    public void setPerformanceType(String performanceType)
    {
        this.performanceType = performanceType;
    }

    public String getPerformanceType()
    {
        return performanceType;
    }
    public void setPerformanceName(String performanceName)
    {
        this.performanceName = performanceName;
    }

    public String getPerformanceName()
    {
        return performanceName;
    }
    public void setTestEquipment(String testEquipment)
    {
        this.testEquipment = testEquipment;
    }

    public String getTestEquipment()
    {
        return testEquipment;
    }
    public void setTestParameter(String testParameter)
    {
        this.testParameter = testParameter;
    }

    public String getTestParameter()
    {
        return testParameter;
    }
    public void setAttachments(String attachments)
    {
        this.attachments = attachments;
    }

    public String getAttachments()
    {
        return attachments;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("testPlanId", getTestPlanId())
                .append("planCode", getPlanCode())
                .append("performanceType", getPerformanceType())
                .append("performanceName", getPerformanceName())
                .append("testEquipment", getTestEquipment())
                .append("testParameter", getTestParameter())
                .append("attachments", getAttachments())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}