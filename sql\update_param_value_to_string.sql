-- 材料明细参数数值字段类型修改脚本
-- 将 param_value 字段从 DECIMAL(20,6) 改为 VARCHAR(100)
-- 执行前请备份数据库！

-- 1. 备份现有数据
CREATE TABLE IF NOT EXISTS process_param_item_backup AS 
SELECT * FROM process_param_item;

-- 2. 修改字段类型
ALTER TABLE process_param_item 
MODIFY COLUMN param_value VARCHAR(100) DEFAULT NULL COMMENT '参数数值（字符串格式）';

-- 3. 验证修改结果
DESCRIBE process_param_item;

-- 4. 检查数据完整性
SELECT 
    COUNT(*) as '总记录数',
    COUNT(param_value) as '有数值的记录数',
    COUNT(CASE WHEN param_value IS NULL OR param_value = '' THEN 1 END) as '空值记录数'
FROM process_param_item;

-- 5. 显示修改后的示例数据
SELECT 
    item_id,
    param_name,
    param_value,
    unit,
    create_time
FROM process_param_item 
LIMIT 10;

-- 修改完成提示
SELECT '参数数值字段类型修改完成！现在支持字符串格式的数据录入。' as '修改状态';
