package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

/**
 * 趋势对比Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface TrendMapper 
{
    /**
     * 获取趋势对比数据
     * 
     * @param paramNumbers 参数编号数组
     * @param performanceNames 性能名称数组
     * @return 趋势数据
     */
    public List<Map<String, Object>> selectTrendData(@Param("paramNumbers") String[] paramNumbers, 
                                                     @Param("performanceNames") String[] performanceNames);

    /**
     * 获取所有参数编号
     * 
     * @return 参数编号列表
     */
    public List<String> selectParamNumbers();

    /**
     * 获取所有性能名称
     * 
     * @return 性能名称列表
     */
    public List<String> selectPerformanceNames();

    /**
     * 根据参数编号获取参数详情
     * 
     * @param paramNumber 参数编号
     * @return 参数详情
     */
    public Map<String, Object> selectParamDetails(@Param("paramNumber") String paramNumber);
}