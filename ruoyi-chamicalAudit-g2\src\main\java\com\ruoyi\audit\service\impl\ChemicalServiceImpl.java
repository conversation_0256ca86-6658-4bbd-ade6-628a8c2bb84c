package com.ruoyi.audit.service.impl;

import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.audit.mapper.ChemicalMapper;
import com.ruoyi.audit.domain.Chemical;
import com.ruoyi.audit.service.IChemicalService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 化学检测数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ChemicalServiceImpl implements IChemicalService 
{
    private static final Logger log = LoggerFactory.getLogger(ChemicalServiceImpl.class);

    @Autowired
    private ChemicalMapper chemicalMapper;

    /**
     * 查询化学检测数据
     * 
     * @param id 化学检测数据主键
     * @return 化学检测数据
     */
    @Override
    public Chemical selectChemicalById(String id)
    {
        return chemicalMapper.selectChemicalById(id);
    }

    /**
     * 查询化学检测数据列表
     * 
     * @param chemical 化学检测数据
     * @return 化学检测数据
     */
    @Override
    public List<Chemical> selectChemicalList(Chemical chemical)
    {
        return chemicalMapper.selectChemicalList(chemical);
    }

    /**
     * 新增化学检测数据
     * 
     * @param chemical 化学检测数据
     * @return 结果
     */
    @Override
    public int insertChemical(Chemical chemical)
    {
        chemical.setCreateTime(DateUtils.getNowDate());
        return chemicalMapper.insertChemical(chemical);
    }

    /**
     * 修改化学检测数据
     * 
     * @param chemical 化学检测数据
     * @return 结果
     */
    @Override
    public int updateChemical(Chemical chemical)
    {
        chemical.setUpdateTime(DateUtils.getNowDate());
        return chemicalMapper.updateChemical(chemical);
    }

    /**
     * 批量删除化学检测数据
     * 
     * @param ids 需要删除的化学检测数据主键
     * @return 结果
     */
    @Override
    public int deleteChemicalByIds(String[] ids)
    {
        return chemicalMapper.deleteChemicalByIds(ids);
    }

    /**
     * 删除化学检测数据信息
     * 
     * @param id 化学检测数据主键
     * @return 结果
     */
    @Override
    public int deleteChemicalById(String id)
    {
        return chemicalMapper.deleteChemicalById(id);
    }

    /**
     * 查询未导出的化学检测数据
     * 
     * @param chemical 查询条件
     * @return 化学检测数据集合
     */
    @Override
    public List<Chemical> selectUnexportedChemicalList(Chemical chemical)
    {
        return chemicalMapper.selectUnexportedChemicalList(chemical);
    }

    /**
     * 查询未处理的化学检测数据
     * 
     * @param chemical 查询条件
     * @return 化学检测数据集合
     */
    @Override
    public List<Chemical> selectUnprocessedChemicalList(Chemical chemical)
    {
        return chemicalMapper.selectUnprocessedChemicalList(chemical);
    }

    /**
     * 批量更新导出状态
     * 
     * @param ids 需要更新的数据主键集合
     * @return 结果
     */
    @Override
    public int updateExportedStatusByIds(String[] ids)
    {
        return chemicalMapper.updateExportedStatusByIds(ids);
    }

    /**
     * 批量更新处理状态
     * 
     * @param ids 需要更新的数据主键集合
     * @return 结果
     */
    @Override
    public int updateProcessedStatusByIds(String[] ids)
    {
        return chemicalMapper.updateProcessedStatusByIds(ids);
    }

    /**
     * 标记为不处理
     * 
     * @param id 数据主键
     * @return 结果
     */
    @Override
    public int markAsNotProcess(String id)
    {
        return chemicalMapper.markAsNotProcess(id);
    }

    /**
     * 标记为已导出
     * 
     * @param id 数据主键
     * @return 结果
     */
    @Override
    public int markAsExported(String id)
    {
        return chemicalMapper.markAsExported(id);
    }

    /**
     * 重置处理状态
     * 
     * @param id 数据主键
     * @return 结果
     */
    @Override
    public int resetProcessStatus(String id)
    {
        return chemicalMapper.resetProcessStatus(id);
    }

    /**
     * 重新处理数据
     * 
     * @param ids 需要重新处理的数据主键集合
     * @return 结果
     */
    @Override
    public int reprocessChemicalData(String[] ids)
    {
        try {
            // 重置处理状态
            for (String id : ids) {
                resetProcessStatus(id);
            }
            
            // 触发数据处理逻辑
            // 实现数据重新处理逻辑
            for (String id : ids) {
                Chemical chemical = chemicalMapper.selectChemicalById(id);
                if (chemical != null) {
                    // 重新验证数据
                    validateChemicalData(chemical);

                    // 重新计算相关值
                    recalculateChemicalValues(chemical);

                    // 更新数据库
                    chemical.setUpdateTime(new Date());
                    chemicalMapper.updateChemical(chemical);
                }
            }
            
            log.info("重新处理化学数据完成，处理数量: {}", ids.length);
            return ids.length;
        } catch (Exception e) {
            log.error("重新处理化学数据失败", e);
            return 0;
        }
    }

    /**
     * 从Pulsar消息队列接收数据
     * 
     * @param messageData 消息数据
     * @return 处理结果
     */
    @Override
    public int processMessageData(String messageData)
    {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Chemical chemical = objectMapper.readValue(messageData, Chemical.class);
            
            if (chemical != null && chemical.getId() != null) {
                // 设置插入时间
                chemical.setInsertionTime(new Date());
                chemical.setCreateTime(new Date());
                
                // 插入数据库
                int result = insertChemical(chemical);
                
                if (result > 0) {
                    log.info("成功处理Pulsar消息，ID: {}", chemical.getId());
                } else {
                    log.warn("插入化学数据失败，ID: {}", chemical.getId());
                }
                
                return result;
            } else {
                log.warn("无效的化学数据消息");
                return 0;
            }
            
        } catch (Exception e) {
            log.error("处理Pulsar消息失败", e);
            return 0;
        }
    }

    /**
     * 获取数据统计信息
     *
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getDataStatistics()
    {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 总数据量
            int totalCount = chemicalMapper.countChemicalList(new Chemical());
            statistics.put("totalCount", totalCount);

            // 今日新增数据
            int todayCount = chemicalMapper.countTodayNewData();
            statistics.put("todayCount", todayCount);

            // 已导出数据
            Chemical exportedQuery = new Chemical();
            exportedQuery.setIsExported(true);
            int exportedCount = chemicalMapper.countChemicalList(exportedQuery);
            statistics.put("exportedCount", exportedCount);

            // 未处理数据
            Chemical notProcessQuery = new Chemical();
            notProcessQuery.setNotProcess(false);
            int notProcessCount = chemicalMapper.countChemicalList(notProcessQuery);
            statistics.put("notProcessCount", notProcessCount);

            // 不同层号的数据分布
            List<String> layerNumbers = chemicalMapper.selectDistinctLayerNumbers();
            statistics.put("layerNumbers", layerNumbers);

            statistics.put("success", true);

        } catch (Exception e) {
            log.error("获取数据统计信息失败", e);
            statistics.put("success", false);
            statistics.put("message", "获取统计信息失败: " + e.getMessage());
        }

        return statistics;
    }

    /**
     * 处理化学数据
     *
     * @return 处理结果
     */
    @Override
    public Map<String, Object> processChemicalData()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询未处理的数据
            Chemical queryParam = new Chemical();
            queryParam.setNotProcess(false);
            List<Chemical> unprocessedData = chemicalMapper.selectChemicalList(queryParam);

            int processedCount = 0;
            int errorCount = 0;

            for (Chemical chemical : unprocessedData) {
                try {
                    // 这里可以添加具体的数据处理逻辑
                    // 例如数据验证、格式化等

                    // 更新处理状态
                    chemical.setUpdateTime(new Date());
                    updateChemical(chemical);

                    processedCount++;

                } catch (Exception e) {
                    log.error("处理化学数据失败，ID: {}", chemical.getId(), e);
                    errorCount++;
                }
            }

            result.put("success", true);
            result.put("message", "数据处理完成");
            result.put("totalCount", unprocessedData.size());
            result.put("processedCount", processedCount);
            result.put("errorCount", errorCount);

        } catch (Exception e) {
            log.error("处理化学数据失败", e);
            result.put("success", false);
            result.put("message", "数据处理失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证化学数据
     */
    private void validateChemicalData(Chemical chemical) {
        if (chemical == null) {
            throw new IllegalArgumentException("化学数据不能为空");
        }

        // 验证检测值1
        if (chemical.getExamine1() != null && !chemical.getExamine1().trim().isEmpty()) {
            try {
                Double.parseDouble(chemical.getExamine1());
            } catch (NumberFormatException e) {
                log.warn("检测值1格式无效: {} - {}", chemical.getId(), chemical.getExamine1());
                chemical.setExamine1("0"); // 设置默认值
            }
        }

        // 验证检测值2
        if (chemical.getExamine2() != null && !chemical.getExamine2().trim().isEmpty()) {
            try {
                Double.parseDouble(chemical.getExamine2());
            } catch (NumberFormatException e) {
                log.warn("检测值2格式无效: {} - {}", chemical.getId(), chemical.getExamine2());
                chemical.setExamine2("0"); // 设置默认值
            }
        }

        // 验证上下限
        if (chemical.getUpperLimit() != null && !chemical.getUpperLimit().trim().isEmpty()) {
            try {
                Double.parseDouble(chemical.getUpperLimit());
            } catch (NumberFormatException e) {
                log.warn("上限值格式无效: {} - {}", chemical.getId(), chemical.getUpperLimit());
            }
        }

        if (chemical.getDownLimit() != null && !chemical.getDownLimit().trim().isEmpty()) {
            try {
                Double.parseDouble(chemical.getDownLimit());
            } catch (NumberFormatException e) {
                log.warn("下限值格式无效: {} - {}", chemical.getId(), chemical.getDownLimit());
            }
        }
    }

    /**
     * 重新计算化学值
     */
    private void recalculateChemicalValues(Chemical chemical) {
        if (chemical == null) {
            return;
        }

        try {
            // 如果检测值1和检测值2都有值，可以进行一些计算
            if (chemical.getExamine1() != null && !chemical.getExamine1().trim().isEmpty() &&
                chemical.getExamine2() != null && !chemical.getExamine2().trim().isEmpty()) {

                double examine1 = Double.parseDouble(chemical.getExamine1());
                double examine2 = Double.parseDouble(chemical.getExamine2());

                // 可以在这里添加一些计算逻辑，比如平均值、差值等
                // 例如：计算平均值并存储到某个字段
                double average = (examine1 + examine2) / 2.0;
                log.debug("重新计算平均值: {} - {}", chemical.getId(), average);
            }

            // 检查数据是否在规格范围内
            if (chemical.getExamine1() != null && !chemical.getExamine1().trim().isEmpty() &&
                chemical.getUpperLimit() != null && !chemical.getUpperLimit().trim().isEmpty() &&
                chemical.getDownLimit() != null && !chemical.getDownLimit().trim().isEmpty()) {

                try {
                    double examine1 = Double.parseDouble(chemical.getExamine1());
                    double upperLimit = Double.parseDouble(chemical.getUpperLimit());
                    double downLimit = Double.parseDouble(chemical.getDownLimit());

                    // 检查是否超出规格
                    if (examine1 > upperLimit || examine1 < downLimit) {
                        log.debug("数据超出规格范围: {} - 值:{}, 范围:[{}, {}]",
                                chemical.getId(), examine1, downLimit, upperLimit);
                    }
                } catch (NumberFormatException e) {
                    log.warn("规格限制值格式错误: {}", chemical.getId());
                }
            }

        } catch (Exception e) {
            log.error("重新计算化学值失败: {}", chemical.getId(), e);
        }
    }
}
