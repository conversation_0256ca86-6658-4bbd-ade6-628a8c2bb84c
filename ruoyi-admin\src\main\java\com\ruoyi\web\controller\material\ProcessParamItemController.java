package com.ruoyi.web.controller.material;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ProcessParamItem;
import com.ruoyi.system.service.IProcessParamItemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 工艺参数明细Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/material/processParamItem")
public class ProcessParamItemController extends BaseController
{
    @Autowired
    private IProcessParamItemService processParamItemService;

    /**
     * 查询工艺参数明细列表
     */
    @PreAuthorize("@ss.hasPermi('material:processParamItem:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessParamItem processParamItem)
    {
        startPage();
        List<ProcessParamItem> list = processParamItemService.selectProcessParamItemList(processParamItem);
        return getDataTable(list);
    }

    /**
     * 导出工艺参数明细列表
     */
    @PreAuthorize("@ss.hasPermi('material:processParamItem:export')")
    @Log(title = "工艺参数明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcessParamItem processParamItem)
    {
        List<ProcessParamItem> list = processParamItemService.selectProcessParamItemList(processParamItem);
        ExcelUtil<ProcessParamItem> util = new ExcelUtil<ProcessParamItem>(ProcessParamItem.class);
        util.exportExcel(response, list, "工艺参数明细数据");
    }

    /**
     * 获取工艺参数明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:processParamItem:query')")
    @GetMapping(value = "/{itemId}")
    public AjaxResult getInfo(@PathVariable("itemId") Long itemId)
    {
        return AjaxResult.success(processParamItemService.selectProcessParamItemByItemId(itemId));
    }

    /**
     * 新增工艺参数明细
     */
    @PreAuthorize("@ss.hasPermi('material:processParamItem:add')")
    @Log(title = "工艺参数明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessParamItem processParamItem)
    {
        return toAjax(processParamItemService.insertProcessParamItem(processParamItem));
    }

    /**
     * 修改工艺参数明细
     */
    @PreAuthorize("@ss.hasPermi('material:processParamItem:edit')")
    @Log(title = "工艺参数明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessParamItem processParamItem)
    {
        return toAjax(processParamItemService.updateProcessParamItem(processParamItem));
    }

    /**
    /**
     * 删除工艺参数明细
     */
    @PreAuthorize("@ss.hasPermi('material:processParamItem:remove')")
    @Log(title = "工艺参数明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{itemIds}")
    public AjaxResult remove(@PathVariable Long[] itemIds)
    {
        return toAjax(processParamItemService.deleteProcessParamItemByItemIds(itemIds));
    }

    /**
     * 获取参数明细选项数据
     */
    @GetMapping("/options")
    public AjaxResult options(String type)
    {
        List<String> options = processParamItemService.selectProcessParamItemOptions(type);
        return AjaxResult.success(options);
    }
}