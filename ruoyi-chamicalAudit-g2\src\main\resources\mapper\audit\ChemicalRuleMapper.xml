<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.ChemicalRuleMapper">
    
    <resultMap type="ChemicalRule" id="ChemicalRuleResult">
        <result property="ruleId"    column="rule_id"    />
        <result property="productName"    column="product_name"    />
        <result property="processName"    column="process_name"    />
        <result property="testName"    column="test_name"    />
        <result property="isRefresh"    column="is_refresh"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="ruleDesc"    column="rule_desc"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectChemicalRuleVo">
        select rule_id, product_name, process_name, test_name, is_refresh, rule_type, rule_desc, 
               create_by, create_time, update_by, update_time 
        from chemical_rule
    </sql>

    <select id="selectChemicalRuleList" parameterType="ChemicalRule" resultMap="ChemicalRuleResult">
        <include refid="selectChemicalRuleVo"/>
        <where>  
            <if test="productName != null and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="processName != null and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="testName != null and testName != ''"> and test_name like concat('%', #{testName}, '%')</if>
            <if test="isRefresh != null"> and is_refresh = #{isRefresh}</if>
            <if test="ruleType != null and ruleType != ''"> and rule_type = #{ruleType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectChemicalRuleByRuleId" parameterType="Long" resultMap="ChemicalRuleResult">
        <include refid="selectChemicalRuleVo"/>
        where rule_id = #{ruleId}
    </select>

    <select id="selectChemicalRuleByCondition" resultMap="ChemicalRuleResult">
        <include refid="selectChemicalRuleVo"/>
        where product_name = #{productName} and process_name = #{processName} and test_name = #{testName}
    </select>
        
    <insert id="insertChemicalRule" parameterType="ChemicalRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into chemical_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="processName != null and processName != ''">process_name,</if>
            <if test="testName != null and testName != ''">test_name,</if>
            <if test="isRefresh != null">is_refresh,</if>
            <if test="ruleType != null and ruleType != ''">rule_type,</if>
            <if test="ruleDesc != null">rule_desc,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="processName != null and processName != ''">#{processName},</if>
            <if test="testName != null and testName != ''">#{testName},</if>
            <if test="isRefresh != null">#{isRefresh},</if>
            <if test="ruleType != null and ruleType != ''">#{ruleType},</if>
            <if test="ruleDesc != null">#{ruleDesc},</if>
            <if test="createBy != null">#{createBy},</if>
            getdate()
        </trim>
    </insert>

    <update id="updateChemicalRule" parameterType="ChemicalRule">
        update chemical_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="processName != null and processName != ''">process_name = #{processName},</if>
            <if test="testName != null and testName != ''">test_name = #{testName},</if>
            <if test="isRefresh != null">is_refresh = #{isRefresh},</if>
            <if test="ruleType != null and ruleType != ''">rule_type = #{ruleType},</if>
            <if test="ruleDesc != null">rule_desc = #{ruleDesc},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = getdate()
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteChemicalRuleByRuleId" parameterType="Long">
        delete from chemical_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteChemicalRuleByRuleIds" parameterType="String">
        delete from chemical_rule where rule_id in 
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

</mapper>
