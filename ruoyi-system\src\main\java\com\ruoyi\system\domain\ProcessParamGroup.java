package com.ruoyi.system.domain;

import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 工艺参数组对象 process_param_group
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ProcessParamGroup extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 参数组ID */
    private Long groupId;

    /** 所属材料ID */
    @Excel(name = "所属材料ID")
    private Long materialId;

    /** 工艺类型 */
    @Excel(name = "工艺类型")
    private String processType;

    /** 参数编号 */
    @Excel(name = "参数编号")
    private String paramNumber;

    /** 附件URL */
    private String attachments;

    /** 材料名称 */
    @Excel(name = "材料名称")
    private String materialName;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String supplierName;

    /** 工艺参数明细信息 */
    private List<ProcessParamItem> processParamItemList;

    public void setGroupId(Long groupId)
    {
        this.groupId = groupId;
    }

    public Long getGroupId()
    {
        return groupId;
    }
    public void setMaterialId(Long materialId)
    {
        this.materialId = materialId;
    }

    public Long getMaterialId()
    {
        return materialId;
    }
    public void setProcessType(String processType)
    {
        this.processType = processType;
    }

    public String getProcessType()
    {
        return processType;
    }
    public void setParamNumber(String paramNumber)
    {
        this.paramNumber = paramNumber;
    }

    public String getParamNumber()
    {
        return paramNumber;
    }
    public void setAttachments(String attachments)
    {
        this.attachments = attachments;
    }

    public String getAttachments()
    {
        return attachments;
    }

    public String getMaterialName()
    {
        return materialName;
    }

    public void setMaterialName(String materialName)
    {
        this.materialName = materialName;
    }

    public String getSupplierName()
    {
        return supplierName;
    }

    public void setSupplierName(String supplierName)
    {
        this.supplierName = supplierName;
    }

    public List<ProcessParamItem> getProcessParamItemList()
    {
        return processParamItemList;
    }

    public void setProcessParamItemList(List<ProcessParamItem> processParamItemList)
    {
        this.processParamItemList = processParamItemList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("groupId", getGroupId())
                .append("materialId", getMaterialId())
                .append("processType", getProcessType())
                .append("paramNumber", getParamNumber())
                .append("attachments", getAttachments())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("processParamItemList", getProcessParamItemList())
                .toString();
    }
}