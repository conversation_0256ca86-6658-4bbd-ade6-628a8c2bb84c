package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TestPlanMapper;
import com.ruoyi.system.domain.TestPlan;
import com.ruoyi.system.service.ITestPlanService;

/**
 * 测试方案Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class TestPlanServiceImpl implements ITestPlanService 
{
    @Autowired
    private TestPlanMapper testPlanMapper;

    /**
     * 查询测试方案
     * 
     * @param testPlanId 测试方案主键
     * @return 测试方案
     */
    @Override
    public TestPlan selectTestPlanByTestPlanId(Long testPlanId)
    {
        return testPlanMapper.selectTestPlanByTestPlanId(testPlanId);
    }

    /**
     * 查询测试方案列表
     * 
     * @param testPlan 测试方案
     * @return 测试方案
     */
    @Override
    public List<TestPlan> selectTestPlanList(TestPlan testPlan)
    {
        return testPlanMapper.selectTestPlanList(testPlan);
    }

    /**
     * 新增测试方案
     * 
     * @param testPlan 测试方案
     * @return 结果
     */
    @Override
    public int insertTestPlan(TestPlan testPlan)
    {
        testPlan.setCreateTime(DateUtils.getNowDate());
        return testPlanMapper.insertTestPlan(testPlan);
    }

    /**
     * 修改测试方案
     * 
     * @param testPlan 测试方案
     * @return 结果
     */
    @Override
    public int updateTestPlan(TestPlan testPlan)
    {
        testPlan.setUpdateTime(DateUtils.getNowDate());
        return testPlanMapper.updateTestPlan(testPlan);
    }

    /**
     * 批量删除测试方案
     * 
     * @param testPlanIds 需要删除的测试方案主键
     * @return 结果
     */
    @Override
    public int deleteTestPlanByTestPlanIds(Long[] testPlanIds)
    {
        return testPlanMapper.deleteTestPlanByTestPlanIds(testPlanIds);
    }

    /**
     * 删除测试方案信息
     * 
     * @param testPlanId 测试方案主键
     * @return 结果
     */
    @Override
    public int deleteTestPlanByTestPlanId(Long testPlanId)
    {
        return testPlanMapper.deleteTestPlanByTestPlanId(testPlanId);
    }

    /**
     * 获取测试方案选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    @Override
    public List<String> selectTestPlanOptions(String type)
    {
        if ("planCode".equals(type)) {
            return testPlanMapper.selectPlanCodeOptions();
        } else if ("performanceType".equals(type)) {
            return testPlanMapper.selectPerformanceTypeOptions();
        } else if ("testEquipment".equals(type)) {
            return testPlanMapper.selectTestEquipmentOptions();
        }
        return new ArrayList<>();
    }
}
