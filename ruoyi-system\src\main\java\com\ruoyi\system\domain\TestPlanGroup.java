package com.ruoyi.system.domain;

import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 测试方案组对象 test_plan_group
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class TestPlanGroup extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测试方案组ID */
    private Long planGroupId;

    /** 方案编号 */
    @Excel(name = "方案编号")
    private String planCode;

    /** 性能类型 */
    @Excel(name = "性能类型")
    private String performanceType;

    /** 方案/性能名称 */
    @Excel(name = "方案/性能名称")
    private String performanceName;

    /** 测试设备 */
    @Excel(name = "测试设备")
    private String testEquipment;

    /** 附件URL */
    private String attachments;

    /** 测试参数明细信息 */
    private List<TestParamItem> testParamItemList;

    public void setPlanGroupId(Long planGroupId) 
    {
        this.planGroupId = planGroupId;
    }

    public Long getPlanGroupId() 
    {
        return planGroupId;
    }
    public void setPlanCode(String planCode) 
    {
        this.planCode = planCode;
    }

    public String getPlanCode() 
    {
        return planCode;
    }
    public void setPerformanceType(String performanceType) 
    {
        this.performanceType = performanceType;
    }

    public String getPerformanceType() 
    {
        return performanceType;
    }
    public void setPerformanceName(String performanceName) 
    {
        this.performanceName = performanceName;
    }

    public String getPerformanceName() 
    {
        return performanceName;
    }
    public void setTestEquipment(String testEquipment) 
    {
        this.testEquipment = testEquipment;
    }

    public String getTestEquipment() 
    {
        return testEquipment;
    }
    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }

    public List<TestParamItem> getTestParamItemList()
    {
        return testParamItemList;
    }

    public void setTestParamItemList(List<TestParamItem> testParamItemList)
    {
        this.testParamItemList = testParamItemList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("planGroupId", getPlanGroupId())
            .append("planCode", getPlanCode())
            .append("performanceType", getPerformanceType())
            .append("performanceName", getPerformanceName())
            .append("testEquipment", getTestEquipment())
            .append("attachments", getAttachments())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("testParamItemList", getTestParamItemList())
            .toString();
    }
}
