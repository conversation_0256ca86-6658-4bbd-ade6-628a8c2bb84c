package com.ruoyi.audit.mapper;

import java.util.List;
import com.ruoyi.audit.domain.ChemicalRefreshTask;

/**
 * 化学数据刷新任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ChemicalRefreshTaskMapper 
{
    /**
     * 查询化学数据刷新任务
     * 
     * @param refreshId 化学数据刷新任务主键
     * @return 化学数据刷新任务
     */
    public ChemicalRefreshTask selectChemicalRefreshTaskByRefreshId(Long refreshId);

    /**
     * 查询化学数据刷新任务列表
     * 
     * @param chemicalRefreshTask 化学数据刷新任务
     * @return 化学数据刷新任务集合
     */
    public List<ChemicalRefreshTask> selectChemicalRefreshTaskList(ChemicalRefreshTask chemicalRefreshTask);

    /**
     * 新增化学数据刷新任务
     * 
     * @param chemicalRefreshTask 化学数据刷新任务
     * @return 结果
     */
    public int insertChemicalRefreshTask(ChemicalRefreshTask chemicalRefreshTask);

    /**
     * 修改化学数据刷新任务
     * 
     * @param chemicalRefreshTask 化学数据刷新任务
     * @return 结果
     */
    public int updateChemicalRefreshTask(ChemicalRefreshTask chemicalRefreshTask);

    /**
     * 删除化学数据刷新任务
     * 
     * @param refreshId 化学数据刷新任务主键
     * @return 结果
     */
    public int deleteChemicalRefreshTaskByRefreshId(Long refreshId);

    /**
     * 批量删除化学数据刷新任务
     * 
     * @param refreshIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChemicalRefreshTaskByRefreshIds(Long[] refreshIds);

    /**
     * 查询最近的刷新任务
     * 
     * @param limit 查询数量限制
     * @return 刷新任务列表
     */
    public List<ChemicalRefreshTask> selectRecentRefreshTasks(int limit);

    /**
     * 查询正在运行的刷新任务
     * 
     * @return 正在运行的刷新任务列表
     */
    public List<ChemicalRefreshTask> selectRunningRefreshTasks();

    /**
     * 统计刷新任务数量
     * 
     * @param taskStatus 任务状态
     * @return 任务数量
     */
    public int countRefreshTasksByStatus(String taskStatus);

    /**
     * 清理历史刷新任务记录
     * 
     * @param days 保留天数
     * @return 清理的记录数
     */
    public int cleanHistoryRefreshTasks(int days);
}
