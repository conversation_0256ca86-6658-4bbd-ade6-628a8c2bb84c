-- 化学审计系统完整数据库表结构
-- 包含所有功能模块对应的表

-- 1. 化学数据主表
CREATE TABLE chemical (
    id VARCHAR(50) NOT NULL PRIMARY KEY,
    organization_id BIGINT,
    attribute_id BIGINT,
    examine_date DATETIME,
    shift VARCHAR(50),
    staff VARCHAR(100),
    department_code VARCHAR(50),
    process_set_name VARCHA<PERSON>(100),
    process_name VARCHAR(100),
    product_set_name VARCHAR(100),
    product_name VARCHAR(100),
    test_set_name VARCHAR(100),
    test_name VARCHAR(100),
    sample_size INT,
    layer_number VARCHAR(50),
    upper_limit VARCHAR(50),
    median_specification VARCHAR(50),
    down_limit VARCHAR(50),
    examine1 VARCHAR(50),
    examine1_ys VARCHAR(50),
    examine1_zs VARCHAR(50),
    examine2 VARCHAR(50),
    frequency VARCHAR(50),
    frequency_unit VARCHAR(50),
    slot_body_name VARCHA<PERSON>(100),
    project_team_code VARCHAR(50),
    project_team_name VARCHAR(100),
    test_code VARCHAR(50),
    adjustment_upper_limit VARCHAR(50),
    adjustment_mid VARCHAR(50),
    adjustment_lower_limit VARCHAR(50),
    project_unit VARCHAR(50),
    insertion_time DATETIME,
    is_exported BIT DEFAULT 0,
    not_process BIT DEFAULT 0,
    warning_upper_limit VARCHAR(50),
    warning_mid VARCHAR(50),
    warning_lower_limit VARCHAR(50),
    remark TEXT,
    status VARCHAR(10) DEFAULT '0',
    created_by VARCHAR(64),
    last_updated_by VARCHAR(64),
    creation_date DATETIME DEFAULT GETDATE(),
    last_update_date DATETIME DEFAULT GETDATE(),
    create_time DATETIME DEFAULT GETDATE(),
    update_time DATETIME DEFAULT GETDATE()
);

-- 2. 化学应审数据表
CREATE TABLE chemical_ys (
    id VARCHAR(50) NOT NULL PRIMARY KEY,
    organization_id BIGINT,
    attribute_id BIGINT,
    examine_date DATETIME,
    shift VARCHAR(50),
    staff VARCHAR(100),
    process_name VARCHAR(100),
    product_name VARCHAR(100),
    test_name VARCHAR(100),
    layer_number VARCHAR(50),
    upper_limit VARCHAR(50),
    median_specification VARCHAR(50),
    down_limit VARCHAR(50),
    examine1 VARCHAR(50),
    examine1_ys VARCHAR(50),
    examine1_zs VARCHAR(50),
    examine2 VARCHAR(50),
    is_modified BIT DEFAULT 0,
    original_examine1 VARCHAR(50),
    remark TEXT,
    create_time DATETIME DEFAULT GETDATE(),
    update_time DATETIME DEFAULT GETDATE()
);

-- 3. 数据刷新规则配置表
CREATE TABLE rule_config (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    process_name VARCHAR(100) NOT NULL,
    test_name VARCHAR(100) NOT NULL,
    enable_control_limit_adjustment BIT DEFAULT 1,
    enable_moving_range_adjustment BIT DEFAULT 1,
    enable_nine_point_same_side_check BIT DEFAULT 1,
    enable_six_point_trend_check BIT DEFAULT 1,
    enable_cpk_adjustment BIT DEFAULT 1,
    cpk_target DECIMAL(10,3) DEFAULT 1.33,
    is_refresh_mode BIT DEFAULT 0,
    rule_description TEXT,
    status VARCHAR(1) DEFAULT '1',
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    created_time DATETIME DEFAULT GETDATE(),
    updated_time DATETIME DEFAULT GETDATE(),
    UNIQUE(product_name, process_name, test_name)
);

-- 4. 化学任务监控表
CREATE TABLE chemical_task_monitor (
    monitor_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    task_id BIGINT,
    task_name VARCHAR(100),
    task_type VARCHAR(50),
    task_status VARCHAR(20),
    start_time DATETIME,
    end_time DATETIME,
    total_records INT DEFAULT 0,
    processed_records INT DEFAULT 0,
    success_records INT DEFAULT 0,
    error_records INT DEFAULT 0,
    modified_records INT DEFAULT 0,
    progress_percent DECIMAL(5,2) DEFAULT 0,
    execution_time DATETIME,
    execution_duration BIGINT,
    execution_message TEXT,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    disk_usage DECIMAL(5,2),
    network_in DECIMAL(10,2),
    network_out DECIMAL(10,2),
    error_message TEXT,
    task_config TEXT,
    create_by VARCHAR(64),
    create_time DATETIME DEFAULT GETDATE(),
    update_by VARCHAR(64),
    update_time DATETIME DEFAULT GETDATE()
);

-- 5. 化学数据刷新任务表
CREATE TABLE chemical_refresh_task (
    task_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL,
    task_type VARCHAR(50),
    start_date DATETIME,
    end_date DATETIME,
    layer_numbers TEXT,
    task_status VARCHAR(20) DEFAULT 'PENDING',
    total_records INT DEFAULT 0,
    processed_records INT DEFAULT 0,
    modified_records INT DEFAULT 0,
    error_records INT DEFAULT 0,
    start_time DATETIME,
    end_time DATETIME,
    duration_ms BIGINT,
    error_message TEXT,
    created_by VARCHAR(64),
    create_time DATETIME DEFAULT GETDATE(),
    update_time DATETIME DEFAULT GETDATE()
);

-- 6. 化学数据导出记录表
CREATE TABLE chemical_export_record (
    export_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    export_name VARCHAR(200),
    export_type VARCHAR(20),
    start_date DATETIME,
    end_date DATETIME,
    layer_numbers TEXT,
    file_path VARCHAR(500),
    file_name VARCHAR(200),
    file_size BIGINT,
    record_count INT,
    export_status VARCHAR(20) DEFAULT 'PENDING',
    export_progress INT DEFAULT 0,
    error_message TEXT,
    created_by VARCHAR(64),
    create_time DATETIME DEFAULT GETDATE(),
    update_time DATETIME DEFAULT GETDATE()
);

-- 7. 系统配置表
CREATE TABLE chemical_system_config (
    config_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_description VARCHAR(500),
    config_type VARCHAR(50) DEFAULT 'STRING',
    is_system BIT DEFAULT 0,
    status VARCHAR(1) DEFAULT '1',
    created_by VARCHAR(64),
    create_time DATETIME DEFAULT GETDATE(),
    updated_by VARCHAR(64),
    update_time DATETIME DEFAULT GETDATE()
);

-- 8. 数据处理日志表
CREATE TABLE chemical_process_log (
    log_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    task_id BIGINT,
    log_level VARCHAR(10),
    log_message TEXT,
    log_detail TEXT,
    chemical_id VARCHAR(50),
    process_type VARCHAR(50),
    create_time DATETIME DEFAULT GETDATE()
);

-- 创建索引
CREATE INDEX idx_chemical_examine_date ON chemical(examine_date);
CREATE INDEX idx_chemical_product_process_test ON chemical(product_name, process_name, test_name);
CREATE INDEX idx_chemical_layer_number ON chemical(layer_number);
CREATE INDEX idx_chemical_ys_examine_date ON chemical_ys(examine_date);
CREATE INDEX idx_chemical_ys_product_process_test ON chemical_ys(product_name, process_name, test_name);
CREATE INDEX idx_rule_config_product_process_test ON rule_config(product_name, process_name, test_name);
CREATE INDEX idx_task_monitor_task_name ON chemical_task_monitor(task_name);
CREATE INDEX idx_task_monitor_create_time ON chemical_task_monitor(create_time);
CREATE INDEX idx_refresh_task_status ON chemical_refresh_task(task_status);
CREATE INDEX idx_export_record_create_time ON chemical_export_record(create_time);
CREATE INDEX idx_process_log_task_id ON chemical_process_log(task_id);
CREATE INDEX idx_process_log_create_time ON chemical_process_log(create_time);

-- 插入默认系统配置
INSERT INTO chemical_system_config (config_key, config_value, config_description, config_type) VALUES
('chemical.database.cloud.url', 'jdbc:sqlserver://************;DatabaseName=SPC-G2;encrypt=true;trustServerCertificate=true;sslProtocol=TLSv1', '云端数据库连接URL', 'STRING'),
('chemical.database.cloud.username', 'hhh', '云端数据库用户名', 'STRING'),
('chemical.database.cloud.password', 'root1234', '云端数据库密码', 'PASSWORD'),
('chemical.export.default.path', 'D:/chemical_exports', '默认导出路径', 'STRING'),
('chemical.export.max.records', '100000', '最大导出记录数', 'INTEGER'),
('chemical.task.max.concurrent', '5', '最大并发任务数', 'INTEGER'),
('chemical.rule.default.cpk.target', '1.33', '默认CPK目标值', 'DECIMAL'),
('chemical.pulsar.service.url', 'pulsar://localhost:6650', 'Pulsar服务地址', 'STRING'),
('chemical.pulsar.topic.name', 'chemical-data-topic', 'Pulsar主题名称', 'STRING');

-- 插入默认规则配置
INSERT INTO rule_config (product_name, process_name, test_name, rule_description, created_by) VALUES
('DEFAULT', 'DEFAULT', 'DEFAULT', '系统默认规则配置，适用于所有未单独配置的产品-过程-测试组合', 'system');
