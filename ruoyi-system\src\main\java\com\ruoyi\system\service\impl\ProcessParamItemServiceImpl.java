package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ProcessParamItemMapper;
import com.ruoyi.system.domain.ProcessParamItem;
import com.ruoyi.system.service.IProcessParamItemService;

/**
 * 工艺参数明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ProcessParamItemServiceImpl implements IProcessParamItemService 
{
    @Autowired
    private ProcessParamItemMapper processParamItemMapper;

    /**
     * 查询工艺参数明细
     * 
     * @param itemId 工艺参数明细主键
     * @return 工艺参数明细
     */
    @Override
    public ProcessParamItem selectProcessParamItemByItemId(Long itemId)
    {
        return processParamItemMapper.selectProcessParamItemByItemId(itemId);
    }

    /**
     * 查询工艺参数明细列表
     * 
     * @param processParamItem 工艺参数明细
     * @return 工艺参数明细
     */
    @Override
    public List<ProcessParamItem> selectProcessParamItemList(ProcessParamItem processParamItem)
    {
        return processParamItemMapper.selectProcessParamItemList(processParamItem);
    }

    /**
     * 新增工艺参数明细
     * 
     * @param processParamItem 工艺参数明细
     * @return 结果
     */
    @Override
    public int insertProcessParamItem(ProcessParamItem processParamItem)
    {
        processParamItem.setCreateTime(DateUtils.getNowDate());
        return processParamItemMapper.insertProcessParamItem(processParamItem);
    }

    /**
     * 修改工艺参数明细
     * 
     * @param processParamItem 工艺参数明细
     * @return 结果
     */
    @Override
    public int updateProcessParamItem(ProcessParamItem processParamItem)
    {
        processParamItem.setUpdateTime(DateUtils.getNowDate());
        return processParamItemMapper.updateProcessParamItem(processParamItem);
    }

    /**
    /**
     * 批量删除工艺参数明细
     *
     * @param itemIds 需要删除的工艺参数明细主键
     * @return 结果
     */
    @Override
    public int deleteProcessParamItemByItemIds(Long[] itemIds)
    {
        return processParamItemMapper.deleteProcessParamItemByItemIds(itemIds);
    }

    /**
     * 获取工艺参数明细选项数据
     *
     * @param type 选项类型
     * @return 选项列表
     */
    @Override
    public List<String> selectProcessParamItemOptions(String type)
    {
        if ("paramName".equals(type)) {
            return processParamItemMapper.selectParamNameOptions();
        } else if ("unit".equals(type)) {
            return processParamItemMapper.selectUnitOptions();
        }
        return new ArrayList<>();
    }

    /**
     * 删除工艺参数明细信息
     * 
     * @param itemId 工艺参数明细主键
     * @return 结果
     */
    @Override
    public int deleteProcessParamItemByItemId(Long itemId)
    {
        return processParamItemMapper.deleteProcessParamItemByItemId(itemId);
    }
}