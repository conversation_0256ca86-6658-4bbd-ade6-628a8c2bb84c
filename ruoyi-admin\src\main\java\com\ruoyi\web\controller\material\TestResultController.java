package com.ruoyi.web.controller.material;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.TestResult;
import com.ruoyi.system.service.ITestResultService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 测试结果Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/material/testResult")
public class TestResultController extends BaseController
{
    @Autowired
    private ITestResultService testResultService;

    /**
     * 查询测试结果列表
     */
    @PreAuthorize("@ss.hasPermi('material:testResult:list')")
    @GetMapping("/list")
    public TableDataInfo list(TestResult testResult)
    {
        startPage();
        List<TestResult> list = testResultService.selectTestResultList(testResult);
        return getDataTable(list);
    }

    /**
     * 查询测试结果趋势对比数据
     */
    @GetMapping("/trendList")
    public AjaxResult trendList(TestResult testResult)
    {
        List<TestResult> list = testResultService.selectTestResultTrendList(testResult);
        return AjaxResult.success(list);
    }

    /**
     * 导出测试结果列表
     */
    @PreAuthorize("@ss.hasPermi('material:testResult:export')")
    @Log(title = "测试结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestResult testResult)
    {
        List<TestResult> list = testResultService.selectTestResultList(testResult);
        ExcelUtil<TestResult> util = new ExcelUtil<TestResult>(TestResult.class);
        util.exportExcel(response, list, "测试结果数据");
    }

    /**
    /**
     * 获取测试结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:testResult:query')")
    @GetMapping(value = "/{testResultId}")
    public AjaxResult getInfo(@PathVariable("testResultId") Long testResultId)
    {
        return AjaxResult.success(testResultService.selectTestResultByTestResultId(testResultId));
    }

    /**
     * 获取测试结果选项数据
     */
    @GetMapping("/options")
    public AjaxResult getOptions(@RequestParam(required = false) String type)
    {
        List<String> options = new ArrayList<>();

        if ("planCode".equals(type)) {
            // 获取方案编号选项
            options = testResultService.selectPlanCodeOptions();
        } else if ("paramNumber".equals(type)) {
            // 获取参数编号选项
            options = testResultService.selectParamNumberOptions();
        } else if ("materialName".equals(type)) {
            // 获取材料名称选项
            options = testResultService.selectMaterialNameOptions();
        } else if ("supplierName".equals(type)) {
            // 获取供应商名称选项
            options = testResultService.selectSupplierNameOptions();
        } else if ("materialModel".equals(type)) {
            // 获取材料型号选项
            options = testResultService.selectMaterialModelOptions();
        } else if ("processType".equals(type)) {
            // 获取工艺类型选项
            options = testResultService.selectProcessTypeOptions();
        } else if ("performanceType".equals(type)) {
            // 获取性能类型选项
            options = testResultService.selectPerformanceTypeOptions();
        } else if ("testEquipment".equals(type)) {
            // 获取测试设备选项
            options = testResultService.selectTestEquipmentOptions();
        } else if ("testPlanGroup".equals(type)) {
            // 获取测试方案组选项
            options = testResultService.selectTestPlanGroupOptions();
        } else if ("testParamItem".equals(type)) {
            // 获取测试参数明细选项
            options = testResultService.selectTestParamItemOptions();
        }

        return AjaxResult.success(options);
    }

    /**
     * 根据测试方案组ID获取测试参数明细选项
     */
    @GetMapping("/testParamOptions/{planGroupId}")
    public AjaxResult getTestParamOptionsByPlanGroupId(@PathVariable Long planGroupId)
    {
        List<String> options = testResultService.selectTestParamItemOptionsByPlanGroupId(planGroupId);
        return AjaxResult.success(options);
    }

    /**
     * 获取参数详情信息
     */
    @GetMapping("/paramDetail/{paramNumber}")
    public AjaxResult getParamDetail(@PathVariable String paramNumber)
    {
        try {
            // 实现参数详情查询
            Map<String, Object> result = testResultService.getParamDetail(paramNumber);

            if (result != null && !result.isEmpty()) {
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("未找到参数详情: " + paramNumber);
            }
        } catch (Exception e) {
            logger.error("获取参数详情失败: " + paramNumber, e);
            return AjaxResult.error("获取参数详情失败: " + e.getMessage());
        }
    }

    /**
     * 新增测试结果
     */
    @PreAuthorize("@ss.hasPermi('material:testResult:add')")
    @Log(title = "测试结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestResult testResult)
    {
        return toAjax(testResultService.insertTestResult(testResult));
    }

    /**
     * 修改测试结果
     */
    @PreAuthorize("@ss.hasPermi('material:testResult:edit')")
    @Log(title = "测试结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestResult testResult)
    {
        return toAjax(testResultService.updateTestResult(testResult));
    }

    /**
     * 删除测试结果
     */
    @PreAuthorize("@ss.hasPermi('material:testResult:remove')")
    @Log(title = "测试结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{testResultIds}")
    public AjaxResult remove(@PathVariable Long[] testResultIds)
    {
        return toAjax(testResultService.deleteTestResultByTestResultIds(testResultIds));
    }
}