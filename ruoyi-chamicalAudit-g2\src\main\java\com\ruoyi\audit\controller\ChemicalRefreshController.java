package com.ruoyi.audit.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.format.annotation.DateTimeFormat;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.audit.domain.ChemicalRefreshTask;
import com.ruoyi.audit.service.IChemicalRefreshService;

/**
 * 化学数据刷新控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/chemical/refresh")
public class ChemicalRefreshController extends BaseController
{
    @Autowired
    private IChemicalRefreshService chemicalRefreshService;

    /**
     * 查询化学数据刷新任务列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:list')")
    @GetMapping("/list")
    public TableDataInfo list(ChemicalRefreshTask refreshTask)
    {
        startPage();
        List<ChemicalRefreshTask> list = chemicalRefreshService.selectRefreshTaskList(refreshTask);
        return getDataTable(list);
    }

    /**
     * 导出化学数据刷新任务列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:export')")
    @Log(title = "化学数据刷新任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChemicalRefreshTask refreshTask)
    {
        List<ChemicalRefreshTask> list = chemicalRefreshService.selectRefreshTaskList(refreshTask);
        ExcelUtil<ChemicalRefreshTask> util = new ExcelUtil<ChemicalRefreshTask>(ChemicalRefreshTask.class);
        util.exportExcel(response, list, "化学数据刷新任务数据");
    }

    /**
     * 获取化学数据刷新任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:query')")
    @GetMapping(value = "/{refreshId}")
    public AjaxResult getInfo(@PathVariable("refreshId") Long refreshId)
    {
        return success(chemicalRefreshService.selectRefreshTaskById(refreshId));
    }

    /**
     * 新增化学数据刷新任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:add')")
    @Log(title = "化学数据刷新任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ChemicalRefreshTask refreshTask)
    {
        return toAjax(chemicalRefreshService.insertRefreshTask(refreshTask));
    }

    /**
     * 修改化学数据刷新任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:edit')")
    @Log(title = "化学数据刷新任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ChemicalRefreshTask refreshTask)
    {
        return toAjax(chemicalRefreshService.updateRefreshTask(refreshTask));
    }

    /**
     * 删除化学数据刷新任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:remove')")
    @Log(title = "化学数据刷新任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{refreshIds}")
    public AjaxResult remove(@PathVariable Long[] refreshIds)
    {
        return toAjax(chemicalRefreshService.deleteRefreshTaskByIds(refreshIds));
    }

    /**
     * 执行数据刷新任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:execute')")
    @Log(title = "执行数据刷新", businessType = BusinessType.OTHER)
    @PostMapping("/execute")
    public AjaxResult executeRefresh(
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @RequestParam("layerNumbers") String[] layerNumbers,
            @RequestParam(value = "exportPath", required = false) String exportPath,
            @RequestParam(value = "backupPath", required = false) String backupPath)
    {
        try {
            Map<String, Object> result = chemicalRefreshService.executeRefreshTask(
                startDate, endDate, layerNumbers, exportPath, backupPath);
            
            if ((Boolean) result.get("success")) {
                return AjaxResult.success("数据刷新任务启动成功", result);
            } else {
                return AjaxResult.error((String) result.get("message"));
            }
        } catch (Exception e) {
            logger.error("执行数据刷新失败", e);
            return AjaxResult.error("执行数据刷新失败: " + e.getMessage());
        }
    }

    /**
     * 停止正在运行的刷新任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:stop')")
    @Log(title = "停止数据刷新", businessType = BusinessType.OTHER)
    @PostMapping("/stop")
    public AjaxResult stopRefresh()
    {
        try {
            Map<String, Object> result = chemicalRefreshService.stopRunningRefreshTask();
            
            if ((Boolean) result.get("success")) {
                return AjaxResult.success((String) result.get("message"));
            } else {
                return AjaxResult.error((String) result.get("message"));
            }
        } catch (Exception e) {
            logger.error("停止数据刷新失败", e);
            return AjaxResult.error("停止数据刷新失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否有正在运行的刷新任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:query')")
    @GetMapping("/status")
    public AjaxResult getRefreshStatus()
    {
        try {
            boolean hasRunning = chemicalRefreshService.hasRunningRefreshTask();
            Map<String, Object> statistics = chemicalRefreshService.getRefreshTaskStatistics();
            
            statistics.put("hasRunningTask", hasRunning);
            
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            logger.error("获取刷新状态失败", e);
            return AjaxResult.error("获取刷新状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的刷新任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:query')")
    @GetMapping("/recent")
    public AjaxResult getRecentTasks(@RequestParam(value = "limit", defaultValue = "10") int limit)
    {
        try {
            List<ChemicalRefreshTask> recentTasks = chemicalRefreshService.getRecentRefreshTasks(limit);
            return AjaxResult.success(recentTasks);
        } catch (Exception e) {
            logger.error("获取最近刷新任务失败", e);
            return AjaxResult.error("获取最近刷新任务失败: " + e.getMessage());
        }
    }

    /**
     * 导出刷新数据到CSV
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:export')")
    @Log(title = "导出刷新数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCsv")
    public AjaxResult exportCsv(
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @RequestParam("layerNumbers") String[] layerNumbers,
            @RequestParam(value = "exportPath", required = false) String exportPath)
    {
        try {
            Map<String, Object> result = chemicalRefreshService.exportRefreshDataToCsv(
                startDate, endDate, layerNumbers, exportPath);
            
            if ((Boolean) result.get("success")) {
                return AjaxResult.success("数据导出成功", result);
            } else {
                return AjaxResult.error((String) result.get("message"));
            }
        } catch (Exception e) {
            logger.error("导出CSV数据失败", e);
            return AjaxResult.error("导出CSV数据失败: " + e.getMessage());
        }
    }

    /**
     * 清理历史刷新任务记录
     */
    @PreAuthorize("@ss.hasPermi('chemical:refresh:clean')")
    @Log(title = "清理历史记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean/{days}")
    public AjaxResult cleanHistory(@PathVariable("days") int days)
    {
        try {
            int cleanedCount = chemicalRefreshService.cleanHistoryRefreshTasks(days);
            return AjaxResult.success("成功清理 " + cleanedCount + " 条历史记录");
        } catch (Exception e) {
            logger.error("清理历史记录失败", e);
            return AjaxResult.error("清理历史记录失败: " + e.getMessage());
        }
    }
}
