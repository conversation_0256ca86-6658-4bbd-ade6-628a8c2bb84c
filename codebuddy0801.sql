/*
 Navicat Premium Dump SQL

 Source Server         : ry
 Source Server Type    : MySQL
 Source Server Version : 80401 (8.4.1)
 Source Host           : localhost:3306
 Source Schema         : codebuddy

 Target Server Type    : MySQL
 Target Server Version : 80401 (8.4.1)
 File Encoding         : 65001

 Date: 01/08/2025 17:42:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '代码生成业务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table
-- ----------------------------

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------

-- ----------------------------
-- Table structure for materials
-- ----------------------------
DROP TABLE IF EXISTS `materials`;
CREATE TABLE `materials`  (
  `material_id` int NOT NULL AUTO_INCREMENT COMMENT '材料ID（主键）',
  `material_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '材料名称',
  `supplier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商名称',
  `material_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '材料型号',
  `material_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '材料描述',
  `attachments` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`material_id`) USING BTREE,
  INDEX `idx_material_name`(`material_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '材料基本信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of materials
-- ----------------------------
INSERT INTO `materials` VALUES (1, '材料A', '供应商X', 'M-A1', '高性能塑料，用于注塑成型', NULL, '无', 'system', '2025-07-29 16:32:03', NULL, '2025-07-29 16:32:03');
INSERT INTO `materials` VALUES (2, '材料B', '供应商Y', 'M-B1', '耐高温合金，用于发动机部件', NULL, '无', 'system', '2025-07-29 16:32:03', NULL, '2025-07-29 16:32:03');
INSERT INTO `materials` VALUES (3, '材料C', '供应商Z', 'M-C1', '导电复合材料，用于电子封装', NULL, '无', 'system', '2025-07-29 16:32:03', NULL, '2025-07-29 16:32:03');
INSERT INTO `materials` VALUES (4, '材料D', '供应商X', 'M-D1', '高强度钢材，用于结构件', NULL, '无', 'system', '2025-07-29 16:32:03', NULL, '2025-07-29 16:32:03');
INSERT INTO `materials` VALUES (5, '材料E', '供应商Y', 'M-E1', '轻量化铝合金，用于航空零件', NULL, '无', 'system', '2025-07-29 16:32:03', NULL, '2025-07-29 16:32:03');
INSERT INTO `materials` VALUES (8, '油墨', '香港太阳油墨有限公司', 'PSR4000 AUS308', '感光型 阻焊 绿色 亮光 无卤油墨 PSR4000 AUS308 1KG/组（HF）', 'http://localhost:8080/profile/upload/2025/07/30/【78-03-040】AUS products Roadmap_20250730161441A003.pdf', NULL, '吴红姣', '2025-07-30 16:15:47', NULL, '2025-07-30 16:15:47');

-- ----------------------------
-- Table structure for process_param_group
-- ----------------------------
DROP TABLE IF EXISTS `process_param_group`;
CREATE TABLE `process_param_group`  (
  `group_id` int NOT NULL AUTO_INCREMENT COMMENT '参数组ID（主键）',
  `material_id` int NOT NULL COMMENT '所属材料ID',
  `process_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工艺类型',
  `param_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数编号',
  `attachments` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`group_id`) USING BTREE,
  UNIQUE INDEX `uk_mat_num`(`material_id` ASC, `param_number` ASC) USING BTREE,
  INDEX `idx_process_type`(`process_type` ASC) USING BTREE,
  CONSTRAINT `fk_group_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`material_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工艺参数组（工艺类型+参数编号）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of process_param_group
-- ----------------------------
INSERT INTO `process_param_group` VALUES (1, 1, '注塑', 'P001', NULL, '注塑工艺组1', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_group` VALUES (2, 1, '注塑', 'P002', NULL, '注塑工艺组2', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_group` VALUES (3, 2, '压铸', 'P003', NULL, '压铸工艺组1', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_group` VALUES (4, 2, '压铸', 'P007', NULL, '压铸工艺组2', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_group` VALUES (5, 3, '挤出', 'P004', NULL, '挤出工艺组1', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_group` VALUES (6, 3, '挤出', 'P008', NULL, '挤出工艺组2', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_group` VALUES (7, 4, '焊接', 'P005', NULL, '焊接工艺组1', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_group` VALUES (8, 5, '铣削', 'P006', NULL, '铣削工艺组1', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_group` VALUES (9, 1, '滚涂+压平', '1-1-1-1', 'http://localhost:8080/profile/upload/2025/07/30/AUS308油墨加工工艺_20250730161725A004.xlsx', NULL, '吴红姣', '2025-07-30 16:17:26', NULL, '2025-07-30 16:17:26');
INSERT INTO `process_param_group` VALUES (10, 8, '滚涂+压平', '1-1-1', NULL, NULL, '吴红姣', '2025-07-30 16:35:04', NULL, '2025-07-30 16:35:04');

-- ----------------------------
-- Table structure for process_param_item
-- ----------------------------
DROP TABLE IF EXISTS `process_param_item`;
CREATE TABLE `process_param_item`  (
  `item_id` int NOT NULL AUTO_INCREMENT COMMENT '参数明细ID（主键）',
  `group_id` int NOT NULL COMMENT '所属参数组ID',
  `param_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数名称',
  `param_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数数值（字符串格式）',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数单位',
  `attachments` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`item_id`) USING BTREE,
  INDEX `idx_group_id`(`group_id` ASC) USING BTREE,
  CONSTRAINT `fk_item_group` FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工艺参数明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of process_param_item
-- ----------------------------
INSERT INTO `process_param_item` VALUES (1, 1, '温度', '220.500000', '℃', NULL, '注塑温度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (2, 1, '压力', '80.000000', 'MPa', NULL, '注塑压力', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (3, 2, '温度', '230.000000', '℃', NULL, '注塑温度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (4, 2, '压力', '85.000000', 'MPa', NULL, '注塑压力', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (5, 3, '模具温度', '150.000000', '℃', NULL, '压铸模温', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (6, 3, '保压时间', '15.000000', 's', NULL, '保压时间', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (7, 7, '焊接电流', '120.000000', 'A', NULL, '焊接电流', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (8, 7, '焊接电压', '22.500000', 'V', NULL, '焊接电压', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (9, 4, '挤出速率', '5.000000', 'm/min', NULL, '挤出速度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (10, 4, '孔径', '2.500000', 'mm', NULL, '模具孔径', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (11, 8, '挤出速率', '5.500000', 'm/min', NULL, '挤出速度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (12, 8, '孔径', '3.000000', 'mm', NULL, '模具孔径', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (13, 5, '切削速度', '1500.000000', 'rpm', NULL, '铣削速度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (14, 5, '切削深度', '2.000000', 'mm', NULL, '切削深度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item` VALUES (16, 10, '曝光能量', '100.000000', 'J', NULL, NULL, '吴红姣', '2025-07-30 17:27:57', NULL, '2025-07-30 17:27:57');
INSERT INTO `process_param_item` VALUES (17, 10, '曝光能量', '100.000000', 'J', NULL, NULL, '吴红姣', '2025-07-30 17:28:21', NULL, '2025-07-30 17:28:21');
INSERT INTO `process_param_item` VALUES (18, 10, '曝光能量', '80.000000', '焦耳', NULL, NULL, '吴红姣', '2025-07-30 17:30:08', NULL, '2025-07-30 17:30:08');

-- ----------------------------
-- Table structure for process_param_item_backup
-- ----------------------------
DROP TABLE IF EXISTS `process_param_item_backup`;
CREATE TABLE `process_param_item_backup`  (
  `item_id` int NOT NULL DEFAULT 0 COMMENT '参数明细ID（主键）',
  `group_id` int NOT NULL COMMENT '所属参数组ID',
  `param_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数名称',
  `param_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数数值（字符串格式）',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数单位',
  `attachments` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of process_param_item_backup
-- ----------------------------
INSERT INTO `process_param_item_backup` VALUES (1, 1, '温度', '220.500000', '℃', NULL, '注塑温度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (2, 1, '压力', '80.000000', 'MPa', NULL, '注塑压力', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (3, 2, '温度', '230.000000', '℃', NULL, '注塑温度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (4, 2, '压力', '85.000000', 'MPa', NULL, '注塑压力', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (5, 3, '模具温度', '150.000000', '℃', NULL, '压铸模温', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (6, 3, '保压时间', '15.000000', 's', NULL, '保压时间', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (7, 7, '焊接电流', '120.000000', 'A', NULL, '焊接电流', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (8, 7, '焊接电压', '22.500000', 'V', NULL, '焊接电压', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (9, 4, '挤出速率', '5.000000', 'm/min', NULL, '挤出速度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (10, 4, '孔径', '2.500000', 'mm', NULL, '模具孔径', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (11, 8, '挤出速率', '5.500000', 'm/min', NULL, '挤出速度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (12, 8, '孔径', '3.000000', 'mm', NULL, '模具孔径', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (13, 5, '切削速度', '1500.000000', 'rpm', NULL, '铣削速度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (14, 5, '切削深度', '2.000000', 'mm', NULL, '切削深度', 'system', '2025-07-29 16:32:04', NULL, '2025-07-29 16:32:04');
INSERT INTO `process_param_item_backup` VALUES (16, 10, '曝光能量', '100.000000', 'J', NULL, NULL, '吴红姣', '2025-07-30 17:27:57', NULL, '2025-07-30 17:27:57');
INSERT INTO `process_param_item_backup` VALUES (17, 10, '曝光能量', '100.000000', 'J', NULL, NULL, '吴红姣', '2025-07-30 17:28:21', NULL, '2025-07-30 17:28:21');
INSERT INTO `process_param_item_backup` VALUES (18, 10, '曝光能量', '80.000000', '焦耳', NULL, NULL, '吴红姣', '2025-07-30 17:30:08', NULL, '2025-07-30 17:30:08');

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob NULL COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Blob类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_blob_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`, `calendar_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日历信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_calendars
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Cron类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_cron_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`, `entry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '已触发的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_fired_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_job_details
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`, `lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '存储的悲观锁信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`, `trigger_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '暂停的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_paused_trigger_grps
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`, `instance_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '调度器状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_scheduler_state
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '简单触发器的信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_simple_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int NULL DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int NULL DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint NULL DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint NULL DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '同步机制的行锁表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_simprop_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint NULL DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint NULL DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int NULL DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint NULL DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint NULL DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  INDEX `sched_name`(`sched_name` ASC, `job_name` ASC, `job_group` ASC) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '触发器详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2025-07-24 14:57:16', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2025-07-24 14:57:16', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2025-07-24 14:57:16', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'false', 'Y', 'admin', '2025-07-24 14:57:16', 'admin', '2025-07-30 09:55:09', '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2025-07-24 14:57:16', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2025-07-24 14:57:16', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` VALUES (7, '用户管理-初始密码修改策略', 'sys.account.initPasswordModify', '1', 'Y', 'admin', '2025-07-24 14:57:16', '', NULL, '0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框');
INSERT INTO `sys_config` VALUES (8, '用户管理-账号密码更新周期', 'sys.account.passwordValidateDays', '0', 'Y', 'admin', '2025-07-24 14:57:16', '', NULL, '密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 200 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '若依科技', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (101, 100, '0,100', '深圳总公司', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (102, 100, '0,100', '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (103, 101, '0,100,101', '研发部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (104, 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (105, 101, '0,100,101', '测试部门', 3, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (106, 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (107, 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (108, 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);
INSERT INTO `sys_dept` VALUES (109, 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-07-24 14:57:07', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2025-07-24 14:57:15', '', NULL, '停用状态');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2025-07-24 14:57:14', '', NULL, '登录状态列表');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2025-07-24 14:57:17', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2025-07-24 14:57:17', '', NULL, '');
INSERT INTO `sys_job` VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2025-07-24 14:57:17', '', NULL, '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 164 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-24 15:00:17');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-24 18:11:57');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-24 19:07:07');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-24 19:43:36');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-24 20:55:59');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-24 21:40:19');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-25 15:02:53');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-28 13:56:53');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-28 15:05:08');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-28 15:59:06');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 10:14:02');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-29 10:57:22');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 10:58:51');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-07-29 10:59:18');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 10:59:52');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-07-29 12:10:18');
INSERT INTO `sys_logininfor` VALUES (116, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 12:10:18');
INSERT INTO `sys_logininfor` VALUES (117, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 13:08:33');
INSERT INTO `sys_logininfor` VALUES (118, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 14:05:37');
INSERT INTO `sys_logininfor` VALUES (119, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-29 17:32:14');
INSERT INTO `sys_logininfor` VALUES (120, 'SN122507', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-07-29 17:32:28');
INSERT INTO `sys_logininfor` VALUES (121, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 17:32:49');
INSERT INTO `sys_logininfor` VALUES (122, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-29 17:33:13');
INSERT INTO `sys_logininfor` VALUES (123, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 17:33:18');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-29 17:33:48');
INSERT INTO `sys_logininfor` VALUES (125, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 17:34:09');
INSERT INTO `sys_logininfor` VALUES (126, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-29 17:35:44');
INSERT INTO `sys_logininfor` VALUES (127, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 17:35:49');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-29 17:38:10');
INSERT INTO `sys_logininfor` VALUES (129, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 17:38:16');
INSERT INTO `sys_logininfor` VALUES (130, '吴红姣', '************', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-07-29 17:41:36');
INSERT INTO `sys_logininfor` VALUES (131, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-29 17:42:01');
INSERT INTO `sys_logininfor` VALUES (132, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-07-29 17:42:06');
INSERT INTO `sys_logininfor` VALUES (133, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-07-29 17:42:11');
INSERT INTO `sys_logininfor` VALUES (134, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-29 17:42:24');
INSERT INTO `sys_logininfor` VALUES (135, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-07-30 09:52:28');
INSERT INTO `sys_logininfor` VALUES (136, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-07-30 09:52:33');
INSERT INTO `sys_logininfor` VALUES (137, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-07-30 09:52:42');
INSERT INTO `sys_logininfor` VALUES (138, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-30 09:52:56');
INSERT INTO `sys_logininfor` VALUES (139, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-30 09:55:18');
INSERT INTO `sys_logininfor` VALUES (140, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-30 09:55:19');
INSERT INTO `sys_logininfor` VALUES (141, '吴红姣', '************8', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2025-07-30 16:03:46');
INSERT INTO `sys_logininfor` VALUES (142, '吴红姣', '*************', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-30 16:10:14');
INSERT INTO `sys_logininfor` VALUES (143, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-30 16:11:46');
INSERT INTO `sys_logininfor` VALUES (144, '吴红姣', '************8', '内网IP', 'Chrome 11', 'Windows 10', '0', '退出成功', '2025-07-30 16:15:01');
INSERT INTO `sys_logininfor` VALUES (145, 'admin', '************8', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2025-07-30 16:15:11');
INSERT INTO `sys_logininfor` VALUES (146, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-30 16:22:15');
INSERT INTO `sys_logininfor` VALUES (147, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-30 16:22:19');
INSERT INTO `sys_logininfor` VALUES (148, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-30 16:23:05');
INSERT INTO `sys_logininfor` VALUES (149, '吴红姣', '*************', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-30 17:08:31');
INSERT INTO `sys_logininfor` VALUES (150, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-30 18:09:37');
INSERT INTO `sys_logininfor` VALUES (151, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-31 19:17:42');
INSERT INTO `sys_logininfor` VALUES (152, '吴红姣', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-31 19:17:48');
INSERT INTO `sys_logininfor` VALUES (153, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-07-31 19:17:52');
INSERT INTO `sys_logininfor` VALUES (154, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-07-31 19:17:58');
INSERT INTO `sys_logininfor` VALUES (155, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-31 19:18:04');
INSERT INTO `sys_logininfor` VALUES (156, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-08-01 10:40:29');
INSERT INTO `sys_logininfor` VALUES (157, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-08-01 13:35:43');
INSERT INTO `sys_logininfor` VALUES (158, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-08-01 14:20:22');
INSERT INTO `sys_logininfor` VALUES (159, '吴红姣', '*************', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-08-01 14:51:16');
INSERT INTO `sys_logininfor` VALUES (160, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-08-01 15:17:14');
INSERT INTO `sys_logininfor` VALUES (161, '吴红姣', '*************', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-08-01 15:23:09');
INSERT INTO `sys_logininfor` VALUES (162, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-08-01 16:34:00');
INSERT INTO `sys_logininfor` VALUES (163, 'admin', '1********', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-08-01 17:27:23');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2030 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2025-07-24 14:57:10', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2025-07-24 14:57:10', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2025-07-24 14:57:10', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` VALUES (4, '材料官网', 0, 4, 'http://ruoyi.vip', NULL, '', '', 0, 0, 'M', '0', '0', '', 'guide', 'admin', '2025-07-24 14:57:10', 'admin', '2025-07-30 09:56:00', '若依官网地址');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2025-07-24 14:57:10', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2025-07-24 14:57:10', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2025-07-24 14:57:10', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2025-07-24 14:57:10', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2025-07-24 14:57:10', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2025-07-24 14:57:10', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2025-07-24 14:57:10', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2025-07-24 14:57:10', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2025-07-24 14:57:10', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2025-07-24 14:57:10', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2025-07-24 14:57:10', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2025-07-24 14:57:10', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2025-07-24 14:57:10', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2025-07-24 14:57:10', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2025-07-24 14:57:10', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2025-07-24 14:57:10', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2025-07-24 14:57:10', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2025-07-24 14:57:10', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2025-07-24 14:57:10', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2025-07-24 14:57:10', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 116, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 116, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 116, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 116, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 116, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 116, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2025-07-24 14:57:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2000, '材料管理', 0, 5, 'material', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'build', 'admin', '2025-07-24 14:59:04', '', NULL, '材料信息管理菜单');
INSERT INTO `sys_menu` VALUES (2001, '材料及参数配置', 2000, 1, 'config', 'material/config/index', NULL, '', 1, 0, 'C', '0', '0', 'material:material:list', 'tree', 'admin', '2025-07-24 14:59:04', 'admin', '2025-07-30 16:24:06', '材料及参数配置菜单');
INSERT INTO `sys_menu` VALUES (2002, '测试方案配置', 2000, 2, 'testPlan', 'material/testPlan/index', NULL, '', 1, 0, 'C', '0', '0', 'material:testPlan:list', 'form', 'admin', '2025-07-24 14:59:04', '', NULL, '测试方案配置菜单');
INSERT INTO `sys_menu` VALUES (2003, '数据录入', 2000, 3, 'testResult', 'material/testResult/index', NULL, '', 1, 0, 'C', '0', '0', 'material:testResult:list', 'edit', 'admin', '2025-07-24 14:59:04', '', NULL, '测试数据录入菜单');
INSERT INTO `sys_menu` VALUES (2004, '趋势对比', 2000, 4, 'trend', 'material/trend/index', NULL, '', 1, 0, 'C', '0', '0', 'material:trend:view', 'chart', 'admin', '2025-07-24 14:59:04', '', NULL, '数据趋势对比菜单');
INSERT INTO `sys_menu` VALUES (2005, '材料信息查询', 2001, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:material:query', '#', 'admin', '2025-07-24 14:59:04', 'admin', '2025-07-30 16:13:50', '');
INSERT INTO `sys_menu` VALUES (2006, '材料信息新增', 2001, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:material:add', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2007, '材料信息修改', 2001, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:material:edit', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2008, '材料信息删除', 2001, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:material:remove', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2009, '材料信息导出', 2001, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:material:export', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2010, '工艺参数组列表', 2001, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:processParamGroup:list', '#', 'admin', '2025-07-24 14:59:04', 'admin', '2025-07-30 16:30:11', '');
INSERT INTO `sys_menu` VALUES (2011, '工艺参数组新增', 2001, 7, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:processParamGroup:add', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2012, '工艺参数组修改', 2001, 8, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:processParamGroup:edit', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2013, '工艺参数组删除', 2001, 9, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:processParamGroup:remove', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2014, '工艺参数明细列表', 2001, 10, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:processParamItem:list', '#', 'admin', '2025-07-24 14:59:04', 'admin', '2025-07-30 16:38:42', '');
INSERT INTO `sys_menu` VALUES (2015, '工艺参数明细新增', 2001, 11, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:processParamItem:add', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2016, '工艺参数明细修改', 2001, 12, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:processParamItem:edit', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2017, '工艺参数明细删除', 2001, 13, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:processParamItem:remove', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2018, '测试方案查询', 2002, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testPlan:query', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2019, '测试方案新增', 2002, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testPlan:add', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2020, '测试方案修改', 2002, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testPlan:edit', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2021, '测试方案删除', 2002, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testPlan:remove', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2022, '测试方案导出', 2002, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testPlan:export', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2023, '测试结果查询', 2003, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testResult:query', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2024, '测试结果新增', 2003, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testResult:add', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2025, '测试结果修改', 2003, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testResult:edit', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2026, '测试结果删除', 2003, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testResult:remove', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2027, '测试结果导出', 2003, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'material:testResult:export', '#', 'admin', '2025-07-24 14:59:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2028, '工艺参数组查询', 2001, 6, 'material:processParamGroup:query', NULL, NULL, '', 1, 0, 'F', '0', '0', 'material:processParamGroup:query', '#', 'admin', '2025-07-30 16:29:49', 'admin', '2025-07-30 16:30:45', '');
INSERT INTO `sys_menu` VALUES (2029, '工艺参数明细查询', 2001, 10, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'material:processParamItem:query', '#', 'admin', '2025-07-30 16:39:17', '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2025-07-24 14:57:19', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2025-07-24 14:57:19', '', NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 259 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (100, '测试方案', 1, 'com.ruoyi.web.controller.material.TestPlanController.add()', 'POST', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"createTime\":\"2025-07-24 15:03:16\",\"params\":{},\"performanceName\":\"测试\",\"planCode\":\"TP004\",\"testPlanId\":4}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 15:03:17', 289);
INSERT INTO `sys_oper_log` VALUES (101, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.export()', 'POST', 1, 'admin', '研发部门', '/material/material/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-24 18:17:58', 915);
INSERT INTO `sys_oper_log` VALUES (102, '工艺参数组', 5, 'com.ruoyi.web.controller.material.ProcessParamGroupController.export()', 'POST', 1, 'admin', '研发部门', '/material/processParamGroup/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"materialId\":\"2\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-24 18:18:35', 18);
INSERT INTO `sys_oper_log` VALUES (103, '整体导出', 5, 'com.ruoyi.web.controller.material.ProcessParamGroupController.exportComplete()', 'POST', 1, 'admin', '研发部门', '/material/processParamGroup/exportComplete', '1********', '内网IP', '{\"materialId\":\"1\"}', NULL, 0, NULL, '2025-07-24 19:45:55', 711);
INSERT INTO `sys_oper_log` VALUES (104, '工艺参数明细', 5, 'com.ruoyi.web.controller.material.ProcessParamItemController.export()', 'POST', 1, 'admin', '研发部门', '/material/processParamItem/export', '1********', '内网IP', '{\"groupId\":\"1\",\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-24 19:46:15', 23);
INSERT INTO `sys_oper_log` VALUES (105, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"[{\\\"name\\\":\\\"G3延误未处理或即将到期trigger数据.xlsx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/24/G3延误未处理或即将到期trigger数据_20250724194701A002.xlsx\\\",\\\"size\\\":\\\"12.38 KB\\\",\\\"uid\\\":1753357621582,\\\"status\\\":\\\"success\\\"}]\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12K\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-24 14:57:50\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-24 14:57:50\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-24 19:47:02\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 19:47:03', 1220);
INSERT INTO `sys_oper_log` VALUES (106, '工艺参数组', 2, 'com.ruoyi.web.controller.material.ProcessParamGroupController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamGroup', '1********', '内网IP', '{\"attachmentList\":[{\"name\":\"Trigger关联Yyx更新需求文档_V7.docx\",\"url\":\"http://localhost:8080/profile/upload/2025/07/24/Trigger关联Yyx更新需求文档_V7_20250724194719A003.docx\",\"size\":\"1.01 MB\",\"uid\":1753357639680,\"status\":\"success\"}],\"attachments\":\"[{\\\"name\\\":\\\"Trigger关联Yyx更新需求文档_V7.docx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/24/Trigger关联Yyx更新需求文档_V7_20250724194719A003.docx\\\",\\\"size\\\":\\\"1.01 MB\\\",\\\"uid\\\":1753357639680,\\\"status\\\":\\\"success\\\"}]\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":3,\"materialId\":2,\"materialName\":\"玻璃纤维\",\"paramNumber\":\"P003\",\"params\":{},\"processParamItemList\":[{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":3,\"itemId\":6,\"paramName\":\"拉挤速度\",\"paramValue\":2.5,\"params\":{},\"remark\":\"拉挤线速度\",\"unit\":\"m/min\",\"updateTime\":\"2025-07-24 14:57:50\"}],\"processType\":\"拉挤工艺\",\"remark\":\"玻璃纤维拉挤参数组\",\"updateTime\":\"2025-07-24 19:47:22\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 19:47:22', 467);
INSERT INTO `sys_oper_log` VALUES (107, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":3,\"itemId\":6,\"paramName\":\"拉挤速度\",\"paramValue\":2.5,\"params\":{},\"remark\":\"拉挤线速度\",\"unit\":\"m/min\",\"updateTime\":\"2025-07-24 19:47:38\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 19:47:38', 106);
INSERT INTO `sys_oper_log` VALUES (108, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":3,\"itemId\":6,\"paramName\":\"拉挤速度\",\"paramValue\":2.5,\"params\":{},\"remark\":\"拉挤线速度\",\"unit\":\"m/min\",\"updateTime\":\"2025-07-24 19:47:48\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 19:47:49', 370);
INSERT INTO `sys_oper_log` VALUES (109, '材料配置', 5, 'com.ruoyi.web.controller.material.MaterialController.exportComplete()', 'POST', 1, 'admin', '研发部门', '/material/material/exportComplete', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 1, '\r\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'ppi.param_id\' in \'field list\'\r\n### The error may exist in file [F:\\IDEA Project\\RuoYi-Vue\\ruoyi-system\\target\\classes\\mapper\\system\\MaterialMapper.xml]\r\n### The error may involve com.ruoyi.system.mapper.MaterialMapper.selectCompleteExportData-Inline\r\n### The error occurred while setting parameters\r\n### SQL: SELECT              m.material_id as materialId,             m.material_name as materialName,             m.supplier_name as supplierName,             m.material_model as materialModel,             m.material_description as materialDescription,             m.create_by as materialCreateBy,             m.create_time as materialCreateTime,             m.update_by as materialUpdateBy,             m.update_time as materialUpdateTime,             ppg.group_id as groupId,             ppg.process_type as processType,             ppg.param_number as paramNumber,             ppg.create_by as groupCreateBy,             ppg.create_time as groupCreateTime,             ppg.update_by as groupUpdateBy,             ppg.update_time as groupUpdateTime,             ppi.param_id as paramId,             ppi.param_name as paramName,             ppi.param_value as paramValue,             ppi.unit as unit,             ppi.create_by as paramCreateBy,             ppi.create_time as paramCreateTime,             ppi.update_by as paramUpdateBy,             ppi.update_time as paramUpdateTime         FROM materials m         LEFT JOIN process_param_group ppg ON m.material_id = ppg.material_id         LEFT JOIN process_param_item ppi ON ppg.group_id = ppi.group_id         WHERE 1=1                             ORDER BY m.material_id, ppg.group_id, ppi.param_id\r\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'ppi.param_id\' in \'field list\'\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'ppi.param_id\' in \'field list\'', '2025-07-24 20:23:07', 829);
INSERT INTO `sys_oper_log` VALUES (110, '材料信息', 1, 'com.ruoyi.web.controller.material.MaterialController.add()', 'POST', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"[]\",\"createTime\":\"2025-07-24 20:24:42\",\"materialId\":3,\"materialName\":\"测试\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 20:24:43', 957);
INSERT INTO `sys_oper_log` VALUES (111, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.export()', 'POST', 1, 'admin', '研发部门', '/material/material/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-24 20:25:04', 1217);
INSERT INTO `sys_oper_log` VALUES (112, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/3', '1********', '内网IP', '[3]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 20:27:14', 270);
INSERT INTO `sys_oper_log` VALUES (113, '工艺参数组', 2, 'com.ruoyi.web.controller.material.ProcessParamGroupController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamGroup', '1********', '内网IP', '{\"attachmentList\":[{\"name\":\"Trigger关联Yyx更新需求文档_V8.docx\",\"url\":\"http://localhost:8080/profile/upload/2025/07/24/Trigger关联Yyx更新需求文档_V8_20250724202724A001.docx\",\"size\":\"1.04 MB\",\"uid\":1753360044390,\"status\":\"success\"}],\"attachments\":\"[{\\\"name\\\":\\\"Trigger关联Yyx更新需求文档_V8.docx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/24/Trigger关联Yyx更新需求文档_V8_20250724202724A001.docx\\\",\\\"size\\\":\\\"1.04 MB\\\",\\\"uid\\\":1753360044390,\\\"status\\\":\\\"success\\\"}]\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"materialName\":\"碳纤维复合材料\",\"paramNumber\":\"P001\",\"params\":{},\"processParamItemList\":[],\"processType\":\"固化工艺\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-24 20:27:26\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 20:27:27', 251);
INSERT INTO `sys_oper_log` VALUES (114, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":3,\"itemId\":6,\"paramName\":\"拉挤速度\",\"paramValue\":2.5,\"params\":{},\"remark\":\"拉挤线速度\",\"unit\":\"m/min\",\"updateTime\":\"2025-07-24 20:27:44\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 20:27:45', 219);
INSERT INTO `sys_oper_log` VALUES (115, '材料完整数据', 5, 'com.ruoyi.web.controller.material.MaterialController.exportComplete()', 'POST', 1, 'admin', '研发部门', '/material/material/exportComplete', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 1, '\r\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table \'codebuddy.process_param_groups\' doesn\'t exist\r\n### The error may exist in URL [jar:file:/F:/IDEA%20Project/RuoYi-Vue/ruoyi-admin/target/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.9.0.jar!/mapper/system/MaterialMapper.xml]\r\n### The error may involve defaultParameterMap\r\n### The error occurred while setting parameters\r\n### SQL: SELECT              m.material_id as materialId,             m.material_name as materialName,             m.supplier_name as supplierName,             m.material_model as materialModel,             m.material_description as materialDescription,             m.create_by as materialCreateBy,             m.create_time as materialCreateTime,             m.update_by as materialUpdateBy,             m.update_time as materialUpdateTime,             ppg.group_id as groupId,             ppg.process_type as processType,             ppg.param_number as paramNumber,             ppg.create_by as groupCreateBy,             ppg.create_time as groupCreateTime,             ppg.update_by as groupUpdateBy,             ppg.update_time as groupUpdateTime,             ppi.item_id as paramId,             ppi.param_name as paramName,             ppi.param_value as paramValue,             ppi.unit as unit,             ppi.create_by as paramCreateBy,             ppi.create_time as paramCreateTime,             ppi.update_by as paramUpdateBy,             ppi.update_time as paramUpdateTime         FROM materials m         LEFT JOIN process_param_groups ppg ON m.material_id = ppg.material_id         LEFT JOIN process_param_items ppi ON ppg.group_id = ppi.group_id         WHERE 1=1                             ORDER BY m.material_id, ppg.group_id, ppi.item_id\r\n### Cause: java.sql.SQLSyntaxErrorException: Table \'codebuddy.process_param_groups\' doesn\'t exist\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table \'codebuddy.process_param_groups\' doesn\'t exist', '2025-07-24 20:58:16', 1986);
INSERT INTO `sys_oper_log` VALUES (116, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"[{\\\"name\\\":\\\"particle dashboard导出不合格明细模板.xlsx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/24/particle dashboard导出不合格明细模板_20250724210051A002.xlsx\\\",\\\"size\\\":\\\"10.61 KB\\\",\\\"uid\\\":1753362051068,\\\"status\\\":\\\"success\\\"}]\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"标准玻璃纤维材料\",\"materialId\":2,\"materialModel\":\"E-Glass-2400\",\"materialName\":\"玻璃纤维\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"[{\\\"name\\\":\\\"Trigger关联Yyx更新需求文档_V7.docx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/24/Trigger关联Yyx更新需求文档_V7_20250724194719A003.docx\\\",\\\"size\\\":\\\"1.01 MB\\\",\\\"uid\\\":1753357639680,\\\"status\\\":\\\"success\\\"}]\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":3,\"materialId\":2,\"paramNumber\":\"P003\",\"params\":{},\"processType\":\"拉挤工艺\",\"remark\":\"玻璃纤维拉挤参数组\",\"updateTime\":\"2025-07-24 19:47:22\"}],\"remark\":\"常用材料\",\"supplierName\":\"巨石集团\",\"updateTime\":\"2025-07-24 21:00:53\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 21:00:55', 1986);
INSERT INTO `sys_oper_log` VALUES (117, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachmentList\":[],\"createTime\":\"2025-07-24 21:02:17\",\"groupId\":3,\"itemId\":7,\"paramName\":\"1516\",\"paramValue\":0.0,\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-24 21:02:18', 811);
INSERT INTO `sys_oper_log` VALUES (118, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.export()', 'POST', 1, 'admin', '研发部门', '/material/material/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-25 15:04:11', 751);
INSERT INTO `sys_oper_log` VALUES (119, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.export()', 'POST', 1, 'admin', '研发部门', '/material/material/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-25 15:31:27', 33);
INSERT INTO `sys_oper_log` VALUES (120, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.exportComplete()', 'POST', 1, 'admin', '研发部门', '/material/material/exportComplete', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-25 15:33:56', 19);
INSERT INTO `sys_oper_log` VALUES (121, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachmentList\":[],\"createBy\":\"admin\",\"createTime\":\"2025-07-24 21:50:05\",\"groupId\":1,\"itemId\":8,\"paramName\":\"固化温度\",\"paramValue\":181.0,\"params\":{},\"remark\":\"标准固化温度\",\"unit\":\"℃\",\"updateTime\":\"2025-07-25 15:51:48\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-25 15:51:49', 257);
INSERT INTO `sys_oper_log` VALUES (122, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.export()', 'POST', 1, 'admin', '研发部门', '/material/material/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-25 16:17:36', 40);
INSERT INTO `sys_oper_log` VALUES (123, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.exportComplete()', 'POST', 1, 'admin', '研发部门', '/material/material/exportComplete', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-25 16:17:58', 33);
INSERT INTO `sys_oper_log` VALUES (124, '材料信息', 6, 'com.ruoyi.web.controller.material.MaterialController.importData()', 'POST', 1, 'admin', '研发部门', '/material/material/importData', '1********', '内网IP', 'false', NULL, 1, 'Cannot invoke \"org.springframework.web.multipart.MultipartFile.getInputStream()\" because \"file\" is null', '2025-07-25 16:43:53', 66);
INSERT INTO `sys_oper_log` VALUES (125, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachmentList\":[{\"name\":\"material_1753428687313.xlsx\",\"url\":\"http://localhost:8080/profile/upload/2025/07/25/material_1753428687313_20250725170439A001.xlsx\",\"size\":\"3.86 KB\",\"uid\":1753434279514,\"status\":\"success\"}],\"attachments\":\"http://localhost:8080/profile/upload/2025/07/25/material_1753428687313_20250725170439A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 21:50:05\",\"groupId\":1,\"itemId\":8,\"paramName\":\"固化温度\",\"paramValue\":181.0,\"params\":{},\"remark\":\"标准固化温度\",\"unit\":\"℃\",\"updateTime\":\"2025-07-25 17:04:41\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-25 17:04:42', 323);
INSERT INTO `sys_oper_log` VALUES (126, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"[{\\\"name\\\":\\\"complete_data_1753428836123.xlsx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/25/complete_data_1753428836123_20250725171023A001.xlsx\\\",\\\"size\\\":\\\"NaN MB\\\",\\\"uid\\\":1753434626396,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"material_1753427050616.xlsx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/25/material_1753427050616_20250725171026A002.xlsx\\\",\\\"size\\\":\\\"3.86 KB\\\",\\\"uid\\\":1753434626397,\\\"status\\\":\\\"success\\\"}]\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12K\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachmentList\":[\"[{\\\"name\\\":\\\"Trigger关联Yyx更新需求文档_V8.docx\\\"\",\"\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/24/Trigger关联Yyx更新需求文档_V8_20250724202724A001.docx\\\"\",\"\\\"size\\\":\\\"1.04 MB\\\"\",\"\\\"uid\\\":1753360044390\",\"\\\"status\\\":\\\"success\\\"}]\"],\"attachments\":\"[{\\\"name\\\":\\\"Trigger关联Yyx更新需求文档_V8.docx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/24/Trigger关联Yyx更新需求文档_V8_20250724202724A001.docx\\\",\\\"size\\\":\\\"1.04 MB\\\",\\\"uid\\\":1753360044390,\\\"status\\\":\\\"success\\\"}]\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-24 20:27:26\"},{\"attachmentList\":[],\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-24 14:57:50\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-25 17:10:27\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-25 17:10:28', 566);
INSERT INTO `sys_oper_log` VALUES (127, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachmentList\":[{\"name\":\"complete_data_1753431478772.xlsx\",\"url\":\"http://localhost:8080/profile/upload/2025/07/25/complete_data_1753431478772_20250725171123A003.xlsx\",\"size\":\"4.26 KB\",\"uid\":1753434683181,\"status\":\"success\"}],\"attachments\":\"http://localhost:8080/profile/upload/2025/07/25/complete_data_1753431478772_20250725171123A003.xlsx\",\"createTime\":\"2025-07-24 21:02:17\",\"groupId\":3,\"itemId\":7,\"paramName\":\"1516\",\"paramValue\":0.0,\"params\":{},\"updateTime\":\"2025-07-25 17:11:25\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-25 17:11:25', 106);
INSERT INTO `sys_oper_log` VALUES (128, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.exportComplete()', 'POST', 1, 'admin', '研发部门', '/material/material/exportComplete', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-28 13:57:11', 1977);
INSERT INTO `sys_oper_log` VALUES (129, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachmentList\":[{\"name\":\"complete_data_1753428836123.xlsx\",\"url\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728141529A001.xlsx\",\"size\":\"3.95 KB\",\"uid\":1753683329559,\"status\":\"success\"}],\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728141529A001.xlsx\",\"createTime\":\"2025-07-24 21:02:17\",\"groupId\":3,\"itemId\":7,\"paramName\":\"1516\",\"paramValue\":0.0,\"params\":{},\"updateTime\":\"2025-07-28 14:15:33\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 14:15:34', 928);
INSERT INTO `sys_oper_log` VALUES (130, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachmentList\":[{\"name\":\"material_1753431455794.xlsx\",\"url\":\"http://localhost:8080/profile/upload/2025/07/28/material_1753431455794_20250728141541A002.xlsx\",\"size\":\"3.86 KB\",\"uid\":1753683341362,\"status\":\"success\"}],\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/material_1753431455794_20250728141541A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 21:50:05\",\"groupId\":3,\"itemId\":13,\"paramName\":\"拉挤速度\",\"paramValue\":2.5,\"params\":{},\"remark\":\"拉挤线速度\",\"unit\":\"m/min\",\"updateTime\":\"2025-07-28 14:15:43\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 14:15:44', 453);
INSERT INTO `sys_oper_log` VALUES (131, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"[{\\\"name\\\":\\\"[{\\\\\\\"name\\\\\\\":\\\\\\\"complete_data_1753428836123.xlsx\\\\\\\"\\\",\\\"url\\\":\\\"[{\\\\\\\"name\\\\\\\":\\\\\\\"complete_data_1753428836123.xlsx\\\\\\\"\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164366,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"complete_data_1753428836123_20250725171023A001.xlsx\\\\\\\"\\\",\\\"url\\\":\\\"\\\\\\\"url\\\\\\\":\\\\\\\"http://localhost:8080/profile/upload/2025/07/25/complete_data_1753428836123_20250725171023A001.xlsx\\\\\\\"\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164367,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"\\\\\\\"size\\\\\\\":\\\\\\\"NaN MB\\\\\\\"\\\",\\\"url\\\":\\\"\\\\\\\"size\\\\\\\":\\\\\\\"NaN MB\\\\\\\"\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164368,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"\\\\\\\"uid\\\\\\\":1753434626396\\\",\\\"url\\\":\\\"\\\\\\\"uid\\\\\\\":1753434626396\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164369,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"\\\\\\\"status\\\\\\\":\\\\\\\"success\\\\\\\"}\\\",\\\"url\\\":\\\"\\\\\\\"status\\\\\\\":\\\\\\\"success\\\\\\\"}\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164370,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"{\\\\\\\"name\\\\\\\":\\\\\\\"material_1753427050616.xlsx\\\\\\\"\\\",\\\"url\\\":\\\"{\\\\\\\"name\\\\\\\":\\\\\\\"material_1753427050616.xlsx\\\\\\\"\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164371,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"material_1753427050616_20250725171026A002.xlsx\\\\\\\"\\\",\\\"url\\\":\\\"\\\\\\\"url\\\\\\\":\\\\\\\"http://localhost:8080/profile/upload/2025/07/25/material_1753427050616_20250725171026A002.xlsx\\\\\\\"\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164372,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"\\\\\\\"size\\\\\\\":\\\\\\\"3.86 KB\\\\\\\"\\\",\\\"url\\\":\\\"\\\\\\\"size\\\\\\\":\\\\\\\"3.86 KB\\\\\\\"\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164373,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"\\\\\\\"uid\\\\\\\":1753434626397\\\",\\\"url\\\":\\\"\\\\\\\"uid\\\\\\\":1753434626397\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164374,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"\\\\\\\"status\\\\\\\":\\\\\\\"success\\\\\\\"}]\\\",\\\"url\\\":\\\"\\\\\\\"status\\\\\\\":\\\\\\\"success\\\\\\\"}]\\\",\\\"size\\\":\\\"0 B\\\",\\\"uid\\\":1753684164375,\\\"status\\\":\\\"success\\\"},{\\\"name\\\":\\\"complete_data_1753682229187.xlsx\\\",\\\"url\\\":\\\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753682229187_20250728142928A003.xlsx\\\",\\\"size\\\":\\\"4.11 KB\\\",\\\"uid\\\":175', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 14:29:30', 554);
INSERT INTO `sys_oper_log` VALUES (132, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/2025-07-28 11_23_04-无纸化记录查询数据_20250728150521A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12K\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-28 15:04:48\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-24 14:57:50\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-28 15:05:24\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 15:05:25', 702);
INSERT INTO `sys_oper_log` VALUES (133, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/2025-07-28 11_23_04-无纸化记录查询数据_20250728150521A001.xlsx,http://localhost:8080/profile/upload/2025/07/28/2025-07-28 11_05_15-导出_20250728150536A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12K\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-28 15:04:48\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-24 14:57:50\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-28 15:05:37\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 15:05:37', 154);
INSERT INTO `sys_oper_log` VALUES (134, '工艺参数组', 2, 'com.ruoyi.web.controller.material.ProcessParamGroupController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamGroup', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"materialName\":\"碳纤维复合材料\",\"paramNumber\":\"P001\",\"params\":{},\"processParamItemList\":[],\"processType\":\"固化工艺\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-28 15:06:25\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 15:06:25', 105);
INSERT INTO `sys_oper_log` VALUES (135, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/2025-07-28 11_05_15-导出_20250728150536A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12K\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-28 15:06:25\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-24 14:57:50\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-28 15:06:41\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 15:06:41', 160);
INSERT INTO `sys_oper_log` VALUES (136, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728141529A001.xlsx,http://localhost:8080/profile/upload/2025/07/28/material_1753427050616_20250728150701A004.xlsx\",\"createTime\":\"2025-07-24 21:02:17\",\"groupId\":3,\"itemId\":7,\"paramName\":\"1516\",\"paramValue\":0.0,\"params\":{},\"updateTime\":\"2025-07-28 15:07:02\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 15:07:02', 148);
INSERT INTO `sys_oper_log` VALUES (137, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/material_1753427050616_20250728150701A004.xlsx\",\"createTime\":\"2025-07-24 21:02:17\",\"groupId\":3,\"itemId\":7,\"paramName\":\"1516\",\"paramValue\":0.0,\"params\":{},\"updateTime\":\"2025-07-28 15:07:10\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 15:07:10', 106);
INSERT INTO `sys_oper_log` VALUES (138, '测试方案', 5, 'com.ruoyi.web.controller.material.TestPlanController.export()', 'POST', 1, 'admin', '研发部门', '/material/testPlan/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-28 15:20:32', 1619);
INSERT INTO `sys_oper_log` VALUES (139, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/test_plan_1753687230997_20250728155919A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP001\",\"remark\":\"标准拉伸测试\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"最大拉伸强度\",\"testPlanId\":1,\"updateTime\":\"2025-07-28 15:59:21\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 15:59:22', 393);
INSERT INTO `sys_oper_log` VALUES (140, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/test_plan_1753687230997_20250728160325A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP001\",\"remark\":\"标准拉伸测试\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"最大拉伸强度\",\"testPlanId\":1,\"updateTime\":\"2025-07-28 16:03:27\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 16:03:27', 122);
INSERT INTO `sys_oper_log` VALUES (141, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728141529A001_20250728160334A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"params\":{},\"performanceName\":\"弯曲强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP002\",\"remark\":\"三点弯曲测试\",\"testEquipment\":\"三点弯曲试验机\",\"testParameter\":\"弯曲强度\",\"testPlanId\":2,\"updateTime\":\"2025-07-28 16:03:35\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 16:03:35', 166);
INSERT INTO `sys_oper_log` VALUES (142, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/test_plan_1753687230997_20250728160325A001.xlsx,http://localhost:8080/profile/upload/2025/07/28/material_1753428687313_20250728162905A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP001\",\"remark\":\"标准拉伸测试\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"最大拉伸强度\",\"testPlanId\":1,\"updateTime\":\"2025-07-28 16:29:07\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 16:29:08', 617);
INSERT INTO `sys_oper_log` VALUES (143, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/2025-07-28 11_05_15-导出_20250728165501A001.xlsx\",\"createTime\":\"2025-07-24 15:03:16\",\"params\":{},\"performanceName\":\"测试\",\"performanceType\":\"热学性能\",\"planCode\":\"TP004\",\"testPlanId\":4,\"updateTime\":\"2025-07-28 16:55:19\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 16:55:20', 994);
INSERT INTO `sys_oper_log` VALUES (144, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/test_plan_1753687230997_20250728173027A001.xlsx\",\"createTime\":\"2025-07-28 17:30:30\",\"groupId\":1,\"params\":{},\"testPlanId\":1,\"testResultId\":9,\"testValue\":3}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-28 17:30:30', 306);
INSERT INTO `sys_oper_log` VALUES (145, '测试结果', 5, 'com.ruoyi.web.controller.material.TestResultController.export()', 'POST', 1, 'admin', '研发部门', '/material/testResult/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-29 10:24:54', 873);
INSERT INTO `sys_oper_log` VALUES (146, '测试结果', 2, 'com.ruoyi.web.controller.material.TestResultController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/test_plan_1753687230997_20250728173027A001.xlsx\",\"createTime\":\"2025-07-28 17:30:30\",\"groupId\":1,\"materialModel\":\"T700-12K\",\"materialName\":\"碳纤维复合材料\",\"paramNumber\":\"P001\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP001\",\"processType\":\"固化工艺\",\"supplierName\":\"东丽公司\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"最大拉伸强度\",\"testPlanId\":1,\"testResultId\":9,\"testValue\":3.25,\"updateTime\":\"2025-07-29 10:40:04\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 10:40:05', 470);
INSERT INTO `sys_oper_log` VALUES (147, '测试结果', 2, 'com.ruoyi.web.controller.material.TestResultController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/test_plan_1753687230997_20250728173027A001.xlsx\",\"createTime\":\"2025-07-28 17:30:30\",\"groupId\":1,\"materialModel\":\"T700-12K\",\"materialName\":\"碳纤维复合材料\",\"paramNumber\":\"P001\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP001\",\"processType\":\"固化工艺\",\"supplierName\":\"东丽公司\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"最大拉伸强度\",\"testPlanId\":2,\"testResultId\":9,\"testValue\":3.25,\"updateTime\":\"2025-07-29 10:40:23\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 10:40:24', 804);
INSERT INTO `sys_oper_log` VALUES (148, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createTime\":\"2025-07-29 12:12:05\",\"groupId\":3,\"params\":{},\"testPlanCode\":\"TP002\",\"testPlanId\":2,\"testResultId\":10,\"testValue\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 12:12:05', 228);
INSERT INTO `sys_oper_log` VALUES (149, '测试结果', 5, 'com.ruoyi.web.controller.material.TestResultController.export()', 'POST', 1, 'admin', '研发部门', '/material/testResult/export', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-29 12:12:42', 921);
INSERT INTO `sys_oper_log` VALUES (150, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createTime\":\"2025-07-29 13:09:10\",\"groupId\":1,\"itemId\":14,\"paramName\":\"测试1\",\"paramValue\":0.0,\"params\":{},\"unit\":\"ml\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:09:10', 250);
INSERT INTO `sys_oper_log` VALUES (151, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createTime\":\"2025-07-29 13:09:10\",\"groupId\":1,\"itemId\":14,\"paramName\":\"测试1\",\"paramValue\":5.0,\"params\":{},\"unit\":\"ml\",\"updateTime\":\"2025-07-29 13:09:17\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:09:17', 91);
INSERT INTO `sys_oper_log` VALUES (152, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createTime\":\"2025-07-29 13:09:35\",\"groupId\":2,\"itemId\":15,\"paramName\":\"测试2\",\"paramValue\":4.0,\"params\":{},\"unit\":\"l\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:09:35', 61);
INSERT INTO `sys_oper_log` VALUES (153, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createTime\":\"2025-07-29 13:09:47\",\"groupId\":1,\"itemId\":16,\"paramName\":\"测试2\",\"paramValue\":4.0,\"params\":{},\"unit\":\"L\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:09:47', 257);
INSERT INTO `sys_oper_log` VALUES (154, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 13:34:55\",\"groupId\":3,\"params\":{},\"testPlanCode\":\"TP001\",\"testPlanId\":1,\"testResultId\":11,\"testValue\":6}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:34:55', 252);
INSERT INTO `sys_oper_log` VALUES (155, '测试结果', 3, 'com.ruoyi.web.controller.material.TestResultController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/testResult/10,10', '1********', '内网IP', '[10,10]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:35:28', 1426);
INSERT INTO `sys_oper_log` VALUES (156, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/test_plan_1753687230997_20250728160325A001.xlsx,http://localhost:8080/profile/upload/2025/07/28/material_1753428687313_20250728162905A001.xlsx,http://localhost:8080/profile/upload/2025/07/29/test_result_1753762360927_20250729133551A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP001\",\"remark\":\"标准拉伸测试\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"最大拉伸强度\",\"testPlanId\":1,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 13:35:54\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:35:54', 339);
INSERT INTO `sys_oper_log` VALUES (157, '测试方案', 1, 'com.ruoyi.web.controller.material.TestPlanController.add()', 'POST', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-29 13:37:25\",\"params\":{},\"performanceName\":\"测试2\",\"performanceType\":\"热学性能\",\"planCode\":\"TP005\",\"testPlanId\":5}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:37:25', 83);
INSERT INTO `sys_oper_log` VALUES (158, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/2025-07-28 11_05_15-导出_20250728150536A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12KB\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-28 15:06:25\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-24 14:57:50\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 13:37:55\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:37:55', 419);
INSERT INTO `sys_oper_log` VALUES (159, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728141529A001_20250728160334A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"params\":{},\"performanceName\":\"弯曲强度测试1\",\"performanceType\":\"力学性能\",\"planCode\":\"TP002\",\"remark\":\"三点弯曲测试\",\"testEquipment\":\"三点弯曲试验机\",\"testParameter\":\"弯曲强度\",\"testPlanId\":2,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 13:38:09\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:38:10', 1071);
INSERT INTO `sys_oper_log` VALUES (160, '工艺参数组', 2, 'com.ruoyi.web.controller.material.ProcessParamGroupController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamGroup', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"materialName\":\"碳纤维复合材料\",\"paramNumber\":\"P001\",\"params\":{},\"processParamItemList\":[],\"processType\":\"固化工艺1\",\"remark\":\"碳纤维固化参数组\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 13:38:21\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:38:21', 108);
INSERT INTO `sys_oper_log` VALUES (161, '工艺参数组', 2, 'com.ruoyi.web.controller.material.ProcessParamGroupController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamGroup', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"materialName\":\"碳纤维复合材料\",\"paramNumber\":\"P002\",\"params\":{},\"processParamItemList\":[],\"processType\":\"成型工艺1\",\"remark\":\"碳纤维成型参数组\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 13:38:29\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:38:29', 107);
INSERT INTO `sys_oper_log` VALUES (162, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/material_1753427050616_20250728150701A004.xlsx\",\"createTime\":\"2025-07-24 21:02:17\",\"groupId\":3,\"itemId\":7,\"paramName\":\"1516\",\"paramValue\":1.0,\"params\":{},\"updateTime\":\"2025-07-29 13:38:45\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:38:46', 1210);
INSERT INTO `sys_oper_log` VALUES (163, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/material_1753431455794_20250728141541A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 21:50:05\",\"groupId\":3,\"itemId\":13,\"paramName\":\"拉挤速度\",\"paramValue\":3.5,\"params\":{},\"remark\":\"拉挤线速度\",\"unit\":\"m/min\",\"updateTime\":\"2025-07-29 13:38:51\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 13:38:51', 131);
INSERT INTO `sys_oper_log` VALUES (164, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.exportComplete()', 'POST', 1, 'admin', '研发部门', '/material/material/exportComplete', '1********', '内网IP', '{\"pageSize\":\"50\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-29 14:16:06', 539);
INSERT INTO `sys_oper_log` VALUES (165, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/18', '1********', '内网IP', '[18]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:16:53', 207);
INSERT INTO `sys_oper_log` VALUES (166, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/17', '1********', '内网IP', '[17]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:16:56', 378);
INSERT INTO `sys_oper_log` VALUES (167, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/16', '1********', '内网IP', '[16]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:16:58', 53);
INSERT INTO `sys_oper_log` VALUES (168, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/15', '1********', '内网IP', '[15]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:17:01', 377);
INSERT INTO `sys_oper_log` VALUES (169, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/14', '1********', '内网IP', '[14]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:17:04', 45);
INSERT INTO `sys_oper_log` VALUES (170, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.exportComplete()', 'POST', 1, 'admin', '研发部门', '/material/material/exportComplete', '1********', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-29 14:20:43', 45);
INSERT INTO `sys_oper_log` VALUES (171, '测试方案', 5, 'com.ruoyi.web.controller.material.TestPlanController.export()', 'POST', 1, 'admin', '研发部门', '/material/testPlan/export', '1********', '内网IP', '{\"pageSize\":\"50\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-29 14:21:08', 74);
INSERT INTO `sys_oper_log` VALUES (172, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"sn220.pdf\",\"createBy\":\"material_lab\",\"createTime\":\"2025-07-29 14:12:42\",\"materialDescription\":\"高断裂韧性陶瓷\",\"materialId\":6,\"materialModel\":\"SN2201\",\"materialName\":\"氮化硅陶瓷\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":9,\"materialId\":6,\"paramNumber\":\"PARAM-006\",\"params\":{},\"processType\":\"工艺6\",\"updateTime\":\"2025-07-29 14:12:42\"}],\"remark\":\"轴承级精度\",\"supplierName\":\"东陶公司\",\"updateTime\":\"2025-07-29 14:23:14\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:23:14', 125);
INSERT INTO `sys_oper_log` VALUES (173, '工艺参数组', 2, 'com.ruoyi.web.controller.material.ProcessParamGroupController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamGroup', '1********', '内网IP', '{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":12,\"materialId\":1,\"materialName\":\"碳纤维复合材料\",\"paramNumber\":\"PARAM-0012\",\"params\":{},\"processParamItemList\":[{\"createBy\":\"operator\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":12,\"itemId\":31,\"paramName\":\"温度\",\"paramValue\":274.01,\"params\":{},\"unit\":\"℃\",\"updateTime\":\"2025-07-29 14:12:42\"}],\"processType\":\"烧结工艺\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 14:23:24\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:23:24', 78);
INSERT INTO `sys_oper_log` VALUES (174, '工艺参数明细', 2, 'com.ruoyi.web.controller.material.ProcessParamItemController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createBy\":\"operator\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":1,\"itemId\":17,\"paramName\":\"压力测试\",\"paramValue\":129.81,\"params\":{},\"unit\":\"MPa\",\"updateTime\":\"2025-07-29 14:23:31\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:23:32', 454);
INSERT INTO `sys_oper_log` VALUES (175, '材料信息', 1, 'com.ruoyi.web.controller.material.MaterialController.add()', 'POST', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createTime\":\"2025-07-29 14:23:56\",\"materialId\":24,\"materialName\":\"1\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:23:56', 124);
INSERT INTO `sys_oper_log` VALUES (176, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/24', '1********', '内网IP', '[24]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:24:14', 74);
INSERT INTO `sys_oper_log` VALUES (177, '工艺参数组', 1, 'com.ruoyi.web.controller.material.ProcessParamGroupController.add()', 'POST', 1, 'admin', '研发部门', '/material/processParamGroup', '1********', '内网IP', '{\"createTime\":\"2025-07-29 14:24:27\",\"groupId\":40,\"materialId\":1,\"paramNumber\":\"1\",\"params\":{},\"processType\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:24:27', 364);
INSERT INTO `sys_oper_log` VALUES (178, '工艺参数组', 3, 'com.ruoyi.web.controller.material.ProcessParamGroupController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/processParamGroup/40', '1********', '内网IP', '[40]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:24:32', 48);
INSERT INTO `sys_oper_log` VALUES (179, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, 'admin', '研发部门', '/material/processParamItem', '1********', '内网IP', '{\"createTime\":\"2025-07-29 14:24:39\",\"groupId\":1,\"itemId\":72,\"paramName\":\"1\",\"paramValue\":0.0,\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:24:39', 164);
INSERT INTO `sys_oper_log` VALUES (180, '工艺参数明细', 3, 'com.ruoyi.web.controller.material.ProcessParamItemController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/processParamItem/72', '1********', '内网IP', '[72]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:24:43', 67);
INSERT INTO `sys_oper_log` VALUES (181, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"aero_team\",\"createTime\":\"2025-07-29 14:12:42\",\"materialDescription\":\"耐高温500℃\",\"materialId\":11,\"materialModel\":\"Kapton-200\",\"materialName\":\"聚酰亚胺薄膜\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":13,\"materialId\":11,\"paramNumber\":\"PARAM-011\",\"params\":{},\"processType\":\"工艺11\",\"updateTime\":\"2025-07-29 14:12:42\"}],\"remark\":\"宇航级材料\",\"supplierName\":\"杜邦\",\"updateTime\":\"2025-07-29 14:26:45\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:26:46', 361);
INSERT INTO `sys_oper_log` VALUES (182, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"additive_mfg\",\"createTime\":\"2025-07-29 14:12:42\",\"materialDescription\":\"球形粉\",\"materialId\":13,\"materialModel\":\"Ti64-45μm\",\"materialName\":\"钛合金粉末\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":14,\"materialId\":13,\"paramNumber\":\"PARAM-013\",\"params\":{},\"processType\":\"工艺13\",\"updateTime\":\"2025-07-29 14:12:42\"}],\"remark\":\"3D打印用\",\"supplierName\":\"宝鸡钛业\",\"updateTime\":\"2025-07-29 14:26:59\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:26:59', 147);
INSERT INTO `sys_oper_log` VALUES (183, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12KB\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺1\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-29 13:38:21\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺1\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-29 13:38:29\"},{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":12,\"materialId\":1,\"paramNumber\":\"PARAM-0012\",\"params\":{},\"processType\":\"烧结工艺\",\"updateTime\":\"2025-07-29 14:23:24\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 14:27:10\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:27:10', 483);
INSERT INTO `sys_oper_log` VALUES (184, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/2025-07-28 11_05_15-导出_20250728150536A002.xlsx,http://localhost:8080/profile/upload/2025/07/29/test_plan_1753770068680_20250729142721A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12KB\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺1\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-29 13:38:21\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺1\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-29 13:38:29\"},{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":12,\"materialId\":1,\"paramNumber\":\"PARAM-0012\",\"params\":{},\"processType\":\"烧结工艺\",\"updateTime\":\"2025-07-29 14:23:24\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 14:27:23\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:27:23', 117);
INSERT INTO `sys_oper_log` VALUES (185, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/29/test_plan_1753770068680_20250729142721A002.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12KB\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺1\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-29 13:38:21\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺1\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-29 13:38:29\"},{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":12,\"materialId\":1,\"paramNumber\":\"PARAM-0012\",\"params\":{},\"processType\":\"烧结工艺\",\"updateTime\":\"2025-07-29 14:23:24\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 14:27:31\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:27:31', 239);
INSERT INTO `sys_oper_log` VALUES (186, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12KB\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺1\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-29 13:38:21\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺1\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-29 13:38:29\"},{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":12,\"materialId\":1,\"paramNumber\":\"PARAM-0012\",\"params\":{},\"processType\":\"烧结工艺\",\"updateTime\":\"2025-07-29 14:23:24\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 14:27:40\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:27:40', 120);
INSERT INTO `sys_oper_log` VALUES (187, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12KB\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺1\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-29 13:38:21\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺1\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-29 13:38:29\"},{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":12,\"materialId\":1,\"paramNumber\":\"PARAM-0012\",\"params\":{},\"processType\":\"烧结工艺\",\"updateTime\":\"2025-07-29 14:23:24\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateTime\":\"2025-07-29 14:29:46\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:29:46', 116);
INSERT INTO `sys_oper_log` VALUES (188, '材料信息', 5, 'com.ruoyi.web.controller.material.MaterialController.export()', 'POST', 1, 'admin', '研发部门', '/material/material/export', '1********', '内网IP', '{\"pageSize\":\"50\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-29 14:29:59', 37);
INSERT INTO `sys_oper_log` VALUES (189, '测试方案', 3, 'com.ruoyi.web.controller.material.TestPlanController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/testPlan/20,19', '1********', '内网IP', '[20,19]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:31:49', 168);
INSERT INTO `sys_oper_log` VALUES (190, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"createBy\":\"tester\",\"createTime\":\"2025-07-29 14:14:49\",\"params\":{},\"performanceName\":\"Salt Spray Test Plan\",\"performanceType\":\"Corrosion Resistance\",\"planCode\":\"TP-003\",\"remark\":\"——\",\"testEquipment\":\"Salt Spray Chamber\",\"testParameter\":\"Time to Corrosion1\",\"testPlanId\":18,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 14:32:23\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:32:23', 85);
INSERT INTO `sys_oper_log` VALUES (191, '测试方案', 1, 'com.ruoyi.web.controller.material.TestPlanController.add()', 'POST', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-29 14:32:33\",\"params\":{},\"performanceType\":\"1\",\"planCode\":\"1\",\"testPlanId\":26}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:32:33', 695);
INSERT INTO `sys_oper_log` VALUES (192, '测试方案', 3, 'com.ruoyi.web.controller.material.TestPlanController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/testPlan/26', '1********', '内网IP', '[26]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:32:41', 56);
INSERT INTO `sys_oper_log` VALUES (193, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/29/material_1753770599713_20250729143251A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 13:37:25\",\"params\":{},\"performanceName\":\"测试2\",\"performanceType\":\"热学性能\",\"planCode\":\"TP005\",\"testPlanId\":5,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 14:32:52\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:32:53', 138);
INSERT INTO `sys_oper_log` VALUES (194, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-29 13:37:25\",\"params\":{},\"performanceName\":\"测试2\",\"performanceType\":\"热学性能\",\"planCode\":\"TP005\",\"testPlanId\":5,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 14:33:01\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:33:02', 864);
INSERT INTO `sys_oper_log` VALUES (195, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-29 13:37:25\",\"params\":{},\"performanceName\":\"测试2\",\"performanceType\":\"热学性能\",\"planCode\":\"TP005\",\"testPlanId\":5,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 14:33:09\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:33:09', 153);
INSERT INTO `sys_oper_log` VALUES (196, '测试方案', 1, 'com.ruoyi.web.controller.material.TestPlanController.add()', 'POST', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/test_plan_1753687230997_20250728160325A001.xlsx,http://localhost:8080/profile/upload/2025/07/28/material_1753428687313_20250728162905A001.xlsx,http://localhost:8080/profile/upload/2025/07/29/test_result_1753762360927_20250729133551A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 14:33:45\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP001_副本\",\"remark\":\"标准拉伸测试\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"最大拉伸强度\",\"testPlanId\":27,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 13:35:54\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:33:46', 683);
INSERT INTO `sys_oper_log` VALUES (197, '测试方案', 3, 'com.ruoyi.web.controller.material.TestPlanController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/testPlan/5,4', '1********', '内网IP', '[5,4]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:34:06', 190);
INSERT INTO `sys_oper_log` VALUES (198, '测试方案', 5, 'com.ruoyi.web.controller.material.TestPlanController.export()', 'POST', 1, 'admin', '研发部门', '/material/testPlan/export', '1********', '内网IP', '{\"pageSize\":\"50\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-07-29 14:34:32', 30);
INSERT INTO `sys_oper_log` VALUES (199, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 14:37:35\",\"groupId\":5,\"params\":{},\"testPlanCode\":\"TP-CHEM01\",\"testValue\":0}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\IDEA Project\\RuoYi-Vue\\ruoyi-system\\target\\classes\\mapper\\system\\TestResultMapper.xml]\r\n### The error may involve com.ruoyi.system.mapper.TestResultMapper.insertTestResult-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into test_results          ( group_id,                          test_value,              attachments,                          create_by,             create_time )           values ( ?,                          ?,              ?,                          ?,             sysdate() )\r\n### Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\n; Field \'test_plan_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value', '2025-07-29 14:37:36', 851);
INSERT INTO `sys_oper_log` VALUES (200, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 14:40:01\",\"groupId\":5,\"params\":{},\"testPlanCode\":\"TP001_副本\",\"testValue\":1}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\IDEA Project\\RuoYi-Vue\\ruoyi-system\\target\\classes\\mapper\\system\\TestResultMapper.xml]\r\n### The error may involve com.ruoyi.system.mapper.TestResultMapper.insertTestResult-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into test_results          ( group_id,                          test_value,              attachments,                          create_by,             create_time )           values ( ?,                          ?,              ?,                          ?,             sysdate() )\r\n### Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\n; Field \'test_plan_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value', '2025-07-29 14:40:01', 3);
INSERT INTO `sys_oper_log` VALUES (201, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 14:40:23\",\"groupId\":5,\"params\":{},\"testPlanCode\":\"TP001_副本\",\"testValue\":1}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\IDEA Project\\RuoYi-Vue\\ruoyi-system\\target\\classes\\mapper\\system\\TestResultMapper.xml]\r\n### The error may involve com.ruoyi.system.mapper.TestResultMapper.insertTestResult-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into test_results          ( group_id,                          test_value,              attachments,                          create_by,             create_time )           values ( ?,                          ?,              ?,                          ?,             sysdate() )\r\n### Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\n; Field \'test_plan_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value', '2025-07-29 14:40:23', 3);
INSERT INTO `sys_oper_log` VALUES (202, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-29 14:12:42\",\"materialDescription\":\"高导热率陶瓷基板\",\"materialId\":4,\"materialModel\":\"A473\",\"materialName\":\"氧化铝陶瓷基板\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":7,\"materialId\":4,\"paramNumber\":\"PARAM-004\",\"params\":{},\"processType\":\"工艺4\",\"updateTime\":\"2025-07-29 14:12:42\"}],\"remark\":\"耐高温\",\"supplierName\":\"京瓷株式会社\",\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 14:56:49\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:56:49', 216);
INSERT INTO `sys_oper_log` VALUES (203, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-29 14:12:42\",\"materialDescription\":\"高导热率陶瓷基板\",\"materialId\":4,\"materialModel\":\"A473\",\"materialName\":\"氧化铝陶瓷基板\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":7,\"materialId\":4,\"paramNumber\":\"PARAM-004\",\"params\":{},\"processType\":\"工艺4\",\"updateTime\":\"2025-07-29 14:12:42\"}],\"remark\":\"耐高温\",\"supplierName\":\"京瓷株式会社\",\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 14:56:58\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:56:58', 177);
INSERT INTO `sys_oper_log` VALUES (204, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"materialDescription\":\"高强度碳纤维复合材料，适用于航空航天领域\",\"materialId\":1,\"materialModel\":\"T700-12KB\",\"materialName\":\"碳纤维复合材料\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/28/complete_data_1753428836123_20250728150624A003.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":1,\"materialId\":1,\"paramNumber\":\"P001\",\"params\":{},\"processType\":\"固化工艺1\",\"remark\":\"碳纤维固化参数组\",\"updateTime\":\"2025-07-29 13:38:21\"},{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"groupId\":2,\"materialId\":1,\"paramNumber\":\"P002\",\"params\":{},\"processType\":\"成型工艺1\",\"remark\":\"碳纤维成型参数组\",\"updateTime\":\"2025-07-29 13:38:29\"},{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":12,\"materialId\":1,\"paramNumber\":\"PARAM-0012\",\"params\":{},\"processType\":\"烧结工艺\",\"updateTime\":\"2025-07-29 14:23:24\"}],\"remark\":\"测试材料\",\"supplierName\":\"东丽公司\",\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 14:57:09\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:57:10', 56);
INSERT INTO `sys_oper_log` VALUES (205, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-29 14:12:42\",\"materialDescription\":\"高导热率陶瓷基板\",\"materialId\":4,\"materialModel\":\"A473\",\"materialName\":\"氧化铝陶瓷基板\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":7,\"materialId\":4,\"paramNumber\":\"PARAM-004\",\"params\":{},\"processType\":\"工艺4\",\"updateTime\":\"2025-07-29 14:12:42\"}],\"remark\":\"耐高温\",\"supplierName\":\"京瓷株式会社\",\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 14:58:25\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 14:58:26', 591);
INSERT INTO `sys_oper_log` VALUES (206, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/19,13', '1********', '内网IP', '[19,13]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:00:16', 209);
INSERT INTO `sys_oper_log` VALUES (207, '工艺参数组', 3, 'com.ruoyi.web.controller.material.ProcessParamGroupController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/processParamGroup/33,32', '1********', '内网IP', '[33,32]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:01:03', 122);
INSERT INTO `sys_oper_log` VALUES (208, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/20', '1********', '内网IP', '[20]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:01:09', 59);
INSERT INTO `sys_oper_log` VALUES (209, '材料信息', 2, 'com.ruoyi.web.controller.material.MaterialController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"tech_user\",\"createTime\":\"2025-07-29 14:12:42\",\"materialDescription\":\"PCB基材\",\"materialId\":5,\"materialModel\":\"S117G\",\"materialName\":\"FR-4环氧玻纤板\",\"params\":{},\"processParamGroupList\":[{\"attachments\":\"proc_guide.pdf\",\"createBy\":\"process_eng\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":5,\"materialId\":5,\"paramNumber\":\"PARAM-005\",\"params\":{},\"processType\":\"工艺5\",\"updateTime\":\"2025-07-29 14:12:42\"}],\"remark\":\"阻燃94V-0\",\"supplierName\":\"生益科技\",\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 15:01:24\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:01:25', 89);
INSERT INTO `sys_oper_log` VALUES (210, '材料信息', 1, 'com.ruoyi.web.controller.material.MaterialController.add()', 'POST', 1, 'admin', '研发部门', '/material/material', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-29 15:01:36\",\"materialId\":25,\"materialName\":\"1\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:01:36', 59);
INSERT INTO `sys_oper_log` VALUES (211, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/material/25', '1********', '内网IP', '[25]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:01:43', 131);
INSERT INTO `sys_oper_log` VALUES (212, '测试方案', 3, 'com.ruoyi.web.controller.material.TestPlanController.remove()', 'DELETE', 1, 'admin', '研发部门', '/material/testPlan/11,10', '1********', '内网IP', '[11,10]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:02:09', 99);
INSERT INTO `sys_oper_log` VALUES (213, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testPlan', '1********', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:50\",\"params\":{},\"performanceName\":\"弯曲强度测试1\",\"performanceType\":\"力学性能\",\"planCode\":\"TP002\",\"remark\":\"三点弯曲测试\",\"testEquipment\":\"三点弯曲试验机\",\"testParameter\":\"弯曲强度\",\"testPlanId\":2,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 15:02:20\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:02:20', 129);
INSERT INTO `sys_oper_log` VALUES (214, '测试结果', 2, 'com.ruoyi.web.controller.material.TestResultController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"tester\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":10,\"materialModel\":\"E-Glass-2400\",\"materialName\":\"玻璃纤维\",\"paramNumber\":\"PARAM-002\",\"params\":{},\"performanceName\":\"导热系数\",\"performanceType\":\"热学性能\",\"planCode\":\"TP-THERM02\",\"processType\":\"激光切割\",\"supplierDatasheetVal\":\"101.9 MPa\",\"supplierName\":\"巨石集团\",\"testEquipment\":\"Netzsch LFA 467\",\"testParameter\":\"激光闪射法\",\"testPlanCode\":\"TP-THERM02\",\"testPlanId\":8,\"testResultId\":75,\"testValue\":111.42,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 15:03:55\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:03:55', 142);
INSERT INTO `sys_oper_log` VALUES (215, '测试结果', 2, 'com.ruoyi.web.controller.material.TestResultController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"tester\",\"createTime\":\"2025-07-29 14:12:42\",\"groupId\":8,\"materialModel\":\"Zirco-3Y\",\"materialName\":\"氧化锆陶瓷\",\"paramNumber\":\"PARAM-010\",\"params\":{},\"performanceName\":\"导热系数\",\"performanceType\":\"热学性能\",\"planCode\":\"TP-THERM02\",\"processType\":\"工艺10\",\"supplierDatasheetVal\":\"135.6 MPa\",\"supplierName\":\"圣戈班\",\"testEquipment\":\"Netzsch LFA 467\",\"testParameter\":\"激光闪射法\",\"testPlanCode\":\"TP-THERM02\",\"testPlanId\":8,\"testResultId\":77,\"testValue\":85.92,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 15:04:05\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:04:05', 167);
INSERT INTO `sys_oper_log` VALUES (216, '测试结果', 2, 'com.ruoyi.web.controller.material.TestResultController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 13:34:55\",\"groupId\":3,\"materialModel\":\"E-Glass-2400\",\"materialName\":\"玻璃纤维\",\"paramNumber\":\"P003\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP001\",\"processType\":\"拉挤工艺\",\"supplierDatasheetVal\":\"50\",\"supplierName\":\"巨石集团\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"最大拉伸强度\",\"testPlanCode\":\"TP001\",\"testPlanId\":1,\"testResultId\":11,\"testValue\":6,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 15:04:20\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:04:20', 75);
INSERT INTO `sys_oper_log` VALUES (217, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/29/test_plan_1753770871768_20250729150527A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 15:05:29\",\"groupId\":10,\"params\":{},\"testPlanCode\":\"TP-CHEM01\",\"testPlanId\":12,\"testResultId\":154,\"testValue\":4}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:05:29', 104);
INSERT INTO `sys_oper_log` VALUES (218, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 15:06:08\",\"groupId\":11,\"params\":{},\"supplierDatasheetVal\":\"45\",\"testPlanCode\":\"TP001_副本\",\"testValue\":7}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\IDEA Project\\RuoYi-Vue\\ruoyi-system\\target\\classes\\mapper\\system\\TestResultMapper.xml]\r\n### The error may involve com.ruoyi.system.mapper.TestResultMapper.insertTestResult-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into test_results          ( group_id,             supplier_datasheet_val,             test_value,              attachments,                          create_by,             create_time )           values ( ?,             ?,             ?,              ?,                          ?,             sysdate() )\r\n### Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\n; Field \'test_plan_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value', '2025-07-29 15:06:08', 12);
INSERT INTO `sys_oper_log` VALUES (219, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 15:06:25\",\"groupId\":11,\"params\":{},\"supplierDatasheetVal\":\"45\",\"testPlanCode\":\"TP-THERM02\",\"testPlanId\":8,\"testResultId\":155,\"testValue\":7}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:06:26', 798);
INSERT INTO `sys_oper_log` VALUES (220, '测试结果', 2, 'com.ruoyi.web.controller.material.TestResultController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/29/test_plan_1753770871768_20250729150527A001.xlsx\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 15:05:29\",\"groupId\":10,\"materialModel\":\"E-Glass-2400\",\"materialName\":\"玻璃纤维\",\"paramNumber\":\"PARAM-002\",\"params\":{},\"performanceName\":\"耐酸碱测试\",\"performanceType\":\"化学性能\",\"planCode\":\"TP-CHEM01\",\"processType\":\"激光切割\",\"supplierDatasheetVal\":\"56\",\"supplierName\":\"巨石集团\",\"testEquipment\":\"定制反应釜\",\"testParameter\":\"98%H₂SO₄浸泡\",\"testPlanCode\":\"TP-CHEM01\",\"testPlanId\":12,\"testResultId\":154,\"testValue\":4,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-29 15:06:38\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:06:38', 51);
INSERT INTO `sys_oper_log` VALUES (221, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 15:06:48\",\"groupId\":5,\"params\":{},\"testPlanCode\":\"TP-ADHE01\",\"testPlanId\":14,\"testResultId\":156,\"testValue\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 15:06:48', 340);
INSERT INTO `sys_oper_log` VALUES (222, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"\",\"createBy\":\"admin\",\"createTime\":\"2025-07-29 15:11:03\",\"groupId\":6,\"params\":{},\"testPlanCode\":\"TP\",\"testValue\":0}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\IDEA Project\\RuoYi-Vue\\ruoyi-system\\target\\classes\\mapper\\system\\TestResultMapper.xml]\r\n### The error may involve com.ruoyi.system.mapper.TestResultMapper.insertTestResult-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into test_results          ( group_id,                          test_value,              attachments,                          create_by,             create_time )           values ( ?,                          ?,              ?,                          ?,             sysdate() )\r\n### Cause: java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value\n; Field \'test_plan_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'test_plan_id\' doesn\'t have a default value', '2025-07-29 15:11:03', 4);
INSERT INTO `sys_oper_log` VALUES (223, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '1********', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-24 14:57:09\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2002,2018,2019,2020,2021,2022,2003,2023,2024,2025,2026,2027,2004],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 17:30:52', 788);
INSERT INTO `sys_oper_log` VALUES (224, '用户管理', 1, 'com.ruoyi.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', '研发部门', '/system/user', '1********', '内网IP', '{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"SN122507\",\"params\":{},\"postIds\":[],\"roleIds\":[2],\"status\":\"0\",\"userId\":100,\"userName\":\"吴红姣\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 17:32:05', 236);
INSERT INTO `sys_oper_log` VALUES (225, '材料信息', 1, 'com.ruoyi.web.controller.material.MaterialController.add()', 'POST', 1, '吴红姣', NULL, '/material/material', '1********', '内网IP', '{\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-29 17:34:40\",\"materialId\":6,\"materialName\":\"1\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 17:34:40', 231);
INSERT INTO `sys_oper_log` VALUES (226, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2005,\"menuName\":\"材料信息查询\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"material:material:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 17:36:50', 160);
INSERT INTO `sys_oper_log` VALUES (227, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2010,\"menuName\":\"工艺参数组查询\",\"menuType\":\"F\",\"orderNum\":6,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"material:processParamGroup:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 17:37:34', 398);
INSERT INTO `sys_oper_log` VALUES (228, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2014,\"menuName\":\"工艺参数明细查询\",\"menuType\":\"F\",\"orderNum\":10,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"material:processParamItem:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 17:38:00', 628);
INSERT INTO `sys_oper_log` VALUES (229, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, '吴红姣', NULL, '/material/material/6', '1********', '内网IP', '[6]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-29 17:38:42', 373);
INSERT INTO `sys_oper_log` VALUES (230, '参数管理', 2, 'com.ruoyi.web.controller.system.SysConfigController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/config', '1********', '内网IP', '{\"configId\":4,\"configKey\":\"sys.account.captchaEnabled\",\"configName\":\"账号自助-验证码开关\",\"configType\":\"Y\",\"configValue\":\"false\",\"createBy\":\"admin\",\"createTime\":\"2025-07-24 14:57:16\",\"params\":{},\"remark\":\"是否开启验证码功能（true开启，false关闭）\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 09:55:09', 471);
INSERT INTO `sys_oper_log` VALUES (231, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-24 14:57:10\",\"icon\":\"guide\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":4,\"menuName\":\"材料官网\",\"menuType\":\"M\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"http://ruoyi.vip\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 09:56:00', 160);
INSERT INTO `sys_oper_log` VALUES (232, '测试结果', 2, 'com.ruoyi.web.controller.material.TestResultController.edit()', 'PUT', 1, 'admin', '研发部门', '/material/testResult', '1********', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/30/2025-07-30 10_05_07-导出_20250730101659A001.xlsx\",\"createBy\":\"system\",\"createTime\":\"2025-07-29 11:45:00\",\"groupId\":3,\"materialModel\":\"M-B1\",\"materialName\":\"材料B\",\"paramNumber\":\"P003\",\"params\":{},\"performanceName\":\"拉伸强度测试\",\"performanceType\":\"力学性能\",\"planCode\":\"TP-001\",\"processType\":\"压铸\",\"remark\":\"批次4\",\"supplierDatasheetVal\":\"60\",\"supplierName\":\"供应商Y\",\"testEquipment\":\"万能试验机\",\"testParameter\":\"拉伸强度\",\"testPlanId\":1,\"testResultId\":12,\"testValue\":58.7,\"updateBy\":\"admin\",\"updateTime\":\"2025-07-30 10:17:03\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 10:17:03', 457);
INSERT INTO `sys_oper_log` VALUES (233, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"material/config/index\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"tree\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"材料及参数配置\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"config\",\"perms\":\"material:config:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:13:39', 169);
INSERT INTO `sys_oper_log` VALUES (234, '材料信息', 1, 'com.ruoyi.web.controller.material.MaterialController.add()', 'POST', 1, '吴红姣', NULL, '/material/material', '*************', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/30/【78-03-040】AUS products Roadmap_20250730161344A002.pdf\",\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 16:13:46\",\"materialDescription\":\"感光型 阻焊 绿色 亮光 无卤油墨 PSR4000 AUS308 1KG/组（HF）\",\"materialId\":7,\"materialModel\":\"AUS 308\",\"materialName\":\"油墨\",\"params\":{},\"supplierName\":\"Taiyo\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:13:47', 739);
INSERT INTO `sys_oper_log` VALUES (235, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2005,\"menuName\":\"材料信息查询\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"material:material:query\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:13:50', 83);
INSERT INTO `sys_oper_log` VALUES (236, '材料信息', 1, 'com.ruoyi.web.controller.material.MaterialController.add()', 'POST', 1, '吴红姣', NULL, '/material/material', '*************', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/30/【78-03-040】AUS products Roadmap_20250730161441A003.pdf\",\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 16:15:47\",\"materialDescription\":\"感光型 阻焊 绿色 亮光 无卤油墨 PSR4000 AUS308 1KG/组（HF）\",\"materialId\":8,\"materialModel\":\"PSR4000 AUS308\",\"materialName\":\"油墨\",\"params\":{},\"supplierName\":\"香港太阳油墨有限公司\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:15:47', 92);
INSERT INTO `sys_oper_log` VALUES (237, '材料信息', 3, 'com.ruoyi.web.controller.material.MaterialController.remove()', 'DELETE', 1, '吴红姣', NULL, '/material/material/7', '*************', '内网IP', '[7]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:16:06', 220);
INSERT INTO `sys_oper_log` VALUES (238, '工艺参数组', 1, 'com.ruoyi.web.controller.material.ProcessParamGroupController.add()', 'POST', 1, '吴红姣', NULL, '/material/processParamGroup', '*************', '内网IP', '{\"attachments\":\"http://localhost:8080/profile/upload/2025/07/30/AUS308油墨加工工艺_20250730161725A004.xlsx\",\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 16:17:26\",\"groupId\":9,\"materialId\":1,\"paramNumber\":\"1-1-1-1\",\"params\":{},\"processType\":\"滚涂+压平\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:17:27', 926);
INSERT INTO `sys_oper_log` VALUES (239, '测试方案', 1, 'com.ruoyi.web.controller.material.TestPlanController.add()', 'POST', 1, '吴红姣', NULL, '/material/testPlan', '*************', '内网IP', '{\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 16:19:08\",\"params\":{},\"performanceName\":\"油墨固化度\",\"performanceType\":\"力学性能\",\"planCode\":\"YM-01\",\"testEquipment\":\"FTIR\",\"testParameter\":\"测试方式：ATR\",\"testPlanId\":4}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:19:08', 252);
INSERT INTO `sys_oper_log` VALUES (240, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"material/config/index\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"tree\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"材料及参数配置\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"config\",\"perms\":\"material:material:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:24:07', 111);
INSERT INTO `sys_oper_log` VALUES (241, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2010,\"menuName\":\"工艺参数组查询\",\"menuType\":\"F\",\"orderNum\":6,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"material:processParamGroup:query\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:28:24', 103);
INSERT INTO `sys_oper_log` VALUES (242, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2010,\"menuName\":\"工艺参数组查询\",\"menuType\":\"F\",\"orderNum\":6,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"material:processParamGroup:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:28:54', 383);
INSERT INTO `sys_oper_log` VALUES (243, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"工艺参数组查询\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":2001,\"path\":\"material:processParamGroup:query\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"新增菜单\'工艺参数组查询\'失败，菜单名称已存在\",\"code\":500}', 0, NULL, '2025-07-30 16:29:43', 23);
INSERT INTO `sys_oper_log` VALUES (244, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"工艺参数组查询1\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":2001,\"path\":\"material:processParamGroup:query\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:29:49', 125);
INSERT INTO `sys_oper_log` VALUES (245, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2010,\"menuName\":\"工艺参数组列表\",\"menuType\":\"F\",\"orderNum\":6,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"material:processParamGroup:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:30:11', 92);
INSERT INTO `sys_oper_log` VALUES (246, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-30 16:29:49\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2028,\"menuName\":\"工艺参数组查询\",\"menuType\":\"F\",\"orderNum\":6,\"params\":{},\"parentId\":2001,\"path\":\"material:processParamGroup:query\",\"perms\":\"material:processParamGroup:query\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:30:45', 97);
INSERT INTO `sys_oper_log` VALUES (247, '测试方案', 2, 'com.ruoyi.web.controller.material.TestPlanController.edit()', 'PUT', 1, '吴红姣', NULL, '/material/testPlan', '*************', '内网IP', '{\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 16:19:08\",\"params\":{},\"performanceName\":\"油墨固化度\",\"performanceType\":\"力学性能\",\"planCode\":\"YM-01\",\"testEquipment\":\"FTIR\",\"testParameter\":\"测试方式：ATR\",\"testPlanId\":4,\"updateBy\":\"吴红姣\",\"updateTime\":\"2025-07-30 16:33:01\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:33:01', 248);
INSERT INTO `sys_oper_log` VALUES (248, '工艺参数组', 1, 'com.ruoyi.web.controller.material.ProcessParamGroupController.add()', 'POST', 1, '吴红姣', NULL, '/material/processParamGroup', '*************', '内网IP', '{\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 16:35:04\",\"groupId\":10,\"materialId\":8,\"paramNumber\":\"1-1-1\",\"params\":{},\"processType\":\"滚涂+压平\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:35:04', 66);
INSERT INTO `sys_oper_log` VALUES (249, '个人信息', 2, 'com.ruoyi.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile/updatePwd', '1********', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:36:32', 299);
INSERT INTO `sys_oper_log` VALUES (250, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, '吴红姣', NULL, '/material/processParamItem', '1********', '内网IP', '{\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 16:37:19\",\"groupId\":9,\"itemId\":15,\"paramName\":\"1\",\"paramValue\":0.0,\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:37:19', 138);
INSERT INTO `sys_oper_log` VALUES (251, '工艺参数明细', 3, 'com.ruoyi.web.controller.material.ProcessParamItemController.remove()', 'DELETE', 1, '吴红姣', NULL, '/material/processParamItem/15', '1********', '内网IP', '[15]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:37:24', 106);
INSERT INTO `sys_oper_log` VALUES (252, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-24 14:59:04\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2014,\"menuName\":\"工艺参数明细列表\",\"menuType\":\"F\",\"orderNum\":10,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"material:processParamItem:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:38:42', 79);
INSERT INTO `sys_oper_log` VALUES (253, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', '研发部门', '/system/menu', '1********', '内网IP', '{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"工艺参数明细查询\",\"menuType\":\"F\",\"orderNum\":10,\"params\":{},\"parentId\":2001,\"perms\":\"material:processParamItem:query\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:39:17', 53);
INSERT INTO `sys_oper_log` VALUES (254, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '1********', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-24 14:57:09\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2005,2006,2007,2008,2009,2010,2028,2011,2012,2013,2014,2029,2015,2016,2017,2002,2018,2019,2020,2021,2022,2003,2023,2024,2025,2026,2027,2004],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 16:39:44', 218);
INSERT INTO `sys_oper_log` VALUES (255, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, '吴红姣', NULL, '/material/processParamItem', '*************', '内网IP', '{\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 17:27:57\",\"groupId\":10,\"itemId\":16,\"paramName\":\"曝光能量\",\"paramValue\":100.0,\"params\":{},\"unit\":\"J\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 17:27:58', 503);
INSERT INTO `sys_oper_log` VALUES (256, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, '吴红姣', NULL, '/material/processParamItem', '*************', '内网IP', '{\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 17:28:21\",\"groupId\":10,\"itemId\":17,\"paramName\":\"曝光能量\",\"paramValue\":100.0,\"params\":{},\"unit\":\"J\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 17:28:21', 113);
INSERT INTO `sys_oper_log` VALUES (257, '工艺参数明细', 1, 'com.ruoyi.web.controller.material.ProcessParamItemController.add()', 'POST', 1, '吴红姣', NULL, '/material/processParamItem', '*************', '内网IP', '{\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 17:30:08\",\"groupId\":10,\"itemId\":18,\"paramName\":\"曝光能量\",\"paramValue\":80.0,\"params\":{},\"unit\":\"焦耳\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 17:30:08', 181);
INSERT INTO `sys_oper_log` VALUES (258, '测试结果', 1, 'com.ruoyi.web.controller.material.TestResultController.add()', 'POST', 1, '吴红姣', NULL, '/material/testResult', '*************', '内网IP', '{\"attachments\":\"\",\"createBy\":\"吴红姣\",\"createTime\":\"2025-07-30 17:48:01\",\"groupId\":9,\"params\":{},\"supplierDatasheetVal\":\"150\",\"testPlanId\":4,\"testResultId\":37,\"testValue\":160}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-30 17:48:02', 641);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2025-07-24 14:57:08', '', NULL, '');
INSERT INTO `sys_post` VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2025-07-24 14:57:08', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2025-07-24 14:57:08', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2025-07-24 14:57:08', '', NULL, '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2025-07-24 14:57:09', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2025-07-24 14:57:09', 'admin', '2025-07-30 16:39:44', '普通角色');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (2, 100);
INSERT INTO `sys_role_dept` VALUES (2, 101);
INSERT INTO `sys_role_dept` VALUES (2, 105);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (2, 2000);
INSERT INTO `sys_role_menu` VALUES (2, 2001);
INSERT INTO `sys_role_menu` VALUES (2, 2002);
INSERT INTO `sys_role_menu` VALUES (2, 2003);
INSERT INTO `sys_role_menu` VALUES (2, 2004);
INSERT INTO `sys_role_menu` VALUES (2, 2005);
INSERT INTO `sys_role_menu` VALUES (2, 2006);
INSERT INTO `sys_role_menu` VALUES (2, 2007);
INSERT INTO `sys_role_menu` VALUES (2, 2008);
INSERT INTO `sys_role_menu` VALUES (2, 2009);
INSERT INTO `sys_role_menu` VALUES (2, 2010);
INSERT INTO `sys_role_menu` VALUES (2, 2011);
INSERT INTO `sys_role_menu` VALUES (2, 2012);
INSERT INTO `sys_role_menu` VALUES (2, 2013);
INSERT INTO `sys_role_menu` VALUES (2, 2014);
INSERT INTO `sys_role_menu` VALUES (2, 2015);
INSERT INTO `sys_role_menu` VALUES (2, 2016);
INSERT INTO `sys_role_menu` VALUES (2, 2017);
INSERT INTO `sys_role_menu` VALUES (2, 2018);
INSERT INTO `sys_role_menu` VALUES (2, 2019);
INSERT INTO `sys_role_menu` VALUES (2, 2020);
INSERT INTO `sys_role_menu` VALUES (2, 2021);
INSERT INTO `sys_role_menu` VALUES (2, 2022);
INSERT INTO `sys_role_menu` VALUES (2, 2023);
INSERT INTO `sys_role_menu` VALUES (2, 2024);
INSERT INTO `sys_role_menu` VALUES (2, 2025);
INSERT INTO `sys_role_menu` VALUES (2, 2026);
INSERT INTO `sys_role_menu` VALUES (2, 2027);
INSERT INTO `sys_role_menu` VALUES (2, 2028);
INSERT INTO `sys_role_menu` VALUES (2, 2029);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime NULL DEFAULT NULL COMMENT '密码最后更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 103, 'admin', '若依', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$O323BtuVKhFwioaKy0lYmuwF6wG6khcYxajSnzvv1EkfMykHsd3x2', '0', '0', '1********', '2025-08-01 17:27:24', '2025-07-30 16:36:32', 'admin', '2025-07-24 14:57:08', '', '2025-08-01 17:27:23', '管理员');
INSERT INTO `sys_user` VALUES (2, 105, 'ry', '若依', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '1********', '2025-07-24 14:57:08', '2025-07-24 14:57:08', 'admin', '2025-07-24 14:57:08', '', NULL, '测试员');
INSERT INTO `sys_user` VALUES (100, NULL, '吴红姣', 'SN122507', '00', '', '', '0', '', '$2a$10$9Jt3nFXtn.Vfix6uw4YzRulQG2ak8t2wpwaat.D9itYAUqRLRAhrC', '0', '0', '*************', '2025-08-01 15:23:09', NULL, 'admin', '2025-07-29 17:32:05', '', '2025-08-01 15:23:09', NULL);

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);
INSERT INTO `sys_user_post` VALUES (2, 2);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 2);
INSERT INTO `sys_user_role` VALUES (100, 2);

-- ----------------------------
-- Table structure for test_param_item
-- ----------------------------
DROP TABLE IF EXISTS `test_param_item`;
CREATE TABLE `test_param_item`  (
  `test_param_id` int NOT NULL AUTO_INCREMENT COMMENT '测试参数ID（主键）',
  `plan_group_id` int NOT NULL COMMENT '所属测试方案组ID',
  `param_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '测试参数名称',
  `param_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '测试参数数值（字符串格式）',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数单位',
  `attachments` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_param_id`) USING BTREE,
  INDEX `idx_plan_group_id`(`plan_group_id` ASC) USING BTREE,
  CONSTRAINT `fk_test_param_group` FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '测试参数明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of test_param_item
-- ----------------------------
INSERT INTO `test_param_item` VALUES (1, 1, '试样长度', '250', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (2, 1, '试样宽度', '25', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (3, 1, '拉伸速度', '2', 'mm/min', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (4, 2, '试样长度', '250', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (5, 2, '试样宽度', '25', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (6, 2, '拉伸速度', '2', 'mm/min', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (7, 3, '试样长度', '250', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (8, 3, '试样宽度', '25', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (9, 3, '拉伸速度', '2', 'mm/min', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (10, 7, '盐雾浓度', '5', '%', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (11, 7, '测试时间', '168', 'h', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (12, 4, '试样尺寸', '25x25', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (13, 4, '温度范围', '25-200', '°C', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (14, 4, '升温速率', '5', '°C/min', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (15, 5, '试样尺寸', '25x25', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (16, 5, '温度范围', '25-200', '°C', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (17, 5, '升温速率', '5', '°C/min', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (18, 8, '试样体积', '1', 'cm³', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (19, 8, '测试温度', '23', '°C', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (20, 6, '试样厚度', '1', 'mm', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (21, 6, '测试频率', '1000', 'Hz', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');
INSERT INTO `test_param_item` VALUES (22, 6, '测试电压', '1', 'V', NULL, NULL, 'admin', '2025-08-01 13:28:05', NULL, '2025-08-01 13:28:05');

-- ----------------------------
-- Table structure for test_plan_group
-- ----------------------------
DROP TABLE IF EXISTS `test_plan_group`;
CREATE TABLE `test_plan_group`  (
  `plan_group_id` int NOT NULL AUTO_INCREMENT COMMENT '测试方案组ID（主键）',
  `plan_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '方案编号',
  `performance_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性能类型',
  `performance_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '方案/性能名称',
  `test_equipment` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '测试设备',
  `attachments` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_group_id`) USING BTREE,
  UNIQUE INDEX `uk_plan_code`(`plan_code` ASC) USING BTREE,
  INDEX `idx_performance_type`(`performance_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '测试方案组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of test_plan_group
-- ----------------------------
INSERT INTO `test_plan_group` VALUES (1, 'TP001', '力学性能', '拉伸强度测试', 'Instron 5985', NULL, NULL, 'admin', '2025-08-01 13:28:04', NULL, '2025-08-01 13:28:04');
INSERT INTO `test_plan_group` VALUES (2, 'TP002', '力学性能', '弯曲强度测试', 'Instron 5985', NULL, NULL, 'admin', '2025-08-01 13:28:04', NULL, '2025-08-01 13:28:04');
INSERT INTO `test_plan_group` VALUES (3, 'TP003', '力学性能', '冲击韧性测试', 'Charpy冲击试验机', NULL, NULL, 'admin', '2025-08-01 13:28:04', NULL, '2025-08-01 13:28:04');
INSERT INTO `test_plan_group` VALUES (4, 'TP004', '热学性能', '热膨胀系数测试', 'TMA热机械分析仪', NULL, NULL, 'admin', '2025-08-01 13:28:04', NULL, '2025-08-01 13:28:04');
INSERT INTO `test_plan_group` VALUES (5, 'TP005', '热学性能', '导热系数测试', '激光导热仪', NULL, NULL, 'admin', '2025-08-01 13:28:04', NULL, '2025-08-01 13:28:04');
INSERT INTO `test_plan_group` VALUES (6, 'TP006', '电学性能', '介电常数测试', '阻抗分析仪', NULL, NULL, 'admin', '2025-08-01 13:28:04', NULL, '2025-08-01 13:28:04');
INSERT INTO `test_plan_group` VALUES (7, 'TP007', '化学性能', '耐腐蚀性测试', '盐雾试验箱', NULL, NULL, 'admin', '2025-08-01 13:28:04', NULL, '2025-08-01 13:28:04');
INSERT INTO `test_plan_group` VALUES (8, 'TP008', '物理性能', '密度测试', '密度计', NULL, NULL, 'admin', '2025-08-01 13:28:04', NULL, '2025-08-01 13:28:04');

-- ----------------------------
-- Table structure for test_results
-- ----------------------------
DROP TABLE IF EXISTS `test_results`;
CREATE TABLE `test_results`  (
  `test_result_id` int NOT NULL AUTO_INCREMENT COMMENT '测试结果ID（主键）',
  `plan_group_id` int NOT NULL COMMENT '测试方案组ID',
  `test_param_id` bigint NULL DEFAULT NULL COMMENT '测试参数明细ID',
  `group_id` int NOT NULL COMMENT '工艺参数组ID',
  `supplier_datasheet_val` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商Datasheet值',
  `test_value` decimal(20, 6) NULL DEFAULT NULL COMMENT '实际测试值',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位',
  `attachments` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)，含原始测试数据',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`test_result_id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_group_id` ASC) USING BTREE,
  INDEX `idx_group_id2`(`group_id` ASC) USING BTREE,
  CONSTRAINT `fk_result_param_group` FOREIGN KEY (`group_id`) REFERENCES `process_param_group` (`group_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_result_plan_group` FOREIGN KEY (`plan_group_id`) REFERENCES `test_plan_group` (`plan_group_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '测试结果表（关联测试方案与工艺参数组）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of test_results
-- ----------------------------

-- ----------------------------
-- Table structure for test_results_backup
-- ----------------------------
DROP TABLE IF EXISTS `test_results_backup`;
CREATE TABLE `test_results_backup`  (
  `test_result_id` int NOT NULL DEFAULT 0 COMMENT '测试结果ID（主键）',
  `plan_group_id` int NOT NULL COMMENT '测试方案组ID',
  `group_id` int NOT NULL COMMENT '工艺参数组ID',
  `supplier_datasheet_val` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商Datasheet值',
  `test_value` decimal(20, 6) NULL DEFAULT NULL COMMENT '实际测试值',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位',
  `attachments` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL(多个用逗号分隔)，含原始测试数据',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of test_results_backup
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
