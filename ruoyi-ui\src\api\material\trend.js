import request from '@/utils/request'

// 获取趋势对比数据
// 查询趋势对比数据
export function getTrendData(query) {
  return request({
    url: '/material/testResult/trendList',
    method: 'get',
    params: query
  })
}

// 获取对比选项数据
export function getCompareOptions() {
  return request({
    url: '/material/testResult/options',
    method: 'get'
  })
}

// 获取参数详情信息
export function getParamDetails(paramNumbers) {
  return request({
    url: '/material/trend/paramDetails',
    method: 'post',
    data: { paramNumbers }
  })
}

// 获取统计分析数据
export function getStatisticsData(query) {
  return request({
    url: '/material/trend/statistics',
    method: 'get',
    params: query
  })
}

// 导出趋势分析报告
export function exportTrendReport(query) {
  return request({
    url: '/material/trend/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}