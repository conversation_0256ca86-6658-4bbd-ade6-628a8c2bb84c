import request from '@/utils/request'

// 查询测试参数明细列表
export function listTestParamItem(query) {
  return request({
    url: '/material/testParamItem/list',
    method: 'get',
    params: query
  })
}

// 根据测试方案组ID查询测试参数明细列表
export function listByPlanGroupId(planGroupId) {
  return request({
    url: '/material/testParamItem/listByPlanGroupId/' + planGroupId,
    method: 'get'
  })
}

// 查询测试参数明细详细
export function getTestParamItem(testParamId) {
  return request({
    url: '/material/testParamItem/' + testParamId,
    method: 'get'
  })
}

// 新增测试参数明细
export function addTestParamItem(data) {
  return request({
    url: '/material/testParamItem',
    method: 'post',
    data: data
  })
}

// 修改测试参数明细
export function updateTestParamItem(data) {
  return request({
    url: '/material/testParamItem',
    method: 'put',
    data: data
  })
}

// 删除测试参数明细
export function delTestParamItem(testParamId) {
  return request({
    url: '/material/testParamItem/' + testParamId,
    method: 'delete'
  })
}

// 导出测试参数明细
export function exportTestParamItem(query) {
  return request({
    url: '/material/testParamItem/export',
    method: 'get',
    params: query
  })
}

// 获取测试参数明细选项（用于下拉选择）
export function getTestParamItemOptions(query) {
  return request({
    url: '/material/testParamItem/options',
    method: 'get',
    params: query
  })
}
