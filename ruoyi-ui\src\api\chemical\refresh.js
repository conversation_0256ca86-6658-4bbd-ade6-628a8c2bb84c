import request from '@/utils/request'

// 查询化学数据刷新任务列表
export function listRefreshTask(query) {
  return request({
    url: '/chemical/refresh/list',
    method: 'get',
    params: query
  })
}

// 查询化学数据刷新任务详细
export function getRefreshTask(refreshId) {
  return request({
    url: '/chemical/refresh/' + refreshId,
    method: 'get'
  })
}

// 新增化学数据刷新任务
export function addRefreshTask(data) {
  return request({
    url: '/chemical/refresh',
    method: 'post',
    data: data
  })
}

// 修改化学数据刷新任务
export function updateRefreshTask(data) {
  return request({
    url: '/chemical/refresh',
    method: 'put',
    data: data
  })
}

// 删除化学数据刷新任务
export function delRefreshTask(refreshIds) {
  return request({
    url: '/chemical/refresh/' + refreshIds,
    method: 'delete'
  })
}

// 执行数据刷新任务
export function executeRefresh(data) {
  return request({
    url: '/chemical/refresh/execute',
    method: 'post',
    params: data,
    timeout: 300000 // 5分钟超时
  })
}

// 停止正在运行的刷新任务
export function stopRefresh() {
  return request({
    url: '/chemical/refresh/stop',
    method: 'post'
  })
}

// 获取刷新状态
export function getRefreshStatus() {
  return request({
    url: '/chemical/refresh/status',
    method: 'get'
  })
}

// 获取最近的刷新任务
export function getRecentTasks(limit = 10) {
  return request({
    url: '/chemical/refresh/recent',
    method: 'get',
    params: { limit }
  })
}

// 导出刷新数据到CSV
export function exportCsv(data) {
  return request({
    url: '/chemical/refresh/exportCsv',
    method: 'post',
    params: data,
    timeout: 120000 // 2分钟超时
  })
}

// 清理历史刷新任务记录
export function cleanHistory(days) {
  return request({
    url: '/chemical/refresh/clean/' + days,
    method: 'delete'
  })
}

// 导出刷新任务列表
export function exportRefreshTask(query) {
  return request({
    url: '/chemical/refresh/export',
    method: 'post',
    data: query
  })
}
