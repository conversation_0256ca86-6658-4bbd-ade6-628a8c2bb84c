package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.TestResult;

/**
 * 测试结果Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ITestResultService 
{
    /**
     * 查询测试结果
     * 
     * @param testResultId 测试结果主键
     * @return 测试结果
     */
    public TestResult selectTestResultByTestResultId(Long testResultId);

    /**
     * 查询测试结果列表
     * 
     * @param testResult 测试结果
     * @return 测试结果集合
     */
    public List<TestResult> selectTestResultList(TestResult testResult);

    /**
     * 查询测试结果趋势对比数据
     * 
     * @param testResult 测试结果
     * @return 测试结果集合
     */
    public List<TestResult> selectTestResultTrendList(TestResult testResult);

    /**
     * 新增测试结果
     * 
     * @param testResult 测试结果
     * @return 结果
     */
    public int insertTestResult(TestResult testResult);

    /**
     * 修改测试结果
     * 
     * @param testResult 测试结果
     * @return 结果
     */
    public int updateTestResult(TestResult testResult);

    /**
     * 批量删除测试结果
     * 
     * @param testResultIds 需要删除的测试结果主键集合
     * @return 结果
     */
    public int deleteTestResultByTestResultIds(Long[] testResultIds);

    /**
    /**
     * 删除测试结果信息
     * 
     * @param testResultId 测试结果主键
     * @return 结果
     */
    public int deleteTestResultByTestResultId(Long testResultId);


    /**
     * 获取方案编号选项
     * 
     * @return 方案编号列表
     */
    public List<String> selectPlanCodeOptions();

    /**
     * 获取参数编号选项
     *
     * @return 参数编号列表
     */
    public List<String> selectParamNumberOptions();

    /**
     * 获取材料名称选项
     *
     * @return 材料名称列表
     */
    public List<String> selectMaterialNameOptions();

    /**
     * 获取供应商名称选项
     *
     * @return 供应商名称列表
     */
    public List<String> selectSupplierNameOptions();

    /**
     * 获取工艺类型选项
     *
     * @return 工艺类型列表
     */
    public List<String> selectProcessTypeOptions();

    /**
     * 获取测试方案组选项
     *
     * @return 测试方案组列表
     */
    public List<String> selectTestPlanGroupOptions();

    /**
     * 获取测试参数明细选项
     *
     * @return 测试参数明细列表
     */
    public List<String> selectTestParamItemOptions();

    /**
     * 根据测试方案组ID获取测试参数明细选项
     *
     * @param planGroupId 测试方案组ID
     * @return 测试参数明细列表
     */
    public List<String> selectTestParamItemOptionsByPlanGroupId(Long planGroupId);

    /**
     * 获取材料型号选项
     *
     * @return 材料型号列表
     */
    public List<String> selectMaterialModelOptions();

    /**
     * 获取性能类型选项
     *
     * @return 性能类型列表
     */
    public List<String> selectPerformanceTypeOptions();

    /**
     * 获取测试设备选项
     *
     * @return 测试设备列表
     */
    public List<String> selectTestEquipmentOptions();

    /**
     * 获取参数详情
     *
     * @param paramNumber 参数编号
     * @return 参数详情
     */
    public Map<String, Object> getParamDetail(String paramNumber);
}
