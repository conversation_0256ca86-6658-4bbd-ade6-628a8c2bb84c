package com.ruoyi.web.controller.material;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Material;
import com.ruoyi.system.service.IMaterialService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 材料信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/material/material")
public class MaterialController extends BaseController
{
    @Autowired
    private IMaterialService materialService;

    /**
     * 查询材料信息列表
     */
    @PreAuthorize("@ss.hasPermi('material:material:list')")
    @GetMapping("/list")
    public TableDataInfo list(Material material)
    {
        startPage();
        List<Material> list = materialService.selectMaterialList(material);
        return getDataTable(list);
    }

    /**
    /**
     * 导出材料信息列表
     */
    @PreAuthorize("@ss.hasPermi('material:material:export')")
    @Log(title = "材料信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Material material)
    {
        List<Material> list = materialService.selectMaterialList(material);
        ExcelUtil<Material> util = new ExcelUtil<Material>(Material.class);
        util.exportExcel(response, list, "材料信息数据");
    }


    /**
     * 获取材料信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:material:query')")
    @GetMapping(value = "/{materialId}")
    public AjaxResult getInfo(@PathVariable("materialId") Long materialId)
    {
        return AjaxResult.success(materialService.selectMaterialByMaterialId(materialId));
    }

    /**
     * 新增材料信息
     */
    @PreAuthorize("@ss.hasPermi('material:material:add')")
    @Log(title = "材料信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Material material)
    {
        return toAjax(materialService.insertMaterial(material));
    }

    /**
     * 修改材料信息
     */
    @PreAuthorize("@ss.hasPermi('material:material:edit')")
    @Log(title = "材料信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Material material)
    {
        return toAjax(materialService.updateMaterial(material));
    }

    /**
     * 删除材料信息
     */
    @PreAuthorize("@ss.hasPermi('material:material:remove')")
    @Log(title = "材料信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{materialIds}")
    public AjaxResult remove(@PathVariable Long[] materialIds)
    {
        return toAjax(materialService.deleteMaterialByMaterialIds(materialIds));
    }

    /**
     * 获取材料选项数据
     */
    @GetMapping("/options")
    public AjaxResult options(String type)
    {
        List<String> options = materialService.selectMaterialOptions(type);
        return AjaxResult.success(options);
    }

    /**
     * 整体导出材料数据（包含参数组和参数明细）
     */
    @PreAuthorize("@ss.hasPermi('material:material:export')")
    @Log(title = "材料信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportComplete")
    public void exportComplete(HttpServletResponse response, Material material)
    {
        materialService.exportCompleteData(response, material);
    }
}