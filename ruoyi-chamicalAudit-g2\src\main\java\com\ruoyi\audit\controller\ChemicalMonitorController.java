package com.ruoyi.audit.controller;

import java.util.Map;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.audit.domain.ChemicalTaskMonitor;
import com.ruoyi.audit.service.IChemicalTaskMonitorService;
import com.ruoyi.audit.service.IChemicalService;
import com.ruoyi.audit.service.IChemicalTaskService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 化学审计监控Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/chemical/monitor")
public class ChemicalMonitorController extends BaseController
{
    @Autowired
    private IChemicalTaskMonitorService taskMonitorService;
    
    @Autowired
    private IChemicalService chemicalService;
    
    @Autowired
    private IChemicalTaskService taskService;

    /**
     * 获取系统概览信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/overview")
    public AjaxResult getSystemOverview()
    {
        Map<String, Object> overview = Map.of(
            "dataStatistics", chemicalService.getDataStatistics(),
            "taskStatus", taskService.getTaskStatus(),
            "systemStatus", taskService.getSystemStatus(),
            "pulsarStatus", taskService.getPulsarConsumerStatus()
        );
        
        return success(overview);
    }

    /**
     * 获取实时数据统计
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/realtime")
    public AjaxResult getRealtimeData()
    {
        Map<String, Object> realtimeData = Map.of(
            "statistics", chemicalService.getDataStatistics(),
            "taskMonitor", taskService.getTaskMonitorInfo(),
            "systemStatus", taskService.getSystemStatus()
        );
        
        return success(realtimeData);
    }

    /**
     * 获取任务监控列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/tasks")
    public TableDataInfo getTaskMonitorList(ChemicalTaskMonitor taskMonitor)
    {
        startPage();
        List<ChemicalTaskMonitor> list = taskMonitorService.selectChemicalTaskMonitorList(taskMonitor);
        return getDataTable(list);
    }

    /**
     * 获取运行中的任务
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/runningTasks")
    public AjaxResult getRunningTasks()
    {
        List<ChemicalTaskMonitor> list = taskMonitorService.selectRunningTasks();
        return success(list);
    }

    /**
     * 获取任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/task/{taskId}")
    public AjaxResult getTaskDetail(@PathVariable Long taskId)
    {
        ChemicalTaskMonitor task = taskMonitorService.selectChemicalTaskMonitorByTaskId(taskId);
        return success(task);
    }

    /**
     * 获取最新任务记录
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/latestTask")
    public AjaxResult getLatestTask(@RequestParam String taskType)
    {
        ChemicalTaskMonitor task = taskMonitorService.selectLatestTaskByType(taskType);
        return success(task);
    }

    /**
     * 获取实时日志
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/logs")
    public AjaxResult getRealtimeLogs(@RequestParam(value = "lastLogId", defaultValue = "0") Long lastLogId)
    {
        Map<String, Object> result = taskService.getRealtimeLogs(lastLogId);
        return success(result);
    }

    /**
     * 获取系统性能指标
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/performance")
    public AjaxResult getPerformanceMetrics()
    {
        Map<String, Object> metrics = Map.of(
            "memoryUsage", getMemoryUsage(),
            "cpuUsage", getCpuUsage(),
            "diskUsage", getDiskUsage(),
            "networkStatus", getNetworkStatus()
        );
        
        return success(metrics);
    }

    /**
     * 获取数据处理趋势
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/trends")
    public AjaxResult getDataTrends(@RequestParam(value = "days", defaultValue = "7") int days)
    {
        Map<String, Object> trends = taskMonitorService.getDataTrends(days);
        return success(trends);
    }

    /**
     * 获取错误统计
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/errors")
    public AjaxResult getErrorStatistics(@RequestParam(value = "hours", defaultValue = "24") int hours)
    {
        Map<String, Object> errors = taskMonitorService.getErrorStatistics(hours);
        return success(errors);
    }

    /**
     * 获取告警信息
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:view')")
    @GetMapping("/alerts")
    public AjaxResult getAlerts()
    {
        List<Map<String, Object>> alerts = taskMonitorService.getSystemAlerts();
        return success(alerts);
    }

    /**
     * 清理历史任务记录
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:edit')")
    @Log(title = "化学审计监控", businessType = BusinessType.DELETE)
    @PostMapping("/cleanHistory")
    public AjaxResult cleanHistoryTasks(@RequestParam(value = "days", defaultValue = "30") int days)
    {
        int result = taskMonitorService.cleanHistoryTasks(days);
        return result > 0 ? success("清理完成，清理记录数：" + result) : success("无需清理");
    }

    /**
     * 导出监控报告
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:export')")
    @Log(title = "化学审计监控", businessType = BusinessType.EXPORT)
    @PostMapping("/exportReport")
    public AjaxResult exportMonitorReport(@RequestParam(value = "days", defaultValue = "7") int days)
    {
        Map<String, Object> result = taskMonitorService.exportMonitorReport(days);
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 重置任务状态
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:edit')")
    @Log(title = "化学审计监控", businessType = BusinessType.UPDATE)
    @PostMapping("/resetTask/{taskId}")
    public AjaxResult resetTaskStatus(@PathVariable Long taskId)
    {
        int result = taskMonitorService.resetTaskStatus(taskId);
        return toAjax(result);
    }

    /**
     * 删除任务记录
     */
    @PreAuthorize("@ss.hasPermi('chemical:monitor:remove')")
    @Log(title = "化学审计监控", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult removeTaskRecords(@PathVariable Long[] taskIds)
    {
        return toAjax(taskMonitorService.deleteChemicalTaskMonitorByTaskIds(taskIds));
    }

    // 私有方法：获取系统性能指标
    private Map<String, Object> getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        return Map.of(
            "total", totalMemory / 1024 / 1024, // MB
            "used", usedMemory / 1024 / 1024,   // MB
            "free", freeMemory / 1024 / 1024,   // MB
            "usage", (double) usedMemory / totalMemory * 100
        );
    }

    private Map<String, Object> getCpuUsage() {
        // 简化的CPU使用率获取
        return Map.of(
            "usage", 0.0, // 实际应用中需要使用JMX或其他方式获取
            "cores", Runtime.getRuntime().availableProcessors()
        );
    }

    private Map<String, Object> getDiskUsage() {
        // 简化的磁盘使用率获取
        return Map.of(
            "total", 0L,
            "used", 0L,
            "free", 0L,
            "usage", 0.0
        );
    }

    private Map<String, Object> getNetworkStatus() {
        // 简化的网络状态获取
        return Map.of(
            "connected", true,
            "latency", 0
        );
    }
}
