package com.ruoyi.audit.controller;

import java.util.Date;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.format.annotation.DateTimeFormat;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.audit.service.IChemicalExportService;
import com.ruoyi.common.core.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 化学数据导出Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/chemical/export")
public class ChemicalExportController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ChemicalExportController.class);

    @Autowired
    private IChemicalExportService chemicalExportService;

    /**
     * 导出CSV文件
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:csv')")
    @Log(title = "化学数据导出", businessType = BusinessType.EXPORT)
    @PostMapping("/csv")
    public AjaxResult exportToCSV(
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @RequestParam(value = "layerNumbers", required = false) String[] layerNumbers)
    {
        Map<String, Object> result = chemicalExportService.exportToCSV(startDate, endDate, layerNumbers);
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 导出Excel文件
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:excel')")
    @Log(title = "化学数据导出", businessType = BusinessType.EXPORT)
    @PostMapping("/excel")
    public AjaxResult exportToExcel(
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @RequestParam(value = "layerNumbers", required = false) String[] layerNumbers)
    {
        Map<String, Object> result = chemicalExportService.exportToExcel(startDate, endDate, layerNumbers);
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }

    /**
     * 获取导出历史记录
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:view')")
    @GetMapping("/history")
    public AjaxResult getExportHistory()
    {
        Map<String, Object> result = chemicalExportService.getExportHistory();
        return success(result);
    }

    /**
     * 下载导出文件
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:download')")
    @GetMapping("/download/{exportId}")
    public void downloadFile(@PathVariable Long exportId, HttpServletResponse response)
    {
        try {
            Map<String, Object> result = chemicalExportService.downloadExportFile(exportId);
            Boolean success = (Boolean) result.get("success");
            
            if (success) {
                String fileName = (String) result.get("fileName");
                String filePath = (String) result.get("filePath");

                // 设置响应头
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

                // TODO: 实现文件下载逻辑
                // Files.copy(Paths.get(filePath), response.getOutputStream());
                log.info("准备下载文件: {}", filePath);
            } else {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            logger.error("文件下载失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 删除导出记录
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:remove')")
    @Log(title = "化学数据导出", businessType = BusinessType.DELETE)
    @DeleteMapping("/{exportIds}")
    public AjaxResult deleteExportRecords(@PathVariable Long[] exportIds)
    {
        int result = chemicalExportService.deleteExportRecords(exportIds);
        return toAjax(result);
    }

    /**
     * 获取导出配置
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:view')")
    @GetMapping("/config")
    public AjaxResult getExportConfig()
    {
        Map<String, Object> result = chemicalExportService.getExportConfig();
        return success(result);
    }

    /**
     * 更新导出配置
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:edit')")
    @Log(title = "化学数据导出", businessType = BusinessType.UPDATE)
    @PostMapping("/config")
    public AjaxResult updateExportConfig(@RequestBody Map<String, Object> config)
    {
        int result = chemicalExportService.updateExportConfig(config);
        return toAjax(result);
    }

    /**
     * 预览导出数据
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:view')")
    @GetMapping("/preview")
    public TableDataInfo previewExportData(
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @RequestParam(value = "layerNumbers", required = false) String[] layerNumbers,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize)
    {
        Map<String, Object> result = chemicalExportService.previewExportData(startDate, endDate, layerNumbers, pageNum, pageSize);
        
        TableDataInfo dataInfo = new TableDataInfo();
        dataInfo.setCode(200);
        dataInfo.setMsg("查询成功");
        dataInfo.setRows((java.util.List<?>) result.get("rows"));
        dataInfo.setTotal((Long) result.get("total"));
        
        return dataInfo;
    }

    /**
     * 获取可选的层号列表
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:view')")
    @GetMapping("/layerNumbers")
    public AjaxResult getAvailableLayerNumbers()
    {
        Map<String, Object> result = chemicalExportService.getAvailableLayerNumbers();
        return success(result);
    }

    /**
     * 验证导出参数
     */
    @PreAuthorize("@ss.hasPermi('chemical:export:view')")
    @PostMapping("/validate")
    public AjaxResult validateExportParams(
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @RequestParam(value = "layerNumbers", required = false) String[] layerNumbers)
    {
        Map<String, Object> result = chemicalExportService.validateExportParams(startDate, endDate, layerNumbers);
        Boolean success = (Boolean) result.get("success");
        String message = (String) result.get("message");
        
        return success ? AjaxResult.success(message).put("data", result) : error(message);
    }
}
