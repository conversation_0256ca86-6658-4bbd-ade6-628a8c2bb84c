package com.ruoyi.web.controller.material;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.TestParamItem;
import com.ruoyi.system.service.ITestParamItemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 测试参数明细Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/material/testParamItem")
public class TestParamItemController extends BaseController
{
    @Autowired
    private ITestParamItemService testParamItemService;

    /**
     * 查询测试参数明细列表
     */
    @PreAuthorize("@ss.hasPermi('material:testParamItem:list')")
    @GetMapping("/list")
    public TableDataInfo list(TestParamItem testParamItem)
    {
        startPage();
        List<TestParamItem> list = testParamItemService.selectTestParamItemList(testParamItem);
        return getDataTable(list);
    }

    /**
     * 根据测试方案组ID查询测试参数明细列表
     */
    @GetMapping("/listByPlanGroupId/{planGroupId}")
    public AjaxResult listByPlanGroupId(@PathVariable("planGroupId") Long planGroupId)
    {
        List<TestParamItem> list = testParamItemService.selectTestParamItemByPlanGroupId(planGroupId);
        return AjaxResult.success(list);
    }

    /**
     * 导出测试参数明细列表
     */
    @PreAuthorize("@ss.hasPermi('material:testParamItem:export')")
    @Log(title = "测试参数明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestParamItem testParamItem)
    {
        List<TestParamItem> list = testParamItemService.selectTestParamItemList(testParamItem);
        ExcelUtil<TestParamItem> util = new ExcelUtil<TestParamItem>(TestParamItem.class);
        util.exportExcel(response, list, "测试参数明细数据");
    }

    /**
     * 获取测试参数明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:testParamItem:query')")
    @GetMapping(value = "/{testParamId}")
    public AjaxResult getInfo(@PathVariable("testParamId") Long testParamId)
    {
        return AjaxResult.success(testParamItemService.selectTestParamItemByTestParamId(testParamId));
    }

    /**
     * 新增测试参数明细
     */
    @PreAuthorize("@ss.hasPermi('material:testParamItem:add')")
    @Log(title = "测试参数明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestParamItem testParamItem)
    {
        return toAjax(testParamItemService.insertTestParamItem(testParamItem));
    }

    /**
     * 修改测试参数明细
     */
    @PreAuthorize("@ss.hasPermi('material:testParamItem:edit')")
    @Log(title = "测试参数明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestParamItem testParamItem)
    {
        return toAjax(testParamItemService.updateTestParamItem(testParamItem));
    }

    /**
     * 删除测试参数明细
     */
    @PreAuthorize("@ss.hasPermi('material:testParamItem:remove')")
    @Log(title = "测试参数明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{testParamIds}")
    public AjaxResult remove(@PathVariable Long[] testParamIds)
    {
        return toAjax(testParamItemService.deleteTestParamItemByTestParamIds(testParamIds));
    }

    /**
     * 获取测试参数明细选项数据
     */
    @GetMapping("/options")
    public AjaxResult getOptions(@RequestParam(required = false) String type)
    {
        List<String> options = testParamItemService.selectTestParamItemOptions(type);
        return AjaxResult.success(options);
    }
}
