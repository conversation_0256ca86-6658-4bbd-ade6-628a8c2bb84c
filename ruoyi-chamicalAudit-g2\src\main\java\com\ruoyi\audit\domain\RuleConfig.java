package com.ruoyi.audit.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 数据刷新规则配置对象 rule_config
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class RuleConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 过程名称 */
    @Excel(name = "过程名称")
    private String processName;

    /** 测试名称 */
    @Excel(name = "测试名称")
    private String testName;

    /** 是否启用控制线内调整 */
    @Excel(name = "控制线内调整")
    private Boolean enableControlLimitAdjustment;

    /** 是否启用移动极差调整 */
    @Excel(name = "移动极差调整")
    private Boolean enableMovingRangeAdjustment;

    /** 是否启用9点同侧检查 */
    @Excel(name = "9点同侧检查")
    private Boolean enableNinePointSameSideCheck;

    /** 是否启用6点递变检查 */
    @Excel(name = "6点递变检查")
    private Boolean enableSixPointTrendCheck;

    /** 是否启用CPK调整 */
    @Excel(name = "CPK调整")
    private Boolean enableCpkAdjustment;

    /** CPK目标值 */
    @Excel(name = "CPK目标值")
    private Double cpkTarget;

    /** 是否为刷新模式 */
    @Excel(name = "刷新模式")
    private Boolean isRefreshMode;

    /** 规则描述 */
    @Excel(name = "规则描述")
    private String ruleDescription;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }

    public void setProcessName(String processName) 
    {
        this.processName = processName;
    }

    public String getProcessName() 
    {
        return processName;
    }

    public void setTestName(String testName) 
    {
        this.testName = testName;
    }

    public String getTestName() 
    {
        return testName;
    }

    public void setEnableControlLimitAdjustment(Boolean enableControlLimitAdjustment) 
    {
        this.enableControlLimitAdjustment = enableControlLimitAdjustment;
    }

    public Boolean getEnableControlLimitAdjustment() 
    {
        return enableControlLimitAdjustment;
    }

    public void setEnableMovingRangeAdjustment(Boolean enableMovingRangeAdjustment) 
    {
        this.enableMovingRangeAdjustment = enableMovingRangeAdjustment;
    }

    public Boolean getEnableMovingRangeAdjustment() 
    {
        return enableMovingRangeAdjustment;
    }

    public void setEnableNinePointSameSideCheck(Boolean enableNinePointSameSideCheck) 
    {
        this.enableNinePointSameSideCheck = enableNinePointSameSideCheck;
    }

    public Boolean getEnableNinePointSameSideCheck() 
    {
        return enableNinePointSameSideCheck;
    }

    public void setEnableSixPointTrendCheck(Boolean enableSixPointTrendCheck) 
    {
        this.enableSixPointTrendCheck = enableSixPointTrendCheck;
    }

    public Boolean getEnableSixPointTrendCheck() 
    {
        return enableSixPointTrendCheck;
    }

    public void setEnableCpkAdjustment(Boolean enableCpkAdjustment) 
    {
        this.enableCpkAdjustment = enableCpkAdjustment;
    }

    public Boolean getEnableCpkAdjustment() 
    {
        return enableCpkAdjustment;
    }

    public void setCpkTarget(Double cpkTarget) 
    {
        this.cpkTarget = cpkTarget;
    }

    public Double getCpkTarget() 
    {
        return cpkTarget;
    }

    public void setIsRefreshMode(Boolean isRefreshMode) 
    {
        this.isRefreshMode = isRefreshMode;
    }

    public Boolean getIsRefreshMode() 
    {
        return isRefreshMode;
    }

    public void setRuleDescription(String ruleDescription) 
    {
        this.ruleDescription = ruleDescription;
    }

    public String getRuleDescription() 
    {
        return ruleDescription;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setCreatedBy(String createdBy) 
    {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() 
    {
        return createdBy;
    }

    public void setUpdatedBy(String updatedBy) 
    {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() 
    {
        return updatedBy;
    }

    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }

    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productName", getProductName())
            .append("processName", getProcessName())
            .append("testName", getTestName())
            .append("enableControlLimitAdjustment", getEnableControlLimitAdjustment())
            .append("enableMovingRangeAdjustment", getEnableMovingRangeAdjustment())
            .append("enableNinePointSameSideCheck", getEnableNinePointSameSideCheck())
            .append("enableSixPointTrendCheck", getEnableSixPointTrendCheck())
            .append("enableCpkAdjustment", getEnableCpkAdjustment())
            .append("cpkTarget", getCpkTarget())
            .append("isRefreshMode", getIsRefreshMode())
            .append("ruleDescription", getRuleDescription())
            .append("status", getStatus())
            .append("createdBy", getCreatedBy())
            .append("updatedBy", getUpdatedBy())
            .append("createdTime", getCreatedTime())
            .append("updatedTime", getUpdatedTime())
            .toString();
    }
}
