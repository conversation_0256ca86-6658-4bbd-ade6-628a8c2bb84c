import request from '@/utils/request'

// 查询测试结果列表
export function listTestResult(query) {
  return request({
    url: '/material/testResult/list',
    method: 'get',
    params: query
  })
}

// 查询测试结果详细
export function getTestResult(testResultId) {
  return request({
    url: '/material/testResult/' + testResultId,
    method: 'get'
  })
}

// 新增测试结果
export function addTestResult(data) {
  return request({
    url: '/material/testResult',
    method: 'post',
    data: data
  })
}

// 修改测试结果
export function updateTestResult(data) {
  return request({
    url: '/material/testResult',
    method: 'put',
    data: data
  })
}

// 删除测试结果
export function delTestResult(testResultId) {
  return request({
    url: '/material/testResult/' + testResultId,
    method: 'delete'
  })
}

// 导出测试结果
export function exportTestResult(query) {
  return request({
    url: '/material/testResult/export',
    method: 'get',
    params: query
  })
}

// 获取测试结果选项（用于下拉选择和搜索建议）
export function getTestResultOptions(query) {
  return request({
    url: '/material/testResult/options',
    method: 'get',
    params: query
  })
}

// 根据测试方案组ID获取测试参数明细选项
export function getTestParamOptionsByPlanGroupId(planGroupId) {
  return request({
    url: '/material/testResult/testParamOptions/' + planGroupId,
    method: 'get'
  })
}

// 获取趋势数据
export function getTrendList(query) {
  return request({
    url: '/material/testResult/trend',
    method: 'get',
    params: query
  })
}

// 上传附件
export function uploadResultAttachment(data) {
  return request({
    url: '/material/testResult/upload',
    method: 'post',
    data: data
  })
}

// 下载附件
export function downloadResultAttachment(fileName) {
  return request({
    url: '/material/testResult/download/' + fileName,
    method: 'get',
    responseType: 'blob'
  })
}