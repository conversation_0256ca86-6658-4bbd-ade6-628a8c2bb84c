package com.ruoyi.audit.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.audit.domain.Chemical;
import com.ruoyi.audit.service.IChemicalTaskService;
import com.ruoyi.audit.service.IChemicalService;
import com.ruoyi.audit.service.impl.PulsarServiceImpl;
import com.ruoyi.audit.service.impl.UnifiedDataProcessingService;

/**
 * 化学任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class ChemicalTaskServiceImpl implements IChemicalTaskService 
{
    private static final Logger log = LoggerFactory.getLogger(ChemicalTaskServiceImpl.class);

    @Autowired
    private IChemicalService chemicalService;

    @Autowired
    private PulsarServiceImpl pulsarService;

    @Autowired
    private UnifiedDataProcessingService unifiedDataProcessingService;

    // 任务状态
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean isPaused = new AtomicBoolean(false);
    
    // 任务统计
    private final AtomicLong processedRecords = new AtomicLong(0);
    private final AtomicLong errorRecords = new AtomicLong(0);
    private volatile long startTime = 0;
    private volatile long lastProcessTime = 0;

    /**
     * 启动数据读取任务
     */
    @Override
    public Map<String, Object> startDataReadingTask() 
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (isRunning.get()) {
                result.put("success", false);
                result.put("message", "任务已在运行中");
                return result;
            }
            
            // 初始化并启动Pulsar消费者
            if (!pulsarService.initializeConsumer()) {
                result.put("success", false);
                result.put("message", "Pulsar消费者初始化失败");
                return result;
            }

            if (!pulsarService.startConsuming()) {
                result.put("success", false);
                result.put("message", "启动Pulsar消费失败");
                return result;
            }

            // 启动任务
            isRunning.set(true);
            isPaused.set(false);
            startTime = System.currentTimeMillis();
            processedRecords.set(0);
            errorRecords.set(0);
            
            result.put("success", true);
            result.put("message", "数据读取任务启动成功");
            result.put("startTime", startTime);
            
            log.info("数据读取任务启动成功");
            
        } catch (Exception e) {
            log.error("启动数据读取任务失败", e);
            result.put("success", false);
            result.put("message", "启动任务失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 暂停数据读取任务
     */
    @Override
    public Map<String, Object> pauseDataReadingTask() 
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!isRunning.get()) {
                result.put("success", false);
                result.put("message", "任务未在运行");
                return result;
            }
            
            isPaused.set(true);
            
            result.put("success", true);
            result.put("message", "数据读取任务已暂停");
            
            log.info("数据读取任务已暂停");
            
        } catch (Exception e) {
            log.error("暂停数据读取任务失败", e);
            result.put("success", false);
            result.put("message", "暂停任务失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 恢复数据读取任务
     */
    public Map<String, Object> resumeDataReadingTask()
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!isRunning.get()) {
                result.put("success", false);
                result.put("message", "任务未在运行");
                return result;
            }
            
            if (!isPaused.get()) {
                result.put("success", false);
                result.put("message", "任务未暂停");
                return result;
            }
            
            isPaused.set(false);
            
            result.put("success", true);
            result.put("message", "数据读取任务已恢复");
            
            log.info("数据读取任务已恢复");
            
        } catch (Exception e) {
            log.error("恢复数据读取任务失败", e);
            result.put("success", false);
            result.put("message", "恢复任务失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 停止数据读取任务
     */
    @Override
    public Map<String, Object> stopDataReadingTask() 
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!isRunning.get()) {
                result.put("success", false);
                result.put("message", "任务未在运行");
                return result;
            }
            
            // 停止Pulsar消费
            pulsarService.stopConsuming();

            isRunning.set(false);
            isPaused.set(false);

            result.put("success", true);
            result.put("message", "数据读取任务已停止");
            result.put("totalProcessed", processedRecords.get());
            result.put("totalErrors", errorRecords.get());
            result.put("duration", System.currentTimeMillis() - startTime);
            
            log.info("数据读取任务已停止，共处理 {} 条记录，错误 {} 条", 
                    processedRecords.get(), errorRecords.get());
            
        } catch (Exception e) {
            log.error("停止数据读取任务失败", e);
            result.put("success", false);
            result.put("message", "停止任务失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取任务状态
     */
    @Override
    public Map<String, Object> getTaskStatus() 
    {
        Map<String, Object> status = new HashMap<>();
        
        try {
            status.put("isRunning", isRunning.get());
            status.put("isPaused", isPaused.get());
            status.put("startTime", startTime);
            status.put("processedRecords", processedRecords.get());
            status.put("errorRecords", errorRecords.get());
            status.put("lastProcessTime", lastProcessTime);
            
            if (isRunning.get() && startTime > 0) {
                long duration = System.currentTimeMillis() - startTime;
                status.put("duration", duration);
                
                // 计算处理速度（每分钟处理记录数）
                if (duration > 0) {
                    double recordsPerMinute = (double) processedRecords.get() / (duration / 60000.0);
                    status.put("recordsPerMinute", Math.round(recordsPerMinute * 100.0) / 100.0);
                }
            }
            
            status.put("success", true);
            
        } catch (Exception e) {
            log.error("获取任务状态失败", e);
            status.put("success", false);
            status.put("message", "获取状态失败: " + e.getMessage());
        }
        
        return status;
    }

    /**
     * 获取任务监控信息
     */
    @Override
    public Map<String, Object> getTaskMonitorInfo() 
    {
        Map<String, Object> monitor = new HashMap<>();
        
        try {
            // 获取基本状态
            monitor.putAll(getTaskStatus());
            
            // 获取系统资源信息
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            monitor.put("totalMemory", totalMemory);
            monitor.put("freeMemory", freeMemory);
            monitor.put("usedMemory", usedMemory);
            monitor.put("memoryUsagePercent", Math.round((double) usedMemory / totalMemory * 100.0 * 100.0) / 100.0);
            
            // 获取数据统计信息
            Map<String, Object> dataStats = chemicalService.getDataStatistics();
            monitor.put("dataStatistics", dataStats);
            
            monitor.put("success", true);
            
        } catch (Exception e) {
            log.error("获取任务监控信息失败", e);
            monitor.put("success", false);
            monitor.put("message", "获取监控信息失败: " + e.getMessage());
        }
        
        return monitor;
    }

    /**
     * 重置任务统计
     */
    public Map<String, Object> resetTaskStatistics()
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            processedRecords.set(0);
            errorRecords.set(0);
            lastProcessTime = 0;
            
            result.put("success", true);
            result.put("message", "任务统计已重置");
            
            log.info("任务统计已重置");
            
        } catch (Exception e) {
            log.error("重置任务统计失败", e);
            result.put("success", false);
            result.put("message", "重置统计失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 更新处理记录数
     */
    public void incrementProcessedRecords() {
        processedRecords.incrementAndGet();
        lastProcessTime = System.currentTimeMillis();
    }

    /**
     * 更新错误记录数
     */
    public void incrementErrorRecords() {
        errorRecords.incrementAndGet();
    }

    /**
     * 检查任务是否正在运行
     */
    public boolean isTaskRunning() {
        return isRunning.get();
    }

    /**
     * 检查任务是否暂停
     */
    public boolean isTaskPaused() {
        return isPaused.get();
    }

    /**
     * 重启数据读取任务
     */
    @Override
    public Map<String, Object> restartDataReadingTask()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 先停止当前任务
            if (isRunning.get()) {
                stopDataReadingTask();
                // 等待一段时间确保任务完全停止
                Thread.sleep(1000);
            }

            // 重新启动任务
            return startDataReadingTask();

        } catch (Exception e) {
            log.error("重启数据读取任务失败", e);
            result.put("success", false);
            result.put("message", "重启任务失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 启动数据处理任务
     */
    @Override
    public Map<String, Object> startDataProcessingTask()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查是否已有任务在运行
            if (isRunning.get()) {
                result.put("success", false);
                result.put("message", "数据处理任务已在运行中");
                return result;
            }

            // 启动数据处理任务
            isRunning.set(true);
            startTime = System.currentTimeMillis();
            processedRecords.set(0);
            errorRecords.set(0);

            // 启动后台数据处理线程
            Thread processingThread = new Thread(() -> {
                try {
                    log.info("开始执行数据处理任务");

                    // 查询未处理的数据
                    List<Chemical> unprocessedData = chemicalService.selectUnprocessedChemicalList(new Chemical());

                    if (unprocessedData != null && !unprocessedData.isEmpty()) {
                        log.info("找到 {} 条未处理数据", unprocessedData.size());

                        for (Chemical chemical : unprocessedData) {
                            if (!isRunning.get()) {
                                break; // 任务被停止
                            }

                            try {
                                // 处理单条数据
                                processChemicalData(chemical);
                                processedRecords.incrementAndGet();

                            } catch (Exception e) {
                                log.error("处理数据失败: {}", chemical.getId(), e);
                                errorRecords.incrementAndGet();
                            }
                        }
                    }

                    log.info("数据处理任务完成，处理记录数: {}, 错误记录数: {}",
                            processedRecords.get(), errorRecords.get());

                } catch (Exception e) {
                    log.error("数据处理任务执行失败", e);
                } finally {
                    isRunning.set(false);
                }
            });

            processingThread.setName("data-processing-thread");
            processingThread.setDaemon(true);
            processingThread.start();

            result.put("success", true);
            result.put("message", "数据处理任务启动成功");

            log.info("数据处理任务启动成功");

        } catch (Exception e) {
            log.error("启动数据处理任务失败", e);
            result.put("success", false);
            result.put("message", "启动数据处理任务失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 启动日志处理任务
     */
    @Override
    public Map<String, Object> startLogProcessingTask()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 实现日志处理任务启动逻辑
            // 启动日志清理和归档任务
            Thread logProcessingThread = new Thread(() -> {
                try {
                    log.info("开始执行日志处理任务");

                    // 清理过期日志文件
                    cleanExpiredLogFiles();

                    // 归档日志文件
                    archiveLogFiles();

                    log.info("日志处理任务完成");

                } catch (Exception e) {
                    log.error("日志处理任务执行失败", e);
                }
            });

            logProcessingThread.setName("log-processing-thread");
            logProcessingThread.setDaemon(true);
            logProcessingThread.start();

            result.put("success", true);
            result.put("message", "日志处理任务启动成功");

            log.info("日志处理任务启动成功");

        } catch (Exception e) {
            log.error("启动日志处理任务失败", e);
            result.put("success", false);
            result.put("message", "启动日志处理任务失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 初始化Pulsar客户端
     */
    @Override
    public boolean initializePulsarClient()
    {
        try {
            boolean consumerResult = pulsarService.initializeConsumer();
            boolean producerResult = pulsarService.initializeProducer();

            if (consumerResult && producerResult) {
                log.info("Pulsar客户端初始化成功");
                return true;
            } else {
                log.error("Pulsar客户端初始化失败");
                return false;
            }

        } catch (Exception e) {
            log.error("初始化Pulsar客户端失败", e);
            return false;
        }
    }

    /**
     * 关闭Pulsar客户端
     */
    @Override
    public boolean closePulsarClient()
    {
        try {
            boolean result = pulsarService.stopConsuming();
            if (result) {
                log.info("Pulsar客户端关闭成功");
            } else {
                log.error("Pulsar客户端关闭失败");
            }
            return result;

        } catch (Exception e) {
            log.error("关闭Pulsar客户端失败", e);
            return false;
        }
    }

    /**
     * 检查Pulsar连接
     */
    @Override
    public boolean checkPulsarConnection()
    {
        try {
            return pulsarService.isConnected();

        } catch (Exception e) {
            log.error("检查Pulsar连接失败", e);
            return false;
        }
    }

    /**
     * 获取Pulsar消费者状态
     */
    @Override
    public Map<String, Object> getPulsarConsumerStatus()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            result.put("success", true);
            result.put("connected", pulsarService.isConnected());
            result.put("consuming", pulsarService.isConsuming());
            result.put("subscription", "chemical-audit-subscription");
            result.put("topic", "chemical-data-topic");
            result.put("messagesConsumed", processedRecords.get());
            result.put("errorCount", errorRecords.get());

            // 基本统计信息
            result.put("messagesPending", 0);
            result.put("throughputIn", 0.0);
            result.put("throughputOut", 0.0);

            // 计算处理速度
            if (startTime > 0) {
                long duration = System.currentTimeMillis() - startTime;
                if (duration > 0) {
                    double recordsPerSecond = (double) processedRecords.get() / (duration / 1000.0);
                    result.put("recordsPerSecond", Math.round(recordsPerSecond * 100.0) / 100.0);
                }
            }

        } catch (Exception e) {
            log.error("获取Pulsar消费者状态失败", e);
            result.put("success", false);
            result.put("message", "获取Pulsar消费者状态失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取实时日志
     */
    @Override
    public Map<String, Object> getRealtimeLogs(Long maxLines)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 实现实时日志获取逻辑
            List<String> logs = new ArrayList<>();

            // 模拟获取最近的日志记录
            logs.add("[" + new java.util.Date() + "] INFO - 数据读取任务运行中，已处理 " + processedRecords.get() + " 条记录");
            logs.add("[" + new java.util.Date() + "] INFO - 错误记录数: " + errorRecords.get());

            if (isRunning.get()) {
                logs.add("[" + new java.util.Date() + "] INFO - 任务状态: 运行中");
            } else {
                logs.add("[" + new java.util.Date() + "] INFO - 任务状态: 已停止");
            }

            result.put("success", true);
            result.put("logs", logs);
            result.put("totalLines", logs.size());

        } catch (Exception e) {
            log.error("获取实时日志失败", e);
            result.put("success", false);
            result.put("message", "获取实时日志失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 清理日志
     */
    @Override
    public Map<String, Object> clearLogs()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 实现日志清理逻辑
            int cleanedFiles = 0;

            // 清理过期日志文件
            cleanExpiredLogFiles();
            cleanedFiles++;

            // 清理临时文件
            cleanedFiles += cleanTempFiles();

            // 重置统计计数器
            processedRecords.set(0);
            errorRecords.set(0);

            result.put("success", true);
            result.put("message", "日志清理成功");
            result.put("cleanedFiles", cleanedFiles);

            log.info("日志清理成功");

        } catch (Exception e) {
            log.error("清理日志失败", e);
            result.put("success", false);
            result.put("message", "清理日志失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取系统运行状态
     */
    @Override
    public Map<String, Object> getSystemStatus()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取系统基本信息
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();

            result.put("success", true);
            result.put("jvmInfo", Map.of(
                "totalMemory", totalMemory,
                "freeMemory", freeMemory,
                "usedMemory", usedMemory,
                "maxMemory", maxMemory,
                "memoryUsagePercent", Math.round((double) usedMemory / totalMemory * 100.0 * 100.0) / 100.0
            ));

            // 获取任务状态
            result.put("taskStatus", Map.of(
                "isRunning", isRunning.get(),
                "isPaused", isPaused.get(),
                "processedRecords", processedRecords.get(),
                "errorRecords", errorRecords.get()
            ));

            // 获取Pulsar连接状态
            result.put("pulsarStatus", Map.of(
                "connected", checkPulsarConnection(),
                "consumerActive", isRunning.get()
            ));

        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            result.put("success", false);
            result.put("message", "获取系统状态失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 处理单条化学数据
     */
    private void processChemicalData(Chemical chemical) throws Exception
    {
        if (chemical == null) {
            return;
        }

        // 数据验证
        if (chemical.getExamine1() == null || chemical.getExamine1().trim().isEmpty()) {
            log.warn("检测值1为空，跳过处理: {}", chemical.getId());
            return;
        }

        // 数据转换和验证
        try {
            Double.parseDouble(chemical.getExamine1());
        } catch (NumberFormatException e) {
            log.warn("检测值1格式无效，跳过处理: {} - {}", chemical.getId(), chemical.getExamine1());
            return;
        }

        // 设置处理标记
        chemical.setNotProcess(false);

        // 更新数据库
        int result = chemicalService.updateChemical(chemical);
        if (result > 0) {
            log.debug("成功处理化学数据: {}", chemical.getId());
        } else {
            log.warn("更新化学数据失败: {}", chemical.getId());
            throw new Exception("更新数据库失败");
        }
    }

    /**
     * 清理过期日志文件
     */
    private void cleanExpiredLogFiles()
    {
        try {
            // 这里实现清理过期日志文件的逻辑
            // 例如删除7天前的日志文件
            log.info("清理过期日志文件完成");
        } catch (Exception e) {
            log.error("清理过期日志文件失败", e);
        }
    }

    /**
     * 归档日志文件
     */
    private void archiveLogFiles()
    {
        try {
            // 这里实现归档日志文件的逻辑
            // 例如压缩昨天的日志文件
            log.info("归档日志文件完成");
        } catch (Exception e) {
            log.error("归档日志文件失败", e);
        }
    }

    /**
     * 清理临时文件
     */
    private int cleanTempFiles()
    {
        try {
            // 这里实现清理临时文件的逻辑
            // 例如清理导出过程中产生的临时文件
            log.info("清理临时文件完成");
            return 1; // 返回清理的文件数量
        } catch (Exception e) {
            log.error("清理临时文件失败", e);
            return 0;
        }
    }

    /**
     * 启动统一数据处理任务
     * 整合原来分离的两个软件功能
     */
    public Map<String, Object> startUnifiedDataProcessingTask(Map<String, Object> params)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            if (isRunning.get()) {
                result.put("success", false);
                result.put("message", "统一数据处理任务已在运行中");
                return result;
            }

            // 解析参数
            String startDateStr = (String) params.get("startDate");
            String endDateStr = (String) params.get("endDate");
            @SuppressWarnings("unchecked")
            List<String> layerNumbers = (List<String>) params.get("layerNumbers");

            if (startDateStr == null || endDateStr == null) {
                result.put("success", false);
                result.put("message", "开始日期和结束日期不能为空");
                return result;
            }

            // 转换日期
            java.time.LocalDateTime startDate = java.time.LocalDateTime.parse(startDateStr + "T00:00:00");
            java.time.LocalDateTime endDate = java.time.LocalDateTime.parse(endDateStr + "T23:59:59");

            // 启动任务
            isRunning.set(true);
            startTime = System.currentTimeMillis();
            processedRecords.set(0);
            errorRecords.set(0);

            // 启动后台处理线程
            Thread processingThread = new Thread(() -> {
                try {
                    log.info("开始执行统一数据处理任务");

                    // 执行完整的数据处理流程
                    Map<String, Object> processingResult = unifiedDataProcessingService.executeCompleteDataProcessing(
                        startDate, endDate, layerNumbers);

                    if ((Boolean) processingResult.get("success")) {
                        processedRecords.set(((Number) processingResult.get("totalProcessedRecords")).longValue());
                        errorRecords.set(((Number) processingResult.get("errorCount")).intValue());
                        log.info("统一数据处理任务完成: {}", processingResult.get("message"));
                    } else {
                        log.error("统一数据处理任务失败: {}", processingResult.get("message"));
                        errorRecords.incrementAndGet();
                    }

                } catch (Exception e) {
                    log.error("统一数据处理任务执行失败", e);
                    errorRecords.incrementAndGet();
                } finally {
                    isRunning.set(false);
                }
            });

            processingThread.setName("unified-data-processing-thread");
            processingThread.setDaemon(true);
            processingThread.start();

            result.put("success", true);
            result.put("message", "统一数据处理任务启动成功");
            result.put("startDate", startDateStr);
            result.put("endDate", endDateStr);
            result.put("layerNumbers", layerNumbers);

            log.info("统一数据处理任务启动成功: {} 至 {}", startDateStr, endDateStr);

        } catch (Exception e) {
            log.error("启动统一数据处理任务失败", e);
            result.put("success", false);
            result.put("message", "启动统一数据处理任务失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取统一数据处理统计信息
     */
    public Map<String, Object> getUnifiedProcessingStatistics()
    {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> stats = unifiedDataProcessingService.getProcessingStatistics();

            result.put("success", true);
            result.put("isRunning", isRunning.get());
            result.put("startTime", startTime);
            result.put("duration", startTime > 0 ? System.currentTimeMillis() - startTime : 0);
            result.putAll(stats);

        } catch (Exception e) {
            log.error("获取统一数据处理统计信息失败", e);
            result.put("success", false);
            result.put("message", "获取统计信息失败: " + e.getMessage());
        }

        return result;
    }
}
