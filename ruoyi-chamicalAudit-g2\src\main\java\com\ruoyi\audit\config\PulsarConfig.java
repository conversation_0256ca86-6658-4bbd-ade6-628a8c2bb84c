package com.ruoyi.audit.config;

import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Pulsar配置类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Configuration
public class PulsarConfig 
{
    private static final Logger log = LoggerFactory.getLogger(PulsarConfig.class);

    @Value("${chemical.pulsar.service.url:pulsar://localhost:6650}")
    private String pulsarServiceUrl;

    @Value("${chemical.pulsar.consumer.subscription:chemical-audit-subscription}")
    private String subscription;

    @Value("${chemical.pulsar.consumer.topic:chemical-data-topic}")
    private String topic;

    @Value("${chemical.pulsar.consumer.receiverQueueSize:1000}")
    private int receiverQueueSize;

    @Value("${chemical.pulsar.consumer.maxTotalReceiverQueueSizeAcrossPartitions:50000}")
    private int maxTotalReceiverQueueSizeAcrossPartitions;

    @Value("${chemical.pulsar.producer.sendTimeoutMs:30000}")
    private int sendTimeoutMs;

    @Value("${chemical.pulsar.producer.blockIfQueueFull:true}")
    private boolean blockIfQueueFull;

    /**
     * 创建Pulsar客户端
     */
    @Bean
    public PulsarClient pulsarClient() throws PulsarClientException 
    {
        try {
            PulsarClient client = PulsarClient.builder()
                    .serviceUrl(pulsarServiceUrl)
                    .build();
            
            log.info("Pulsar客户端初始化成功，服务地址: {}", pulsarServiceUrl);
            return client;
            
        } catch (PulsarClientException e) {
            log.error("Pulsar客户端初始化失败", e);
            throw e;
        }
    }

    // Getter methods for configuration values
    public String getPulsarServiceUrl() {
        return pulsarServiceUrl;
    }

    public String getSubscription() {
        return subscription;
    }

    public String getTopic() {
        return topic;
    }

    public int getReceiverQueueSize() {
        return receiverQueueSize;
    }

    public int getMaxTotalReceiverQueueSizeAcrossPartitions() {
        return maxTotalReceiverQueueSizeAcrossPartitions;
    }

    public int getSendTimeoutMs() {
        return sendTimeoutMs;
    }

    public boolean isBlockIfQueueFull() {
        return blockIfQueueFull;
    }
}
