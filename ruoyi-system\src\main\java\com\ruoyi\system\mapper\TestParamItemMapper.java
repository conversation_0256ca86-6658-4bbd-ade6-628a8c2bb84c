package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.TestParamItem;

/**
 * 测试参数明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface TestParamItemMapper 
{
    /**
     * 查询测试参数明细
     * 
     * @param testParamId 测试参数明细主键
     * @return 测试参数明细
     */
    public TestParamItem selectTestParamItemByTestParamId(Long testParamId);

    /**
     * 查询测试参数明细列表
     * 
     * @param testParamItem 测试参数明细
     * @return 测试参数明细集合
     */
    public List<TestParamItem> selectTestParamItemList(TestParamItem testParamItem);

    /**
     * 根据测试方案组ID查询测试参数明细列表
     * 
     * @param planGroupId 测试方案组ID
     * @return 测试参数明细集合
     */
    public List<TestParamItem> selectTestParamItemByPlanGroupId(Long planGroupId);

    /**
     * 新增测试参数明细
     * 
     * @param testParamItem 测试参数明细
     * @return 结果
     */
    public int insertTestParamItem(TestParamItem testParamItem);

    /**
     * 修改测试参数明细
     * 
     * @param testParamItem 测试参数明细
     * @return 结果
     */
    public int updateTestParamItem(TestParamItem testParamItem);

    /**
     * 删除测试参数明细
     * 
     * @param testParamId 测试参数明细主键
     * @return 结果
     */
    public int deleteTestParamItemByTestParamId(Long testParamId);

    /**
     * 批量删除测试参数明细
     * 
     * @param testParamIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTestParamItemByTestParamIds(Long[] testParamIds);

    /**
     * 根据测试方案组ID删除测试参数明细
     * 
     * @param planGroupId 测试方案组ID
     * @return 结果
     */
    public int deleteTestParamItemByPlanGroupId(Long planGroupId);

    /**
     * 获取参数名称选项
     *
     * @return 参数名称列表
     */
    public List<String> selectParamNameOptions();

    /**
     * 获取参数单位选项
     *
     * @return 参数单位列表
     */
    public List<String> selectUnitOptions();
}
